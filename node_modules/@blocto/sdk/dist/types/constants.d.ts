export declare enum KEY_SESSION {
    prod = "BLOCTO_SDK",
    dev = "BLOCTO_SDK_DEV",
    staging = "BLOCTO_SDK_STAGING"
}
export declare enum CHAIN {
    ETHEREUM = "ethereum",
    APTOS = "aptos"
}
export interface BaseConfig {
    appId?: string;
}
type Mapping = Record<number | string, string>;
export declare const ETH_RPC_LIST: Mapping;
export declare const ETH_ENV_WALLET_SERVER_MAPPING: Mapping;
export declare const ETH_SESSION_KEY_MAPPING: Record<string, KEY_SESSION>;
export declare const APT_SESSION_KEY_MAPPING: Record<number | string, KEY_SESSION>;
export declare const APT_CHAIN_ID_SERVER_MAPPING: Mapping;
export declare enum WalletAdapterNetwork {
    Mainnet = "mainnet",
    Testnet = "testnet",
    Devnet = "devnet",
    Testing = "testing",
    Premainnet = "premainnet"
}
export declare const APT_CHAIN_ID_NAME_MAPPING: Record<number, WalletAdapterNetwork>;
export declare const APT_CHAIN_ID_RPC_MAPPING: Mapping;
export declare const EIP1193_EVENTS: Array<string>;
export declare const LOGIN_PERSISTING_TIME: number;
export declare const DEFAULT_APP_ID = "00000000-0000-0000-0000-000000000000";
export declare const SDK_VERSION = "[VI]{version}[/VI]";
export {};
