/// <reference types="typescript" />
import { EIP1193Provider, IEthereumProvider } from 'eip1193-provider';

declare enum KEY_SESSION {
  prod = 'BLOCTO_SDK',
  dev = 'BLOCTO_SDK_DEV',
  staging = 'BLOCTO_SDK_STAGING',
}

interface BaseConfig {
  appId?: string;
}

declare enum WalletAdapterNetwork {
  Mainnet = 'mainnet',
  Testnet = 'testnet',
  Devnet = 'devnet',
  Testing = 'testing',
  Premainnet = 'premainnet',
}

declare interface BloctoProviderInterface extends EIP1193Provider {
  isBlocto: boolean;
  isConnecting: boolean;
  appId: string;
  eventListeners: {
    [key: string]: Array<(arg?: any) => void>;
  };
}

interface SingleChainConfig extends BaseConfig {
  chainId: string | number | null;
  rpc?: string;
  walletServer?: string;
}

interface MultiChainConfig extends BaseConfig {
  defaultChainId: string | number | null;
  walletServer?: string;
  switchableChains: AddEthereumChainParameter[];
}
// EthereumProviderConfig can be both single chain or multi chain config.
type EthereumProviderConfig = SingleChainConfig | MultiChainConfig;

interface EIP1193RequestPayload {
  id?: number;
  jsonrpc?: string;
  method: string;
  params?: Array<any>;
  callback?: JsonRpcCallback;
}

interface SwitchableNetwork {
  [id: number | string]: {
    name: string;
    display_name: string;
    network_type: string;
    wallet_web_url: string;
    rpc_url: string;
  };
}

interface EthereumProviderInterface
  extends BloctoProviderInterface,
    IEthereumProvider {
  chainId: string | number;
  networkVersion: string | number;
  rpc: string;
  _blocto: {
    sessionKeyEnv: KEY_SESSION;
    walletServer: string;
    blockchainName: string;
    networkType: string;
    switchableNetwork: SwitchableNetwork;
  };
  sendUserOperation(userOp: IUserOperation): Promise<string>;
  request(
    args: EIP1193RequestPayload | Array<EIP1193RequestPayload>
  ): Promise<any>;
  loadSwitchableNetwork(
    networkList: {
      chainId: string;
      rpcUrls?: string[];
    }[]
  ): Promise<null>;
  supportChainList(): Promise<{ chainId: string; chainName: string }[]>;
  injectedWalletServer?: string;
}

interface AddEthereumChainParameter {
  chainId: string;
  rpcUrls: string[];
  [key: string]: any;
}

interface JsonRpcRequest {
  id?: string | undefined;
  jsonrpc: '2.0';
  method: string;
  params?: Array<any>;
}

interface JsonRpcResponse {
  id: string | undefined;
  jsonrpc: '2.0';
  method: string;
  result?: unknown;
  error?: Error;
}

type JsonRpcCallback = (
  error: Error | null,
  response?: JsonRpcResponse
) => unknown;

interface PromiseResponseItem {
  status: 'fulfilled' | 'rejected';
  value?: any;
  reason?: any;
}

/**
 *  A [[HexString]] whose length is even, which ensures it is a valid
 *  representation of binary data.
 */
type DataHexString = string;

/**
 *  An object that can be used to represent binary data.
 */
type BytesLike = DataHexString | Uint8Array;

/**
 *  Any type that can be used where a numeric value is needed.
 */
type Numeric = number | bigint;

/**
 *  Any type that can be used where a big number is needed.
 */
type BigNumberish = string | Numeric;

/**
 *  An interface for an ERC-4337 transaction object.
 *  Note: BloctoSDK do not need sender, nonce, initCode, signature to send userOperation.
 *  These parameters will be ignored.
 */
interface IUserOperation {
  callData: BytesLike;
  callGasLimit?: BigNumberish;
  verificationGasLimit?: BigNumberish;
  preVerificationGas?: BigNumberish;
  maxFeePerGas?: BigNumberish;
  maxPriorityFeePerGas?: BigNumberish;
  paymasterAndData?: BytesLike;
  /**
   *  If provided, please ensure it is same as login account.
   */
  sender?: string;
  /**
   * BloctoSDK do not need nonce to send userOperation. Will be ignored.
   * */
  nonce?: BigNumberish;
  /**
   * BloctoSDK do not need initCode to send userOperation. Will be ignored.
   * */
  initCode?: BytesLike;
  /**
   * BloctoSDK do not need signature to send userOperation. Will be ignored.
   * */
  signature?: BytesLike;
}

type ethereum_d_AddEthereumChainParameter = AddEthereumChainParameter;
type ethereum_d_BigNumberish = BigNumberish;
type ethereum_d_BytesLike = BytesLike;
type ethereum_d_DataHexString = DataHexString;
type ethereum_d_EIP1193RequestPayload = EIP1193RequestPayload;
type ethereum_d_EthereumProviderConfig = EthereumProviderConfig;
type ethereum_d_EthereumProviderInterface = EthereumProviderInterface;
type ethereum_d_IUserOperation = IUserOperation;
type ethereum_d_JsonRpcCallback = JsonRpcCallback;
type ethereum_d_JsonRpcRequest = JsonRpcRequest;
type ethereum_d_JsonRpcResponse = JsonRpcResponse;
type ethereum_d_Numeric = Numeric;
type ethereum_d_PromiseResponseItem = PromiseResponseItem;
declare namespace ethereum_d {
  export {
    ethereum_d_AddEthereumChainParameter as AddEthereumChainParameter,
    ethereum_d_BigNumberish as BigNumberish,
    ethereum_d_BytesLike as BytesLike,
    ethereum_d_DataHexString as DataHexString,
    ethereum_d_EIP1193RequestPayload as EIP1193RequestPayload,
    ethereum_d_EthereumProviderConfig as EthereumProviderConfig,
    ethereum_d_EthereumProviderInterface as EthereumProviderInterface,
    ethereum_d_IUserOperation as IUserOperation,
    ethereum_d_JsonRpcCallback as JsonRpcCallback,
    ethereum_d_JsonRpcRequest as JsonRpcRequest,
    ethereum_d_JsonRpcResponse as JsonRpcResponse,
    ethereum_d_Numeric as Numeric,
    ethereum_d_PromiseResponseItem as PromiseResponseItem,
  };
}

interface SignMessagePayload {
  address?: boolean;
  application?: boolean;
  chainId?: boolean;
  message: string;
  nonce: string;
}
interface SignMessageResponse {
  address?: string;
  application?: string;
  chainId?: number;
  fullMessage: string;
  message: string;
  nonce: string;
  prefix: 'APTOS';
  signature: string | string[];
  bitmap?: Uint8Array;
}

declare interface AptosProviderConfig extends BaseConfig {
  // @todo: support different network
  chainId: number;
  server?: string;
}

declare interface PublicAccount {
  address: string | null;
  publicKey: string[] | null;
  authKey: string | null;
  minKeysRequired?: number;
}

type NetworkInfo = {
  api?: string;
  chainId?: string;
  name: WalletAdapterNetwork | undefined;
};

type TxOptions = {
  max_gas_amount?: string;
  gas_unit_price?: string;
  [key: string]: any;
};

declare interface AptosProviderInterface
  extends BloctoProviderInterface {
  publicAccount: PublicAccount;
  network(): Promise<NetworkInfo>;
  connect: () => Promise<PublicAccount>;
  isConnected: () => Promise<boolean>;
  signAndSubmitTransaction(
    transaction: any,
    txOptions?: TxOptions
  ): Promise<{ hash: HexEncodedBytes }>;
  signTransaction(transaction: any): Promise<SubmitTransactionRequest>;
  signMessage(payload: SignMessagePayload): Promise<SignMessageResponse>;
  disconnect(): Promise<void>;
}

type aptos_d_AptosProviderConfig = AptosProviderConfig;
type aptos_d_AptosProviderInterface = AptosProviderInterface;
type aptos_d_NetworkInfo = NetworkInfo;
type aptos_d_PublicAccount = PublicAccount;
type aptos_d_SignMessagePayload = SignMessagePayload;
type aptos_d_SignMessageResponse = SignMessageResponse;
type aptos_d_TxOptions = TxOptions;
type aptos_d_WalletAdapterNetwork = WalletAdapterNetwork;
declare const aptos_d_WalletAdapterNetwork: typeof WalletAdapterNetwork;
declare namespace aptos_d {
  export {
    aptos_d_AptosProviderConfig as AptosProviderConfig,
    aptos_d_AptosProviderInterface as AptosProviderInterface,
    aptos_d_NetworkInfo as NetworkInfo,
    aptos_d_PublicAccount as PublicAccount,
    aptos_d_SignMessagePayload as SignMessagePayload,
    aptos_d_SignMessageResponse as SignMessageResponse,
    aptos_d_TxOptions as TxOptions,
    aptos_d_WalletAdapterNetwork as WalletAdapterNetwork,
  };
}

// eslint-disable-next-line spaced-comment


declare interface BloctoSDKConfig extends BaseConfig {
  ethereum?: EthereumProviderConfig;
  aptos?: AptosProviderConfig;
}
declare class BloctoSDK {
  ethereum?: EthereumProviderInterface;
  aptos?: AptosProviderInterface;
  constructor(config: BloctoSDKConfig);
}

export { AptosProviderConfig, AptosProviderInterface, aptos_d as AptosTypes, BaseConfig, BloctoSDKConfig, EthereumProviderConfig, EthereumProviderInterface, ethereum_d as EthereumTypes, BloctoSDK as default };
