!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).BloctoSDK=t()}(this,(function(){"use strict";function e(e,t,r,n){return new(r||(r=Promise))((function(i,o){function s(e){try{u(n.next(e))}catch(e){o(e)}}function a(e){try{u(n.throw(e))}catch(e){o(e)}}function u(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,a)}u((n=n.apply(e,t||[])).next())}))}function t(e,t,r,n){if("a"===r&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)}function r(e,t){if(!e)throw new Error(t)}var n,i;"function"==typeof SuppressedError&&SuppressedError,function(e){e.prod="BLOCTO_SDK",e.dev="BLOCTO_SDK_DEV",e.staging="BLOCTO_SDK_STAGING"}(n||(n={})),function(e){e.ETHEREUM="ethereum",e.APTOS="aptos"}(i||(i={}));const o={56:"https://bsc-dataseed1.binance.org",97:"https://data-seed-prebsc-1-s1.binance.org:8545",137:"https://rpc-mainnet.maticvigil.com/",80002:"https://rpc-amoy.polygon.technology/",43114:"https://api.avax.network/ext/bc/C/rpc",43113:"https://api.avax-test.network/ext/bc/C/rpc",42161:"https://arb1.arbitrum.io/rpc",421614:"https://arbitrum-sepolia.blockpi.network/v1/rpc/public",10:"https://mainnet.optimism.io",11155420:"https://sepolia.optimism.io",8453:"https://mainnet.base.org",84532:"https://sepolia.base.org",7777777:"https://rpc.zora.energy",999999999:"https://sepolia.rpc.zora.energy",534352:"https://rpc.scroll.io",534351:"https://sepolia-rpc.scroll.io",59144:"https://rpc.linea.build",1261120:"https://rpc.startale.com/zkatana",81457:"https://rpc.blast.io",168587773:"https://sepolia.blast.io"},s={prod:"https://wallet-v2.blocto.app",staging:"https://wallet-v2-staging.blocto.app",dev:"https://wallet-v2-dev.blocto.app"},a={prod:n.prod,staging:n.staging,dev:n.dev},u={1:n.prod,2:n.dev,3:n.dev,4:n.dev,5:n.staging},c={1:"https://wallet-v2.blocto.app",2:"https://wallet-v2-dev.blocto.app",3:"https://wallet-v2-dev.blocto.app",4:"https://wallet-v2-dev.blocto.app",5:"https://wallet-v2-staging.blocto.app"};var h;!function(e){e.Mainnet="mainnet",e.Testnet="testnet",e.Devnet="devnet",e.Testing="testing",e.Premainnet="premainnet"}(h||(h={}));const d={1:h.Mainnet,2:h.Testnet,3:h.Devnet,4:h.Testing,5:h.Premainnet},l={1:"https://fullnode.mainnet.aptoslabs.com/v1",2:"https://fullnode.testnet.aptoslabs.com/v1",3:"https://fullnode.devnet.aptoslabs.com/v1",4:"",5:"https://premainnet.aptosdev.com/v1"},f=["connect","disconnect","message","chainChanged","accountsChanged"],p="********-0000-0000-0000-************",v="0.10.2";class g{constructor(){this.isBlocto=!0,this.isConnecting=!1,this.eventListeners={},this.off=this.removeListener,f.forEach((e=>{this.eventListeners[e]=[]})),this.appId=p}request(t){return e(this,void 0,void 0,(function*(){}))}on(e,t){f.includes(e)&&this.eventListeners[e].push(t)}once(){}removeListener(e,t){const r=this.eventListeners[e].findIndex((e=>e===t));-1!==r&&this.eventListeners[e].splice(r,1)}}function y(e){const t=document.createElement("iframe");return t.setAttribute("src",e),t.setAttribute("style","width:100vw;height:100%;position:fixed;top:0;left:0;z-index:**********;border:none;box-sizing:border-box;color-scheme:light;inset:0px;display:block;pointer-events:auto;"),t}function m(e){document.body.appendChild(e)}function w(e){const t=e&&e.parentNode;t&&t.removeChild instanceof Function&&t.removeChild(e)}var b=(e,t,r=window)=>{r.addEventListener(e,(function n(i){t(i,(()=>r.removeEventListener(e,n)))}))};const E="undefined"!=typeof window?window.memoryStorage:new class{constructor(){this.storage={}}getItem(e){return this[e]||null}setItem(e,t){this.storage[e]=t}removeItem(e){delete this.storage[e]}},S=(()=>{if("undefined"==typeof window)return!1;try{window.sessionStorage.setItem("local_storage_supported","1");const e=window.sessionStorage.getItem("local_storage_supported");return window.sessionStorage.removeItem("local_storage_supported"),"1"===e}catch(e){return!1}})()?window.sessionStorage:E,_=(e,t=null)=>{const r=S.getItem(e);try{return r&&JSON.parse(r)||t}catch(e){return r||t}},A=(e,t)=>S.setItem(e,"string"==typeof t?t:JSON.stringify(t)),O=e=>{A(e,""),S.removeItem(e)},R=e=>{const t=_(e,null);return t?(new Date).getTime()>t.expiry||t.v!==v?(O(e),null):null==t?void 0:t.data:null},I=(e,t,r)=>{var n,i,o;const s=_(e),a={data:{code:(null==t?void 0:t.code)||(null===(n=null==s?void 0:s.data)||void 0===n?void 0:n.code),accounts:Object.assign(Object.assign({},null===(i=null==s?void 0:s.data)||void 0===i?void 0:i.accounts),null==t?void 0:t.accounts),evm:Object.assign(Object.assign({},null===(o=null==s?void 0:s.data)||void 0===o?void 0:o.evm),null==t?void 0:t.evm)},expiry:r||(null==s?void 0:s.expiry)||(new Date).getTime()+864e5,v:v};A(e,a)},P=(e,t)=>{var r,n,i;return(null===(r=R(e))||void 0===r?void 0:r.code)?(null===(i=null===(n=R(e))||void 0===n?void 0:n.accounts)||void 0===i?void 0:i[t])||null:(O(e),null)},T=(e,t)=>{var r,n,i;return(null===(r=R(e))||void 0===r?void 0:r.code)?(null===(i=null===(n=R(e))||void 0===n?void 0:n.evm)||void 0===i?void 0:i[t])||null:(O(e),null)},C=(e,t,r)=>{I(e,{evm:{[t]:r}})},N=e=>{const t=_(e);t&&(t.data.evm={},A(e,t))};function j(t,r,n){return e(this,void 0,void 0,(function*(){if(403!==t.status&&401!==t.status||(n&&n(),O(r)),!t.ok){const e=yield t.json(),r=new Error((null==e?void 0:e.message)||"unknown error");throw r.error_code=null==e?void 0:e.error_code,r}return t.json()}))}const D=e=>/\S+@\S+\.\S+/.test(e);var k;!function(e){e.INVALID_TRANSACTION="Invalid transaction",e.INVALID_TRANSACTIONS="Invalid transactions",e.INVALID_TRANSACTION_VALUE='Transaction params "value" should be hex-encoded string'}(k||(k={}));var x="undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{},L=[],M=[],U="undefined"!=typeof Uint8Array?Uint8Array:Array,K=!1;function B(){K=!0;for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",t=0;t<64;++t)L[t]=e[t],M[e.charCodeAt(t)]=t;M["-".charCodeAt(0)]=62,M["_".charCodeAt(0)]=63}function $(e,t,r){for(var n,i,o=[],s=t;s<r;s+=3)n=(e[s]<<16)+(e[s+1]<<8)+e[s+2],o.push(L[(i=n)>>18&63]+L[i>>12&63]+L[i>>6&63]+L[63&i]);return o.join("")}function q(e){var t;K||B();for(var r=e.length,n=r%3,i="",o=[],s=16383,a=0,u=r-n;a<u;a+=s)o.push($(e,a,a+s>u?u:a+s));return 1===n?(t=e[r-1],i+=L[t>>2],i+=L[t<<4&63],i+="=="):2===n&&(t=(e[r-2]<<8)+e[r-1],i+=L[t>>10],i+=L[t>>4&63],i+=L[t<<2&63],i+="="),o.push(i),o.join("")}function V(e,t,r,n,i){var o,s,a=8*i-n-1,u=(1<<a)-1,c=u>>1,h=-7,d=r?i-1:0,l=r?-1:1,f=e[t+d];for(d+=l,o=f&(1<<-h)-1,f>>=-h,h+=a;h>0;o=256*o+e[t+d],d+=l,h-=8);for(s=o&(1<<-h)-1,o>>=-h,h+=n;h>0;s=256*s+e[t+d],d+=l,h-=8);if(0===o)o=1-c;else{if(o===u)return s?NaN:1/0*(f?-1:1);s+=Math.pow(2,n),o-=c}return(f?-1:1)*s*Math.pow(2,o-n)}function Y(e,t,r,n,i,o){var s,a,u,c=8*o-i-1,h=(1<<c)-1,d=h>>1,l=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,f=n?0:o-1,p=n?1:-1,v=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,s=h):(s=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-s))<1&&(s--,u*=2),(t+=s+d>=1?l/u:l*Math.pow(2,1-d))*u>=2&&(s++,u/=2),s+d>=h?(a=0,s=h):s+d>=1?(a=(t*u-1)*Math.pow(2,i),s+=d):(a=t*Math.pow(2,d-1)*Math.pow(2,i),s=0));i>=8;e[r+f]=255&a,f+=p,a/=256,i-=8);for(s=s<<i|a,c+=i;c>0;e[r+f]=255&s,f+=p,s/=256,c-=8);e[r+f-p]|=128*v}var F={}.toString,z=Array.isArray||function(e){return"[object Array]"==F.call(e)};function J(){return H.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function G(e,t){if(J()<t)throw new RangeError("Invalid typed array length");return H.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=H.prototype:(null===e&&(e=new H(t)),e.length=t),e}function H(e,t,r){if(!(H.TYPED_ARRAY_SUPPORT||this instanceof H))return new H(e,t,r);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return Z(this,e)}return W(this,e,t,r)}function W(e,t,r,n){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,r,n){if(t.byteLength,r<0||t.byteLength<r)throw new RangeError("'offset' is out of bounds");if(t.byteLength<r+(n||0))throw new RangeError("'length' is out of bounds");t=void 0===r&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,r):new Uint8Array(t,r,n);H.TYPED_ARRAY_SUPPORT?(e=t).__proto__=H.prototype:e=Q(e,t);return e}(e,t,r,n):"string"==typeof t?function(e,t,r){"string"==typeof r&&""!==r||(r="utf8");if(!H.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var n=0|re(t,r);e=G(e,n);var i=e.write(t,r);i!==n&&(e=e.slice(0,i));return e}(e,t,r):function(e,t){if(te(t)){var r=0|ee(t.length);return 0===(e=G(e,r)).length||t.copy(e,0,0,r),e}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||(n=t.length)!=n?G(e,0):Q(e,t);if("Buffer"===t.type&&z(t.data))return Q(e,t.data)}var n;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,t)}function X(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function Z(e,t){if(X(t),e=G(e,t<0?0:0|ee(t)),!H.TYPED_ARRAY_SUPPORT)for(var r=0;r<t;++r)e[r]=0;return e}function Q(e,t){var r=t.length<0?0:0|ee(t.length);e=G(e,r);for(var n=0;n<r;n+=1)e[n]=255&t[n];return e}function ee(e){if(e>=J())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+J().toString(16)+" bytes");return 0|e}function te(e){return!(null==e||!e._isBuffer)}function re(e,t){if(te(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var r=e.length;if(0===r)return 0;for(var n=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return Te(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return Ce(e).length;default:if(n)return Te(e).length;t=(""+t).toLowerCase(),n=!0}}function ne(e,t,r){var n=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return me(this,t,r);case"utf8":case"utf-8":return pe(this,t,r);case"ascii":return ge(this,t,r);case"latin1":case"binary":return ye(this,t,r);case"base64":return fe(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return we(this,t,r);default:if(n)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),n=!0}}function ie(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function oe(e,t,r,n,i){if(0===e.length)return-1;if("string"==typeof r?(n=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return-1;r=e.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof t&&(t=H.from(t,n)),te(t))return 0===t.length?-1:se(e,t,r,n,i);if("number"==typeof t)return t&=255,H.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):se(e,[t],r,n,i);throw new TypeError("val must be string, number or Buffer")}function se(e,t,r,n,i){var o,s=1,a=e.length,u=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return-1;s=2,a/=2,u/=2,r/=2}function c(e,t){return 1===s?e[t]:e.readUInt16BE(t*s)}if(i){var h=-1;for(o=r;o<a;o++)if(c(e,o)===c(t,-1===h?0:o-h)){if(-1===h&&(h=o),o-h+1===u)return h*s}else-1!==h&&(o-=o-h),h=-1}else for(r+u>a&&(r=a-u),o=r;o>=0;o--){for(var d=!0,l=0;l<u;l++)if(c(e,o+l)!==c(t,l)){d=!1;break}if(d)return o}return-1}function ae(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=t.length;if(o%2!=0)throw new TypeError("Invalid hex string");n>o/2&&(n=o/2);for(var s=0;s<n;++s){var a=parseInt(t.substr(2*s,2),16);if(isNaN(a))return s;e[r+s]=a}return s}function ue(e,t,r,n){return Ne(Te(t,e.length-r),e,r,n)}function ce(e,t,r,n){return Ne(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(t),e,r,n)}function he(e,t,r,n){return ce(e,t,r,n)}function de(e,t,r,n){return Ne(Ce(t),e,r,n)}function le(e,t,r,n){return Ne(function(e,t){for(var r,n,i,o=[],s=0;s<e.length&&!((t-=2)<0);++s)n=(r=e.charCodeAt(s))>>8,i=r%256,o.push(i),o.push(n);return o}(t,e.length-r),e,r,n)}function fe(e,t,r){return 0===t&&r===e.length?q(e):q(e.slice(t,r))}function pe(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var o,s,a,u,c=e[i],h=null,d=c>239?4:c>223?3:c>191?2:1;if(i+d<=r)switch(d){case 1:c<128&&(h=c);break;case 2:128==(192&(o=e[i+1]))&&(u=(31&c)<<6|63&o)>127&&(h=u);break;case 3:o=e[i+1],s=e[i+2],128==(192&o)&&128==(192&s)&&(u=(15&c)<<12|(63&o)<<6|63&s)>2047&&(u<55296||u>57343)&&(h=u);break;case 4:o=e[i+1],s=e[i+2],a=e[i+3],128==(192&o)&&128==(192&s)&&128==(192&a)&&(u=(15&c)<<18|(63&o)<<12|(63&s)<<6|63&a)>65535&&u<1114112&&(h=u)}null===h?(h=65533,d=1):h>65535&&(h-=65536,n.push(h>>>10&1023|55296),h=56320|1023&h),n.push(h),i+=d}return function(e){var t=e.length;if(t<=ve)return String.fromCharCode.apply(String,e);var r="",n=0;for(;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=ve));return r}(n)}H.TYPED_ARRAY_SUPPORT=void 0===x.TYPED_ARRAY_SUPPORT||x.TYPED_ARRAY_SUPPORT,J(),H.poolSize=8192,H._augment=function(e){return e.__proto__=H.prototype,e},H.from=function(e,t,r){return W(null,e,t,r)},H.TYPED_ARRAY_SUPPORT&&(H.prototype.__proto__=Uint8Array.prototype,H.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&H[Symbol.species]),H.alloc=function(e,t,r){return function(e,t,r,n){return X(t),t<=0?G(e,t):void 0!==r?"string"==typeof n?G(e,t).fill(r,n):G(e,t).fill(r):G(e,t)}(null,e,t,r)},H.allocUnsafe=function(e){return Z(null,e)},H.allocUnsafeSlow=function(e){return Z(null,e)},H.isBuffer=function(e){return null!=e&&(!!e._isBuffer||je(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&je(e.slice(0,0))}(e))},H.compare=function(e,t){if(!te(e)||!te(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var r=e.length,n=t.length,i=0,o=Math.min(r,n);i<o;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:n<r?1:0},H.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},H.concat=function(e,t){if(!z(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return H.alloc(0);var r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;var n=H.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var o=e[r];if(!te(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(n,i),i+=o.length}return n},H.byteLength=re,H.prototype._isBuffer=!0,H.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)ie(this,t,t+1);return this},H.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)ie(this,t,t+3),ie(this,t+1,t+2);return this},H.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)ie(this,t,t+7),ie(this,t+1,t+6),ie(this,t+2,t+5),ie(this,t+3,t+4);return this},H.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?pe(this,0,e):ne.apply(this,arguments)},H.prototype.equals=function(e){if(!te(e))throw new TypeError("Argument must be a Buffer");return this===e||0===H.compare(this,e)},H.prototype.inspect=function(){var e="";return this.length>0&&(e=this.toString("hex",0,50).match(/.{2}/g).join(" "),this.length>50&&(e+=" ... ")),"<Buffer "+e+">"},H.prototype.compare=function(e,t,r,n,i){if(!te(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return-1;if(t>=r)return 1;if(this===e)return 0;for(var o=(i>>>=0)-(n>>>=0),s=(r>>>=0)-(t>>>=0),a=Math.min(o,s),u=this.slice(n,i),c=e.slice(t,r),h=0;h<a;++h)if(u[h]!==c[h]){o=u[h],s=c[h];break}return o<s?-1:s<o?1:0},H.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},H.prototype.indexOf=function(e,t,r){return oe(this,e,t,r,!0)},H.prototype.lastIndexOf=function(e,t,r){return oe(this,e,t,r,!1)},H.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(r)?(r|=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-t;if((void 0===r||r>i)&&(r=i),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var o=!1;;)switch(n){case"hex":return ae(this,e,t,r);case"utf8":case"utf-8":return ue(this,e,t,r);case"ascii":return ce(this,e,t,r);case"latin1":case"binary":return he(this,e,t,r);case"base64":return de(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return le(this,e,t,r);default:if(o)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),o=!0}},H.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var ve=4096;function ge(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}function ye(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}function me(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=t;o<r;++o)i+=Pe(e[o]);return i}function we(e,t,r){for(var n=e.slice(t,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function be(e,t,r){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function Ee(e,t,r,n,i,o){if(!te(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw new RangeError('"value" argument is out of bounds');if(r+n>e.length)throw new RangeError("Index out of range")}function Se(e,t,r,n){t<0&&(t=65535+t+1);for(var i=0,o=Math.min(e.length-r,2);i<o;++i)e[r+i]=(t&255<<8*(n?i:1-i))>>>8*(n?i:1-i)}function _e(e,t,r,n){t<0&&(t=4294967295+t+1);for(var i=0,o=Math.min(e.length-r,4);i<o;++i)e[r+i]=t>>>8*(n?i:3-i)&255}function Ae(e,t,r,n,i,o){if(r+n>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function Oe(e,t,r,n,i){return i||Ae(e,0,r,4),Y(e,t,r,n,23,4),r+4}function Re(e,t,r,n,i){return i||Ae(e,0,r,8),Y(e,t,r,n,52,8),r+8}H.prototype.slice=function(e,t){var r,n=this.length;if((e=~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),(t=void 0===t?n:~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e),H.TYPED_ARRAY_SUPPORT)(r=this.subarray(e,t)).__proto__=H.prototype;else{var i=t-e;r=new H(i,void 0);for(var o=0;o<i;++o)r[o]=this[o+e]}return r},H.prototype.readUIntLE=function(e,t,r){e|=0,t|=0,r||be(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n},H.prototype.readUIntBE=function(e,t,r){e|=0,t|=0,r||be(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},H.prototype.readUInt8=function(e,t){return t||be(e,1,this.length),this[e]},H.prototype.readUInt16LE=function(e,t){return t||be(e,2,this.length),this[e]|this[e+1]<<8},H.prototype.readUInt16BE=function(e,t){return t||be(e,2,this.length),this[e]<<8|this[e+1]},H.prototype.readUInt32LE=function(e,t){return t||be(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},H.prototype.readUInt32BE=function(e,t){return t||be(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},H.prototype.readIntLE=function(e,t,r){e|=0,t|=0,r||be(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},H.prototype.readIntBE=function(e,t,r){e|=0,t|=0,r||be(e,t,this.length);for(var n=t,i=1,o=this[e+--n];n>0&&(i*=256);)o+=this[e+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*t)),o},H.prototype.readInt8=function(e,t){return t||be(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},H.prototype.readInt16LE=function(e,t){t||be(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},H.prototype.readInt16BE=function(e,t){t||be(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},H.prototype.readInt32LE=function(e,t){return t||be(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},H.prototype.readInt32BE=function(e,t){return t||be(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},H.prototype.readFloatLE=function(e,t){return t||be(e,4,this.length),V(this,e,!0,23,4)},H.prototype.readFloatBE=function(e,t){return t||be(e,4,this.length),V(this,e,!1,23,4)},H.prototype.readDoubleLE=function(e,t){return t||be(e,8,this.length),V(this,e,!0,52,8)},H.prototype.readDoubleBE=function(e,t){return t||be(e,8,this.length),V(this,e,!1,52,8)},H.prototype.writeUIntLE=function(e,t,r,n){(e=+e,t|=0,r|=0,n)||Ee(this,e,t,r,Math.pow(2,8*r)-1,0);var i=1,o=0;for(this[t]=255&e;++o<r&&(i*=256);)this[t+o]=e/i&255;return t+r},H.prototype.writeUIntBE=function(e,t,r,n){(e=+e,t|=0,r|=0,n)||Ee(this,e,t,r,Math.pow(2,8*r)-1,0);var i=r-1,o=1;for(this[t+i]=255&e;--i>=0&&(o*=256);)this[t+i]=e/o&255;return t+r},H.prototype.writeUInt8=function(e,t,r){return e=+e,t|=0,r||Ee(this,e,t,1,255,0),H.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},H.prototype.writeUInt16LE=function(e,t,r){return e=+e,t|=0,r||Ee(this,e,t,2,65535,0),H.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):Se(this,e,t,!0),t+2},H.prototype.writeUInt16BE=function(e,t,r){return e=+e,t|=0,r||Ee(this,e,t,2,65535,0),H.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):Se(this,e,t,!1),t+2},H.prototype.writeUInt32LE=function(e,t,r){return e=+e,t|=0,r||Ee(this,e,t,4,4294967295,0),H.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):_e(this,e,t,!0),t+4},H.prototype.writeUInt32BE=function(e,t,r){return e=+e,t|=0,r||Ee(this,e,t,4,4294967295,0),H.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):_e(this,e,t,!1),t+4},H.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);Ee(this,e,t,r,i-1,-i)}var o=0,s=1,a=0;for(this[t]=255&e;++o<r&&(s*=256);)e<0&&0===a&&0!==this[t+o-1]&&(a=1),this[t+o]=(e/s>>0)-a&255;return t+r},H.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);Ee(this,e,t,r,i-1,-i)}var o=r-1,s=1,a=0;for(this[t+o]=255&e;--o>=0&&(s*=256);)e<0&&0===a&&0!==this[t+o+1]&&(a=1),this[t+o]=(e/s>>0)-a&255;return t+r},H.prototype.writeInt8=function(e,t,r){return e=+e,t|=0,r||Ee(this,e,t,1,127,-128),H.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},H.prototype.writeInt16LE=function(e,t,r){return e=+e,t|=0,r||Ee(this,e,t,2,32767,-32768),H.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):Se(this,e,t,!0),t+2},H.prototype.writeInt16BE=function(e,t,r){return e=+e,t|=0,r||Ee(this,e,t,2,32767,-32768),H.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):Se(this,e,t,!1),t+2},H.prototype.writeInt32LE=function(e,t,r){return e=+e,t|=0,r||Ee(this,e,t,4,2147483647,-2147483648),H.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):_e(this,e,t,!0),t+4},H.prototype.writeInt32BE=function(e,t,r){return e=+e,t|=0,r||Ee(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),H.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):_e(this,e,t,!1),t+4},H.prototype.writeFloatLE=function(e,t,r){return Oe(this,e,t,!0,r)},H.prototype.writeFloatBE=function(e,t,r){return Oe(this,e,t,!1,r)},H.prototype.writeDoubleLE=function(e,t,r){return Re(this,e,t,!0,r)},H.prototype.writeDoubleBE=function(e,t,r){return Re(this,e,t,!1,r)},H.prototype.copy=function(e,t,r,n){if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i,o=n-r;if(this===e&&r<t&&t<n)for(i=o-1;i>=0;--i)e[i+t]=this[i+r];else if(o<1e3||!H.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)e[i+t]=this[i+r];else Uint8Array.prototype.set.call(e,this.subarray(r,r+o),t);return o},H.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),1===e.length){var i=e.charCodeAt(0);i<256&&(e=i)}if(void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!H.isEncoding(n))throw new TypeError("Unknown encoding: "+n)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;var o;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(o=t;o<r;++o)this[o]=e;else{var s=te(e)?e:Te(new H(e,n).toString()),a=s.length;for(o=0;o<r-t;++o)this[o+t]=s[o%a]}return this};var Ie=/[^+\/0-9A-Za-z-_]/g;function Pe(e){return e<16?"0"+e.toString(16):e.toString(16)}function Te(e,t){var r;t=t||1/0;for(var n=e.length,i=null,o=[],s=0;s<n;++s){if((r=e.charCodeAt(s))>55295&&r<57344){if(!i){if(r>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(s+1===n){(t-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&o.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;o.push(r)}else if(r<2048){if((t-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function Ce(e){return function(e){var t,r,n,i,o,s;K||B();var a=e.length;if(a%4>0)throw new Error("Invalid string. Length must be a multiple of 4");o="="===e[a-2]?2:"="===e[a-1]?1:0,s=new U(3*a/4-o),n=o>0?a-4:a;var u=0;for(t=0,r=0;t<n;t+=4,r+=3)i=M[e.charCodeAt(t)]<<18|M[e.charCodeAt(t+1)]<<12|M[e.charCodeAt(t+2)]<<6|M[e.charCodeAt(t+3)],s[u++]=i>>16&255,s[u++]=i>>8&255,s[u++]=255&i;return 2===o?(i=M[e.charCodeAt(t)]<<2|M[e.charCodeAt(t+1)]>>4,s[u++]=255&i):1===o&&(i=M[e.charCodeAt(t)]<<10|M[e.charCodeAt(t+1)]<<4|M[e.charCodeAt(t+2)]>>2,s[u++]=i>>8&255,s[u++]=255&i),s}(function(e){if((e=function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}(e).replace(Ie,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function Ne(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length||i>=e.length);++i)t[i+r]=e[i];return i}function je(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}const De=e=>"string"==typeof e&&/^0x[0-9A-Fa-f]*$/.test(e),ke=e=>e&&"object"==typeof e&&"from"in e?e.value&&!De(e.value)?{isValid:!1,invalidMsg:k.INVALID_TRANSACTION_VALUE}:{isValid:!0}:{isValid:!1,invalidMsg:k.INVALID_TRANSACTION};function xe(){return e(this,void 0,void 0,(function*(){const{networks:e}=yield fetch("https://api.blocto.app/networks/evm").then((e=>e.json()));return e.reduce(((e,t)=>Object.assign(Object.assign({},e),{[t.chain_id]:t})),{})}))}var Le={};function Me(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}function Ue(e){return Ue="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ue(e)}function Ke(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Be(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Me(n.key),n)}}function $e(e,t,r){return t&&Be(e.prototype,t),r&&Be(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function qe(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ye(e,t)}function Ve(e){return Ve=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Ve(e)}function Ye(e,t){return Ye=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ye(e,t)}function Fe(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function ze(e,t,r){return ze=Fe()?Reflect.construct.bind():function(e,t,r){var n=[null];n.push.apply(n,t);var i=new(Function.bind.apply(e,n));return r&&Ye(i,r.prototype),i},ze.apply(null,arguments)}function Je(e){var t="function"==typeof Map?new Map:void 0;return Je=function(e){if(null===e||!function(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}}(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return ze(e,arguments,Ve(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),Ye(r,e)},Je(e)}function Ge(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function He(e){var t=Fe();return function(){var r,n=Ve(e);if(t){var i=Ve(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return Ge(this,r)}}function We(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,o,s,a=[],u=!0,c=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(a.push(n.value),a.length!==t);u=!0);}catch(e){c=!0,i=e}finally{try{if(!u&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(c)throw i}}return a}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Xe(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Xe(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Xe(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Ze={},Qe=ot;ot.default=ot,ot.stable=ct,ot.stableStringify=ct;var et="[...]",tt="[Circular]",rt=[],nt=[];function it(){return{depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}}function ot(e,t,r,n){var i;void 0===n&&(n=it()),at(e,"",0,[],void 0,0,n);try{i=0===nt.length?JSON.stringify(e,t,r):JSON.stringify(e,dt(t),r)}catch(e){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==rt.length;){var o=rt.pop();4===o.length?Object.defineProperty(o[0],o[1],o[3]):o[0][o[1]]=o[2]}}return i}function st(e,t,r,n){var i=Object.getOwnPropertyDescriptor(n,r);void 0!==i.get?i.configurable?(Object.defineProperty(n,r,{value:e}),rt.push([n,r,t,i])):nt.push([t,r,e]):(n[r]=e,rt.push([n,r,t]))}function at(e,t,r,n,i,o,s){var a;if(o+=1,"object"===Ue(e)&&null!==e){for(a=0;a<n.length;a++)if(n[a]===e)return void st(tt,e,t,i);if(void 0!==s.depthLimit&&o>s.depthLimit)return void st(et,e,t,i);if(void 0!==s.edgesLimit&&r+1>s.edgesLimit)return void st(et,e,t,i);if(n.push(e),Array.isArray(e))for(a=0;a<e.length;a++)at(e[a],a,a,n,e,o,s);else{var u=Object.keys(e);for(a=0;a<u.length;a++){var c=u[a];at(e[c],c,a,n,e,o,s)}}n.pop()}}function ut(e,t){return e<t?-1:e>t?1:0}function ct(e,t,r,n){void 0===n&&(n=it());var i,o=ht(e,"",0,[],void 0,0,n)||e;try{i=0===nt.length?JSON.stringify(o,t,r):JSON.stringify(o,dt(t),r)}catch(e){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==rt.length;){var s=rt.pop();4===s.length?Object.defineProperty(s[0],s[1],s[3]):s[0][s[1]]=s[2]}}return i}function ht(e,t,r,n,i,o,s){var a;if(o+=1,"object"===Ue(e)&&null!==e){for(a=0;a<n.length;a++)if(n[a]===e)return void st(tt,e,t,i);try{if("function"==typeof e.toJSON)return}catch(e){return}if(void 0!==s.depthLimit&&o>s.depthLimit)return void st(et,e,t,i);if(void 0!==s.edgesLimit&&r+1>s.edgesLimit)return void st(et,e,t,i);if(n.push(e),Array.isArray(e))for(a=0;a<e.length;a++)ht(e[a],a,a,n,e,o,s);else{var u={},c=Object.keys(e).sort(ut);for(a=0;a<c.length;a++){var h=c[a];ht(e[h],h,a,n,e,o,s),u[h]=e[h]}if(void 0===i)return u;rt.push([i,t,e]),i[t]=u}n.pop()}}function dt(e){return e=void 0!==e?e:function(e,t){return t},function(t,r){if(nt.length>0)for(var n=0;n<nt.length;n++){var i=nt[n];if(i[1]===t&&i[0]===r){r=i[2],nt.splice(n,1);break}}return e.call(this,t,r)}}Object.defineProperty(Ze,"__esModule",{value:!0}),Ze.EthereumProviderError=Ze.EthereumRpcError=void 0;var lt=Qe,ft=function(e){qe(r,e);var t=He(r);function r(e,n,i){var o;if(Ke(this,r),!Number.isInteger(e))throw new Error('"code" must be an integer.');if(!n||"string"!=typeof n)throw new Error('"message" must be a nonempty string.');return(o=t.call(this,n)).code=e,void 0!==i&&(o.data=i),o}return $e(r,[{key:"serialize",value:function(){var e={code:this.code,message:this.message};return void 0!==this.data&&(e.data=this.data),this.stack&&(e.stack=this.stack),e}},{key:"toString",value:function(){return lt.default(this.serialize(),vt,2)}}]),r}(Je(Error));Ze.EthereumRpcError=ft;var pt=function(e){qe(r,e);var t=He(r);function r(e,n,i){if(Ke(this,r),!function(e){return Number.isInteger(e)&&e>=1e3&&e<=4999}(e))throw new Error('"code" must be an integer such that: 1000 <= code <= 4999');return t.call(this,e,n,i)}return $e(r)}(ft);function vt(e,t){if("[Circular]"!==t)return t}Ze.EthereumProviderError=pt;var gt={},yt={};Object.defineProperty(yt,"__esModule",{value:!0}),yt.errorValues=yt.errorCodes=void 0,yt.errorCodes={rpc:{invalidInput:-32e3,resourceNotFound:-32001,resourceUnavailable:-32002,transactionRejected:-32003,methodNotSupported:-32004,limitExceeded:-32005,parse:-32700,invalidRequest:-32600,methodNotFound:-32601,invalidParams:-32602,internal:-32603},provider:{userRejectedRequest:4001,unauthorized:4100,unsupportedMethod:4200,disconnected:4900,chainDisconnected:4901}},yt.errorValues={"-32700":{standard:"JSON RPC 2.0",message:"Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text."},"-32600":{standard:"JSON RPC 2.0",message:"The JSON sent is not a valid Request object."},"-32601":{standard:"JSON RPC 2.0",message:"The method does not exist / is not available."},"-32602":{standard:"JSON RPC 2.0",message:"Invalid method parameter(s)."},"-32603":{standard:"JSON RPC 2.0",message:"Internal JSON-RPC error."},"-32000":{standard:"EIP-1474",message:"Invalid input."},"-32001":{standard:"EIP-1474",message:"Resource not found."},"-32002":{standard:"EIP-1474",message:"Resource unavailable."},"-32003":{standard:"EIP-1474",message:"Transaction rejected."},"-32004":{standard:"EIP-1474",message:"Method not supported."},"-32005":{standard:"EIP-1474",message:"Request limit exceeded."},4001:{standard:"EIP-1193",message:"User rejected the request."},4100:{standard:"EIP-1193",message:"The requested account and/or method has not been authorized by the user."},4200:{standard:"EIP-1193",message:"The requested method is not supported by this Ethereum provider."},4900:{standard:"EIP-1193",message:"The provider is disconnected from all chains."},4901:{standard:"EIP-1193",message:"The provider is disconnected from the specified chain."}},function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.serializeError=e.isValidCode=e.getMessageFromCode=e.JSON_RPC_SERVER_ERROR_MESSAGE=void 0;var t=yt,r=Ze,n=t.errorCodes.rpc.internal,i="Unspecified error message. This is a bug, please report it.",o={code:n,message:s(n)};function s(r){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i;if(Number.isInteger(r)){var o=r.toString();if(h(t.errorValues,o))return t.errorValues[o].message;if(u(r))return e.JSON_RPC_SERVER_ERROR_MESSAGE}return n}function a(e){if(!Number.isInteger(e))return!1;var r=e.toString();return!!t.errorValues[r]||!!u(e)}function u(e){return e>=-32099&&e<=-32e3}function c(e){return e&&"object"===Ue(e)&&!Array.isArray(e)?Object.assign({},e):e}function h(e,t){return Object.prototype.hasOwnProperty.call(e,t)}e.JSON_RPC_SERVER_ERROR_MESSAGE="Unspecified server error.",e.getMessageFromCode=s,e.isValidCode=a,e.serializeError=function(e){var t,n,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},u=i.fallbackError,d=void 0===u?o:u,l=i.shouldIncludeStack,f=void 0!==l&&l;if(!d||!Number.isInteger(d.code)||"string"!=typeof d.message)throw new Error("Must provide fallback error with integer number code and string message.");if(e instanceof r.EthereumRpcError)return e.serialize();var p={};if(e&&"object"===Ue(e)&&!Array.isArray(e)&&h(e,"code")&&a(e.code)){var v=e;p.code=v.code,v.message&&"string"==typeof v.message?(p.message=v.message,h(v,"data")&&(p.data=v.data)):(p.message=s(p.code),p.data={originalError:c(e)})}else{p.code=d.code;var g=null===(t=e)||void 0===t?void 0:t.message;p.message=g&&"string"==typeof g?g:d.message,p.data={originalError:c(e)}}var y=null===(n=e)||void 0===n?void 0:n.stack;return f&&e&&y&&"string"==typeof y&&(p.stack=y),p}}(gt);var mt={};Object.defineProperty(mt,"__esModule",{value:!0}),mt.ethErrors=void 0;var wt,bt,Et,St,_t,At=Ze,Ot=gt,Rt=yt;function It(e,t){var r=We(Tt(t),2),n=r[0],i=r[1];return new At.EthereumRpcError(e,n||Ot.getMessageFromCode(e),i)}function Pt(e,t){var r=We(Tt(t),2),n=r[0],i=r[1];return new At.EthereumProviderError(e,n||Ot.getMessageFromCode(e),i)}function Tt(e){if(e){if("string"==typeof e)return[e];if("object"===Ue(e)&&!Array.isArray(e)){var t=e.message,r=e.data;if(t&&"string"!=typeof t)throw new Error("Must specify string message.");return[t||void 0,r]}}return[]}function Ct(e){return e?"number"==typeof e?e:e.startsWith("0x")?parseInt(e,16):parseInt(e,10):1}mt.ethErrors={rpc:{parse:function(e){return It(Rt.errorCodes.rpc.parse,e)},invalidRequest:function(e){return It(Rt.errorCodes.rpc.invalidRequest,e)},invalidParams:function(e){return It(Rt.errorCodes.rpc.invalidParams,e)},methodNotFound:function(e){return It(Rt.errorCodes.rpc.methodNotFound,e)},internal:function(e){return It(Rt.errorCodes.rpc.internal,e)},server:function(e){if(!e||"object"!==Ue(e)||Array.isArray(e))throw new Error("Ethereum RPC Server errors must provide single object argument.");var t=e.code;if(!Number.isInteger(t)||t>-32005||t<-32099)throw new Error('"code" must be an integer such that: -32099 <= code <= -32005');return It(t,e)},invalidInput:function(e){return It(Rt.errorCodes.rpc.invalidInput,e)},resourceNotFound:function(e){return It(Rt.errorCodes.rpc.resourceNotFound,e)},resourceUnavailable:function(e){return It(Rt.errorCodes.rpc.resourceUnavailable,e)},transactionRejected:function(e){return It(Rt.errorCodes.rpc.transactionRejected,e)},methodNotSupported:function(e){return It(Rt.errorCodes.rpc.methodNotSupported,e)},limitExceeded:function(e){return It(Rt.errorCodes.rpc.limitExceeded,e)}},provider:{userRejectedRequest:function(e){return Pt(Rt.errorCodes.provider.userRejectedRequest,e)},unauthorized:function(e){return Pt(Rt.errorCodes.provider.unauthorized,e)},unsupportedMethod:function(e){return Pt(Rt.errorCodes.provider.unsupportedMethod,e)},disconnected:function(e){return Pt(Rt.errorCodes.provider.disconnected,e)},chainDisconnected:function(e){return Pt(Rt.errorCodes.provider.chainDisconnected,e)},custom:function(e){if(!e||"object"!==Ue(e)||Array.isArray(e))throw new Error("Ethereum Provider custom errors must provide single object argument.");var t=e.code,r=e.message,n=e.data;if(!r||"string"!=typeof r)throw new Error('"message" must be a nonempty string');return new At.EthereumProviderError(t,r,n)}}},function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.getMessageFromCode=e.serializeError=e.EthereumProviderError=e.EthereumRpcError=e.ethErrors=e.errorCodes=void 0;var t=Ze;Object.defineProperty(e,"EthereumRpcError",{enumerable:!0,get:function(){return t.EthereumRpcError}}),Object.defineProperty(e,"EthereumProviderError",{enumerable:!0,get:function(){return t.EthereumProviderError}});var r=gt;Object.defineProperty(e,"serializeError",{enumerable:!0,get:function(){return r.serializeError}}),Object.defineProperty(e,"getMessageFromCode",{enumerable:!0,get:function(){return r.getMessageFromCode}});var n=mt;Object.defineProperty(e,"ethErrors",{enumerable:!0,get:function(){return n.ethErrors}});var i=yt;Object.defineProperty(e,"errorCodes",{enumerable:!0,get:function(){return i.errorCodes}})}(Le);class Nt extends g{get existedSDK(){if("undefined"!=typeof window)return window.ethereum}constructor(e){var t;if(super(),wt.add(this),this.networkVersion="1",this.off=this.removeListener,this.injectedWalletServer=e.walletServer,this._blocto={sessionKeyEnv:n.prod,walletServer:this.injectedWalletServer||"",blockchainName:"",networkType:"",switchableNetwork:{}},this.appId=e.appId||p,"chainId"in e){const{chainId:t,rpc:n}=e;r(t,"'chainId' is required"),this.networkVersion=`${Ct(t)}`,this.chainId=`0x${Ct(t).toString(16)}`,this.rpc=n||o[this.networkVersion],r(this.rpc,"'rpc' is required")}else{const{defaultChainId:n,switchableChains:i}=e;r(n,"'defaultChainId' is required"),this.networkVersion=`${Ct(n)}`,this.chainId=`0x${Ct(n).toString(16)}`;const s=i.find((e=>Ct(e.chainId)===Ct(n)));if(!s)throw Le.ethErrors.provider.custom({code:1001,message:`Chain ${n} is not in switchableChains list`});this.rpc=(null===(t=s.rpcUrls)||void 0===t?void 0:t[0])||o[this.networkVersion],r(this.rpc,"'rpc' is required"),this._blocto.unloadedNetwork=i}}send(t,r){return e(this,void 0,void 0,(function*(){switch(!0){case r instanceof Function:return this.sendAsync(t,r);case"string"==typeof t&&Array.isArray(r):return this.sendAsync({jsonrpc:"2.0",method:t,params:r});default:return this.sendAsync(t)}}))}sendAsync(t,r){return e(this,void 0,void 0,(function*(){const e=e=>e.reduce(((e,t)=>{var r;return"eth_sendTransaction"===t.method?e.sendRequests.push(null===(r=t.params)||void 0===r?void 0:r[0]):e.otherRequests.push(this.request(t)),e}),{sendRequests:[],otherRequests:[]});function n(e,t){const r=[];let n=1;return e.forEach((e=>{const i=function(e){return{id:String(e.id),jsonrpc:"2.0",method:e.method}}(e);"eth_sendTransaction"===e.method?(i.result=t[0].value,i.error="fulfilled"!==t[0].status?t[0].reason:void 0):n<t.length&&(i.result=t[n].value,i.error="fulfilled"!==t[n].status?t[n].reason:void 0,n++),r.push(i)})),r}const i=new Promise((r=>{if(Array.isArray(t)){const{sendRequests:i,otherRequests:o}=e(t),s={method:"wallet_sendMultiCallTransaction",params:[i,!1]},a=0===i.length,u=Math.floor(1e4*Math.random()),c=a?[...o]:[this.request(s),...o];Promise.allSettled(c).then((e=>{if(a)return r(e.map(((e,r)=>{var n;return{id:String((null===(n=t[r])||void 0===n?void 0:n.id)||u+r+1),jsonrpc:"2.0",method:t[r].method,result:"fulfilled"===e.status?e.value:void 0,error:"fulfilled"!==e.status?e.reason:void 0}})));const i=n(t,e);return r(i)})).catch((e=>{throw Le.ethErrors.rpc.internal(null==e?void 0:e.message)}))}else this.request(Object.assign(Object.assign({},t),{id:Number(t.id)})).then(r)}));if("function"!=typeof r)return i;i.then((e=>r(null,e))).catch((e=>r(e)))}))}sendUserOperation(t){return e(this,void 0,void 0,(function*(){return this.request({method:"eth_sendUserOperation",params:[t]})}))}request(r){var n,i,o,s,a,u,c,h;return e(this,void 0,void 0,(function*(){if(Array.isArray(r))return this.sendAsync(r);if(!(null==r?void 0:r.method))throw Le.ethErrors.rpc.invalidRequest();const{blockchainName:e,switchableNetwork:d,sessionKeyEnv:l}=yield t(this,wt,"m",bt).call(this);if(null===(n=this.existedSDK)||void 0===n?void 0:n.isBlocto){if("wallet_switchEthereumChain"===r.method){if(!(null===(o=null===(i=null==r?void 0:r.params)||void 0===i?void 0:i[0])||void 0===o?void 0:o.chainId))throw Le.ethErrors.rpc.invalidParams();return this.existedSDK.request(r).then((()=>{var e,t,n;return this.networkVersion=`${Ct(null===(e=null==r?void 0:r.params)||void 0===e?void 0:e[0].chainId)}`,this.chainId=`0x${Ct(null===(t=null==r?void 0:r.params)||void 0===t?void 0:t[0].chainId).toString(16)}`,this.rpc=null===(n=null==d?void 0:d[this.networkVersion])||void 0===n?void 0:n.rpc_url,null}))}return this.existedSDK.request(r)}switch(r.method){case"eth_chainId":return this.chainId;case"net_version":return this.networkVersion;case"wallet_addEthereumChain":return this.loadSwitchableNetwork((null==r?void 0:r.params)||[]);case"eth_blockNumber":case"web3_clientVersion":case"eth_call":{const e=yield this.handleReadRequests(r);if(!e||e&&!e.result&&e.error){const t=(null===(s=null==e?void 0:e.error)||void 0===s?void 0:s.message)?e.error.message:"Request failed";throw Le.ethErrors.rpc.internal(t)}return"function"==typeof(null==r?void 0:r.callback)&&r.callback(null,e.result),e.result}case"wallet_switchEthereumChain":return this.handleSwitchChain(null===(u=null===(a=null==r?void 0:r.params)||void 0===a?void 0:a[0])||void 0===u?void 0:u.chainId);case"wallet_disconnect":return this.handleDisconnect();case"eth_accounts":return T(l,e)||[]}if(!T(l,e)){const e=null===(c=null==r?void 0:r.params)||void 0===c?void 0:c[0];"eth_requestAccounts"===r.method&&D(e)?yield this.enable(e):yield this.enable()}try{let t=null,n=null;switch(r.method){case"eth_requestAccounts":n=yield this.fetchAccounts();break;case"eth_coinbase":n=null===(h=T(l,e))||void 0===h?void 0:h[0];break;case"eth_signTypedData_v3":case"eth_signTypedData":case"eth_signTypedData_v4":case"personal_sign":n=yield this.handleSign(r);break;case"eth_sign":throw Le.ethErrors.rpc.methodNotFound("Method Not Supported: eth_sign has been disabled");case"eth_sendTransaction":n=yield this.handleSendTransaction(r);break;case"wallet_sendMultiCallTransaction":n=yield this.handleSendBatchTransaction(r);break;case"eth_signTransaction":case"eth_sendRawTransaction":throw Le.ethErrors.rpc.methodNotSupported("Method Not Supported: "+r.method);case"eth_sendUserOperation":n=yield this.handleSendUserOperation(r);break;case"eth_estimateUserOperationGas":case"eth_getUserOperationByHash":case"eth_getUserOperationReceipt":case"eth_supportedEntryPoints":n=yield this.handleBundler(r);break;default:t=yield this.handleReadRequests(r)}if(t&&!t.result&&t.error){const e=t.error.message?t.error.message:"Request failed";throw Le.ethErrors.rpc.internal(e)}return t?t.result:n}catch(e){throw Le.ethErrors.rpc.internal(null==e?void 0:e.message)}}))}bloctoApi(r,n){var i;return e(this,void 0,void 0,(function*(){const{walletServer:e,blockchainName:o,sessionKeyEnv:s}=yield t(this,wt,"m",bt).call(this),a=(null===(i=R(s))||void 0===i?void 0:i.code)||"";if(!a)throw Le.ethErrors.provider.unauthorized();return fetch(`${e}/api/${o}${r}`,Object.assign({headers:{"Content-Type":"application/json","Blocto-Application-Identifier":this.appId,"Blocto-Session-Identifier":a}},n)).then((e=>j(e,s,(()=>{var e;null===(e=this.eventListeners)||void 0===e||e.disconnect.forEach((e=>e(Le.ethErrors.provider.disconnected())))})))).catch((e=>{throw"unsupported_method"===(null==e?void 0:e.error_code)?Le.ethErrors.rpc.methodNotSupported("Method Not Supported: "+e.message):Le.ethErrors.rpc.server({code:-32005,message:`Blocto server error: ${e.message}`})}))}))}responseListener(r,n){return e(this,void 0,void 0,(function*(){const{walletServer:e}=yield t(this,wt,"m",bt).call(this);return new Promise(((t,i)=>b("message",((o,s)=>{const a=o;a.origin===e&&"ETH:FRAME:RESPONSE"===a.data.type&&("APPROVED"===a.data.status&&(s(),w(r),t(a.data[n])),"DECLINED"===a.data.status&&(s(),w(r),"incorrect_session_id"===a.data.errorCode&&this.handleDisconnect(),i(Le.ethErrors.provider.userRejectedRequest(a.data.errorMessage)))),"ETH:FRAME:CLOSE"===a.data.type&&(s(),w(r),i(Le.ethErrors.provider.userRejectedRequest("User declined the request")))}))))}))}setIframe(r,n){return e(this,void 0,void 0,(function*(){if("undefined"==typeof window)throw Le.ethErrors.provider.custom({code:1001,message:"Blocto SDK only works in browser environment"});const{walletServer:e,blockchainName:i}=yield t(this,wt,"m",bt).call(this),o=y(`${e}/${this.appId}/${n||i}${r}`);return m(o),o}))}enable(r){var n;return e(this,void 0,void 0,(function*(){if("undefined"==typeof window)throw Le.ethErrors.provider.custom({code:1001,message:"Blocto SDK only works in browser environment"});const{walletServer:e,blockchainName:o,sessionKeyEnv:s}=yield t(this,wt,"m",bt).call(this);if(null===(n=this.existedSDK)||void 0===n?void 0:n.isBlocto)return this.existedSDK.chainId!==this.chainId&&(yield this.existedSDK.request({method:"wallet_addEthereumChain",params:[{chainId:this.chainId}]}),yield this.existedSDK.request({method:"wallet_switchEthereumChain",params:[{chainId:this.chainId}]}),C(s,o,[this.existedSDK.address])),new Promise(((e,t)=>setTimeout((()=>this.existedSDK.enable().then(e).catch(t)),10)));const a=T(s,o);if(a)return new Promise((e=>{e(a)}));const u=new URLSearchParams;u.set("l6n",window.location.origin),u.set("v",v),u.set("q",`${window.location.pathname}${window.location.search}`);const c=r&&D(r)?`/${r}`:"",h=yield this.setIframe(`/authn${c}?${u.toString()}`);return new Promise(((t,r)=>{b("message",((n,a)=>{var u,c;const d=n;d.origin===e&&("ETH:FRAME:RESPONSE"===d.data.type&&(a(),w(h),null===(u=this.eventListeners)||void 0===u||u.connect.forEach((e=>e({chainId:this.chainId}))),I(s,{code:d.data.code,evm:{[o]:[d.data.addr]}},d.data.exp),(null===(c=d.data)||void 0===c?void 0:c.isAccountChanged)&&postMessage({originChain:i.ETHEREUM,type:"BLOCTO_SDK:ACCOUNT_CHANGED"}),b("message",((e,t)=>{var r,n,o;const s=e,a="BLOCTO_SDK:ACCOUNT_CHANGED"===(null===(r=s.data)||void 0===r?void 0:r.type),u=(null===(n=s.data)||void 0===n?void 0:n.originChain)!==i.ETHEREUM;a&&(null===(o=this.eventListeners)||void 0===o||o.accountsChanged.forEach((e=>e([d.data.addr])))),a&&u&&(this.handleDisconnect(),t())})),t([d.data.addr])),"ETH:FRAME:CLOSE"===d.data.type&&(a(),w(h),r(Le.ethErrors.provider.userRejectedRequest())))}))}))}))}fetchAccounts(){return e(this,void 0,void 0,(function*(){t(this,wt,"m",St).call(this);const{blockchainName:e,sessionKeyEnv:r}=yield t(this,wt,"m",bt).call(this),{accounts:n}=yield this.bloctoApi("/accounts");return C(r,e,n),n}))}handleReadRequests(r){return e(this,void 0,void 0,(function*(){return t(this,wt,"m",St).call(this),fetch(this.rpc,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(Object.assign({id:1,jsonrpc:"2.0"},r))}).then((e=>e.json())).catch((e=>{throw Le.ethErrors.rpc.internal(e)}))}))}handleSign({method:r,params:n}){return e(this,void 0,void 0,(function*(){let e="";if(Array.isArray(n))if("personal_sign"===r)e=De(n[0])?n[0].slice(2):(i=n[0],H.from(i,"utf8").toString("hex"));else if(["eth_signTypedData","eth_signTypedData_v3","eth_signTypedData_v4"].includes(r)){e=n[1];const{domain:t}=JSON.parse(e);if(De(t.chainId))throw Le.ethErrors.rpc.invalidParams(`Provided chainId "${t.chainId}" must be a number`);if(Ct(t.chainId)!==Ct(this.chainId))throw Le.ethErrors.rpc.invalidParams(`Provided chainId "${t.chainId}" must match the active chainId "${Ct(this.chainId)}"`)}var i;t(this,wt,"m",St).call(this);const{signatureId:o}=yield this.bloctoApi("/user-signature",{method:"POST",body:JSON.stringify({method:r,message:e})}),s=yield this.setIframe(`/user-signature/${o}`);return this.responseListener(s,"signature")}))}handleSwitchChain(r){var n,i;return e(this,void 0,void 0,(function*(){if(!r)throw Le.ethErrors.rpc.invalidParams();const{walletServer:e,blockchainName:o,sessionKeyEnv:s,switchableNetwork:a}=yield t(this,wt,"m",bt).call(this),u=null===(n=T(s,o))||void 0===n?void 0:n[0],c=Ct(this.chainId),h=Ct(r);if(c===h)return null;if(!a[h])throw Le.ethErrors.provider.custom({code:4902,message:`Unrecognized chain ID "${h}". Try adding the chain using wallet_addEthereumChain first.`});if(this.networkVersion=`${h}`,this.chainId=`0x${h.toString(16)}`,this.rpc=a[h].rpc_url,this._blocto=Object.assign(Object.assign({},this._blocto),{blockchainName:"",networkType:""}),!u)return null===(i=this.eventListeners)||void 0===i||i.chainChanged.forEach((e=>e(this.chainId))),yield t(this,wt,"m",bt).call(this),null;if(a[h].wallet_web_url!==a[c].wallet_web_url)return this.enable().then((([e])=>{var t;return e!==u&&(null===(t=this.eventListeners)||void 0===t||t.accountsChanged.forEach((t=>t([e])))),this.eventListeners.chainChanged.forEach((e=>e(this.chainId))),null})).catch((e=>{throw this.networkVersion=`${c}`,this.chainId=`0x${c.toString(16)}`,this.rpc=a[c].rpc_url,this._blocto=Object.assign(Object.assign({},this._blocto),{blockchainName:"",networkType:""}),t(this,wt,"m",bt).call(this),e}));const d=yield this.setIframe(`/switch-chain?to=${a[h].name}`,a[c].name);return new Promise(((r,n)=>{b("message",((i,o)=>{var l,f,p,v,g,y,m,b;const E=i;E.origin===e&&("ETH:FRAME:RESPONSE"===E.data.type&&(o(),w(d),(null===(l=E.data)||void 0===l?void 0:l.addr)&&u&&(I(s,{code:null===(f=E.data)||void 0===f?void 0:f.code,evm:{[a[h].name]:[E.data.addr]}},null===(p=E.data)||void 0===p?void 0:p.exp),E.data.addr!==u&&(null===(v=this.eventListeners)||void 0===v||v.accountsChanged.forEach((e=>e([E.data.addr]))))),null===(g=this.eventListeners)||void 0===g||g.chainChanged.forEach((e=>e(this.chainId))),t(this,wt,"m",bt).call(this),r(null)),"ETH:FRAME:CLOSE"===E.data.type&&(o(),w(d),(null===(y=E.data)||void 0===y?void 0:y.hasApprovedSwitchChain)?(null===(m=this.eventListeners)||void 0===m||m.chainChanged.forEach((e=>e(this.chainId))),N(s),null===(b=this.eventListeners)||void 0===b||b.disconnect.forEach((e=>e(Le.ethErrors.provider.disconnected()))),t(this,wt,"m",bt).call(this),r(null)):(this.networkVersion=`${c}`,this.chainId=`0x${c.toString(16)}`,this.rpc=a[c].rpc_url,this._blocto=Object.assign(Object.assign({},this._blocto),{blockchainName:"",networkType:""}),t(this,wt,"m",bt).call(this),n(Le.ethErrors.provider.userRejectedRequest()))))}))}))}))}handleSendTransaction(r){var n;return e(this,void 0,void 0,(function*(){t(this,wt,"m",St).call(this);const{isValid:e,invalidMsg:i}=ke(null===(n=r.params)||void 0===n?void 0:n[0]);if(!e)throw Le.ethErrors.rpc.invalidParams(i);return t(this,wt,"m",_t).call(this,r.params)}))}handleSendBatchTransaction(r){return e(this,void 0,void 0,(function*(){let e,n;t(this,wt,"m",St).call(this),Array.isArray(r.params)&&r.params.length>=2?[e,n]=r.params:(e=r.params,n=!1);const i=n||!1,{isValid:o,invalidMsg:s}=(e=>{if(!Array.isArray(e))return{isValid:!1,invalidMsg:k.INVALID_TRANSACTIONS};for(let t=0;t<e.length;t++){const{isValid:r,invalidMsg:n}=ke(e[t]);if(!r)return{isValid:r,invalidMsg:n}}return{isValid:!0}})(e);if(!o)throw Le.ethErrors.rpc.invalidParams(s);return t(this,wt,"m",_t).call(this,e,i)}))}handleSendUserOperation(r){return e(this,void 0,void 0,(function*(){t(this,wt,"m",St).call(this);const{authorizationId:e}=yield this.bloctoApi("/user-operation",{method:"POST",body:JSON.stringify(r.params)}),n=yield this.setIframe(`/user-operation/${e}`);return this.responseListener(n,"userOpHash")}))}handleBundler(r){return e(this,void 0,void 0,(function*(){return t(this,wt,"m",St).call(this),this.bloctoApi("/rpc/bundler",{method:"POST",body:JSON.stringify(Object.assign({id:1,jsonrpc:"2.0"},r))})}))}handleDisconnect(){var r,n;return e(this,void 0,void 0,(function*(){if(null===(r=this.existedSDK)||void 0===r?void 0:r.isBlocto)return this.existedSDK.request({method:"wallet_disconnect"});const{sessionKeyEnv:e}=yield t(this,wt,"m",bt).call(this);N(e),null===(n=this.eventListeners)||void 0===n||n.disconnect.forEach((e=>e(Le.ethErrors.provider.disconnected())))}))}loadSwitchableNetwork(r){return e(this,void 0,void 0,(function*(){if(null==r?void 0:r.length){const e=r.map((({chainId:e,rpcUrls:r})=>{if(!e)throw Le.ethErrors.rpc.invalidParams("Empty chainId");const n=`${Ct(e)}`;if(this._blocto.switchableNetwork[n])return null;const i=(null==r?void 0:r[0])||o[n];if(!i)throw Le.ethErrors.rpc.invalidParams("rpcUrls required");return t(this,wt,"m",Et).call(this,{chainId:n,rpcUrls:[i]})}));return Promise.all(e).then((()=>null))}throw Le.ethErrors.rpc.invalidParams("Empty networkList")}))}supportChainList(){return e(this,void 0,void 0,(function*(){const e=yield xe().catch((e=>{throw Le.ethErrors.provider.custom({code:1001,message:`Get blocto server failed: ${e.message}`})}));return Object.keys(e).map((t=>{const{display_name:r}=e[t];return{chainId:t,chainName:r}}))}))}on(e,t){var r;(null===(r=this.existedSDK)||void 0===r?void 0:r.isBlocto)&&this.existedSDK.on(e,t),super.on(e,t)}removeListener(e,t){var r;(null===(r=this.existedSDK)||void 0===r?void 0:r.isBlocto)&&this.existedSDK.off(e,t),super.removeListener(e,t)}}wt=new WeakSet,bt=function(){var t,r;return e(this,void 0,void 0,(function*(){if((null===(t=this._blocto)||void 0===t?void 0:t.unloadedNetwork)&&(yield this.loadSwitchableNetwork(this._blocto.unloadedNetwork),delete this._blocto.unloadedNetwork),this._blocto.sessionKeyEnv&&this._blocto.walletServer&&this._blocto.blockchainName&&this._blocto.networkType&&this._blocto.switchableNetwork)return this._blocto;const e=yield xe().catch((e=>{throw Le.ethErrors.provider.custom({code:1001,message:`Get blocto server failed: ${e.message}`})})),{chain_id:n,name:i,network_type:o,blocto_service_environment:u,display_name:c}=null!==(r=e[this.networkVersion])&&void 0!==r?r:{};if(!n)throw Le.ethErrors.provider.unsupportedMethod(`Get support chain failed: ${this.networkVersion} might not be supported yet.`);const h=this.injectedWalletServer||s[u];return this._blocto=Object.assign(Object.assign({},this._blocto),{sessionKeyEnv:a[u],walletServer:h,blockchainName:i,networkType:o,switchableNetwork:Object.assign(Object.assign({},this._blocto.switchableNetwork),{[n]:{name:i,display_name:c,network_type:o,wallet_web_url:h,rpc_url:this.rpc}})}),this._blocto}))},Et=function({chainId:r,rpcUrls:n}){var i;return e(this,void 0,void 0,(function*(){yield t(this,wt,"m",bt).call(this);const e=yield xe().catch((e=>{throw Le.ethErrors.provider.custom({code:1001,message:`Get blocto server failed: ${e.message}`})})),{chain_id:o,name:a,display_name:u,network_type:c,blocto_service_environment:h}=null!==(i=e[r])&&void 0!==i?i:{};if(!o)throw Le.ethErrors.provider.unsupportedMethod(`Get support chain failed: ${r} might not be supported yet.`);const d=s[h];this._blocto.switchableNetwork[o]={name:a,display_name:u,network_type:c,wallet_web_url:d,rpc_url:n[0]}}))},St=function(){var e;if((null===(e=this.existedSDK)||void 0===e?void 0:e.isBlocto)&&Ct(this.existedSDK.chainId)!==Ct(this.chainId))throw Le.ethErrors.provider.chainDisconnected()},_t=function(t,r=!0){return e(this,void 0,void 0,(function*(){const{authorizationId:e}=yield this.bloctoApi("/authz",{method:"POST",body:JSON.stringify([t,r])}),n=new URLSearchParams;n.set("l6n",window.location.origin),n.set("q",`${window.location.pathname}${window.location.search}`);const i=yield this.setIframe(`/authz/${e}?${n.toString()}`);return this.responseListener(i,"txHash")}))};class jt extends g{get existedSDK(){if("undefined"!=typeof window)return window.bloctoAptos}constructor({chainId:e,server:t,appId:n}){super(),this.publicKey=[],this.authKey="",this.off=this.removeListener,r(e,"'chainId' is required"),r(n,"It is necessary to interact with Blocto wallet via your app id. Please visit https://developers.blocto.app for more details."),this.chainId=e,this.networkName=d[e],this.api=l[e],this.sessionKey=u[e];const i=c[e];this.appId=n||p,this.server=t||i||""}get publicAccount(){var e;return{address:(null===(e=P(this.sessionKey,i.APTOS))||void 0===e?void 0:e[0])||null,publicKey:this.publicKey.length?this.publicKey:null,authKey:null,minKeysRequired:2}}network(){return e(this,void 0,void 0,(function*(){return{name:this.networkName,api:this.api,chainId:this.chainId.toString()}}))}isConnected(){var t;return e(this,void 0,void 0,(function*(){return!!(null===(t=P(this.sessionKey,i.APTOS))||void 0===t?void 0:t.length)}))}signTransaction(t){var r;return e(this,void 0,void 0,(function*(){if(this.existedSDK)return this.existedSDK.signTransaction(t);if((yield this.isConnected())||(yield this.connect()),!(null===(r=P(this.sessionKey,i.APTOS))||void 0===r?void 0:r.length))throw new Error("Fail to get account");throw new Error("signTransaction method not supported.")}))}disconnect(){var t;return e(this,void 0,void 0,(function*(){var e,r;this.existedSDK?yield this.existedSDK.disconnect():(e=this.sessionKey,r=i.APTOS,I(e,{accounts:{[r]:void 0}}),null===(t=this.eventListeners)||void 0===t||t.disconnect.forEach((e=>e({code:4900,message:"Wallet disconnected"}))))}))}signAndSubmitTransaction(t,r={}){var n,o;return e(this,void 0,void 0,(function*(){if(this.existedSDK)return this.existedSDK.signAndSubmitTransaction(t,r);if((yield this.isConnected())||(yield this.connect()),!(null===(n=P(this.sessionKey,i.APTOS))||void 0===n?void 0:n.length))throw new Error("Fail to get account");const e=(null===(o=R(this.sessionKey))||void 0===o?void 0:o.code)||"",{authorizationId:s}=yield fetch(`${this.server}/api/aptos/authz`,{method:"POST",headers:{"Content-Type":"application/json","Blocto-Application-Identifier":this.appId,"Blocto-Session-Identifier":e},body:JSON.stringify(Object.assign(Object.assign({},t),r))}).then((e=>j(e,this.sessionKey)));if("undefined"==typeof window)throw new Error("Currently only supported in browser");const a=y(`${this.server}/${this.appId}/aptos/authz/${s}`);return m(a),new Promise(((e,t)=>b("message",((r,n)=>{const i=r;i.origin===this.server&&"APTOS:FRAME:RESPONSE"===i.data.type&&("APPROVED"===i.data.status&&(n(),w(a),e({hash:i.data.txHash})),"DECLINED"===i.data.status&&(n(),w(a),"incorrect_session_id"===i.data.errorCode&&this.disconnect(),t(new Error(i.data.errorMessage))))}))))}))}signMessage(t){var r,n;return e(this,void 0,void 0,(function*(){const e=(e=>{var t,r;const n=Object.assign({},e),{message:i,nonce:o,address:s,application:a,chainId:u}=e;return"string"!=typeof i&&(n.message=null!==(t=String(i))&&void 0!==t?t:""),"string"!=typeof o&&(n.nonce=null!==(r=String(o))&&void 0!==r?r:""),s&&"boolean"!=typeof s&&(n.address=!!s),a&&"boolean"!=typeof a&&(n.application=!!a),u&&"boolean"!=typeof u&&(n.chainId=!!u),n})(t);if(this.existedSDK)return this.existedSDK.signMessage(e);if((yield this.isConnected())||(yield this.connect()),!(null===(r=P(this.sessionKey,i.APTOS))||void 0===r?void 0:r.length))throw new Error("Fail to get account");if("undefined"==typeof window)throw new Error("Currently only supported in browser");const o=(null===(n=R(this.sessionKey))||void 0===n?void 0:n.code)||"",{signatureId:s}=yield fetch(`${this.server}/api/aptos/user-signature`,{method:"POST",headers:{"Content-Type":"application/json","Blocto-Application-Identifier":this.appId,"Blocto-Session-Identifier":o},body:JSON.stringify(e)}).then((e=>j(e,this.sessionKey))),a=y(`${this.server}/${this.appId}/aptos/user-signature/${s}`);return m(a),new Promise(((e,t)=>b("message",((r,n)=>{const i=r;i.origin===this.server&&"APTOS:FRAME:RESPONSE"===i.data.type&&("APPROVED"===i.data.status&&(n(),w(a),e(i.data)),"DECLINED"===i.data.status&&(n(),w(a),"incorrect_session_id"===i.data.errorCode&&this.disconnect(),t(new Error(i.data.errorMessage))))}))))}))}connect(){return e(this,void 0,void 0,(function*(){return this.existedSDK?new Promise(((e,t)=>setTimeout((()=>this.existedSDK.connect().then(e).catch(t)),10))):new Promise(((t,r)=>{var n,o;if("undefined"==typeof window)return r("Currently only supported in browser");if(null===(n=P(this.sessionKey,i.APTOS))||void 0===n?void 0:n.length)return t({address:(null===(o=P(this.sessionKey,i.APTOS))||void 0===o?void 0:o[0])||null,publicKey:this.publicKey,authKey:null,minKeysRequired:2});const s=encodeURIComponent(window.location.origin),a=y(`${this.server}/${this.appId}/aptos/authn?l6n=${s}&v=${v}}`);m(a),b("message",((n,o)=>e(this,void 0,void 0,(function*(){var e,s,u,c;const h=n;if(h.origin===this.server){if("APTOS:FRAME:RESPONSE"===h.data.type){if(o(),w(a),I(this.sessionKey,{code:h.data.code,accounts:{[i.APTOS]:[h.data.addr]}},h.data.exp),(null===(e=h.data)||void 0===e?void 0:e.isAccountChanged)&&postMessage({originChain:i.APTOS,type:"BLOCTO_SDK:ACCOUNT_CHANGED"}),b("message",((e,t)=>{var r,n;const o=e;"BLOCTO_SDK:ACCOUNT_CHANGED"===(null===(r=o.data)||void 0===r?void 0:r.type)&&(null===(n=o.data)||void 0===n?void 0:n.originChain)!==i.APTOS&&(this.disconnect(),t())})),!(null===(s=P(this.sessionKey,i.APTOS))||void 0===s?void 0:s.length))return r();try{const{public_keys:e}=yield fetch(`${this.server}/blocto/aptos/accounts/${null===(u=P(this.sessionKey,i.APTOS))||void 0===u?void 0:u[0]}`).then((e=>e.json()));this.publicKey=e||[],t({address:(null===(c=P(this.sessionKey,i.APTOS))||void 0===c?void 0:c[0])||"",publicKey:this.publicKey,authKey:null,minKeysRequired:2})}catch(e){return r(h)}}"APTOS:FRAME:CLOSE"===h.data.type&&(o(),w(a),r(new Error("User declined the login request")))}}))))}))}))}fetchAddress(){var t;return e(this,void 0,void 0,(function*(){const e=(null===(t=R(this.sessionKey))||void 0===t?void 0:t.code)||"",{accounts:r}=yield fetch(`${this.server}/api/aptos/accounts`,{headers:{"Blocto-Application-Identifier":this.appId,"Blocto-Session-Identifier":e}}).then((e=>j(e,this.sessionKey)));var n,o;return n=this.sessionKey,o=i.APTOS,I(n,{accounts:{[o]:r}}),(null==r?void 0:r[0])||""}))}on(e,t){this.existedSDK&&this.existedSDK.on(e,t),super.on(e,t)}removeListener(e,t){this.existedSDK&&this.existedSDK.off(e,t),super.removeListener(e,t)}}return class{constructor({appId:e,ethereum:t,aptos:r}){t&&(this.ethereum=new Nt(Object.assign(Object.assign({},t),{appId:e}))),r&&(this.aptos=new jt(Object.assign(Object.assign({},r),{appId:e})))}}}));
