{"name": "@blocto/sdk", "version": "0.10.2", "repository": "**************:portto/blocto-sdk.git", "author": "Chiaki.C", "license": "MIT", "main": "dist/blocto-sdk.js", "module": "dist/blocto-sdk.module.js", "browser": "dist/blocto-sdk.umd.js", "types": "dist/blocto-sdk.d.ts", "type": "module", "exports": {"require": "./dist/blocto-sdk.js", "import": "./dist/blocto-sdk.module.js", "types": "./dist/blocto-sdk.d.ts"}, "files": ["/dist/**/!(*.html)"], "scripts": {"build": "NODE_ENV=production rollup -c", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint --ext .js,.ts src/", "test": "jest", "prepublishOnly": "npm-run-all clean build", "rollup-watch": "NODE_ENV=development rollup -c rollup.config.dev.js --watch", "serve": "live-server --port=7777 --https=dev-cert/index.js dev", "start": "npm-run-all --parallel rollup-watch serve"}, "dependencies": {"buffer": "^6.0.3", "eip1193-provider": "^1.0.1", "js-sha3": "^0.8.0"}, "devDependencies": {"@babel/core": "^7.22.1", "@babel/eslint-parser": "^7.21.8", "@babel/eslint-plugin": "^7.14.5", "@babel/plugin-transform-runtime": "^7.22.4", "@babel/preset-env": "^7.22.4", "@babel/runtime": "^7.22.3", "@rollup/plugin-alias": "^5.0.0", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-commonjs": "^25.0.0", "@rollup/plugin-node-resolve": "^15.1.0", "@rollup/plugin-typescript": "^11.1.1", "@types/jest": "^29.5.2", "@typescript-eslint/eslint-plugin": "^5.59.9", "@typescript-eslint/parser": "^5.59.9", "aptos": "^1.15.0", "babel-jest": "^29.5.0", "babel-plugin-module-resolver": "^5.0.0", "eslint": "^8.42.0", "eslint-import-resolver-babel-module": "^5.3.1", "eslint-plugin-import": "^2.23.4", "eth-rpc-errors": "^4.0.3", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "jest-fetch-mock": "^3.0.3", "live-server": "^1.2.1", "rimraf": "^5.0.1", "rollup": "^3.23.1", "rollup-plugin-dts": "^5.3.0", "rollup-plugin-json": "^4.0.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-polyfill-node": "^0.12.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-version-injector": "^1.3.3", "rollup-plugin-visualizer": "^5.5.4", "ts-jest": "^29.1.0", "tslib": "^2.5.3", "typescript": "~5.1.3", "yarn-run-all": "^3.1.1"}, "peerDependencies": {"aptos": "^1.3.14"}, "peerDependenciesMeta": {"aptos": {"optional": true}}, "keywords": ["portto", "blocto", "ethereum", "aptos", "wallet", "sdk", "web3", "web3.js", "javascript", "typescript", "dapp", "react", "vue"]}