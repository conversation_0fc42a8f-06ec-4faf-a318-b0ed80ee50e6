import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/pro.fintoken/index.js
var wallet = {
  id: "pro.fintoken",
  name: "FINTOKEN",
  homepage: "https://fintoken.pro/",
  image_id: "420ababa-3c29-4711-4487-84b93bfa5900",
  app: {
    browser: null,
    ios: "https://apps.apple.com/us/app/fintoken-web3-crypto-wallet/id6447503215",
    android: "https://play.google.com/store/apps/details?id=com.digitalasset.fintoken&pli=1",
    mac: null,
    windows: null,
    linux: null,
    chrome: null,
    firefox: null,
    safari: null,
    edge: null,
    opera: null
  },
  rdns: null,
  mobile: {
    native: "fintoken://",
    universal: "https://ios.fintoken.pro/app"
  },
  desktop: {
    native: null,
    universal: null
  }
};
export {
  wallet
};
//# sourceMappingURL=pro-U7YAM3EB.js.map
