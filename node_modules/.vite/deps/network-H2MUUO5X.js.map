{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/network.trustkeys/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"network.trustkeys\",\n  name: \"TrustKeys Web3 SocialFi\",\n  homepage: \"https://trustkeys.network\",\n  image_id: \"35644c6b-c6f3-4e45-b68b-e888c21afd00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/vn/app/trustkeys-web3-socialfi/id1601968967\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.trustkeysnetwork\",\n    mac: \"\",\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"TKwc://\",\n    universal: \"https://trustkeys.network/\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}