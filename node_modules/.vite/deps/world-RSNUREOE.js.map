{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/world.ixo/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"world.ixo\",\n  name: \"Impact Wallet\",\n  homepage: \"https://www.ixo.world/\",\n  image_id: \"afc85418-2ca6-46cf-cfb9-daf6bc43e400\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/app/impacts-x/id6444948058\",\n    android: \"https://play.google.com/store/apps/details?id=com.ixo.mobile\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"impactsx://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}