{"version": 3, "sources": ["../../thirdweb/src/extensions/erc721/__generated__/IERC721A/read/isApprovedForAll.ts"], "sourcesContent": ["import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"isApprovedForAll\" function.\n */\nexport type IsApprovedForAllParams = {\n  owner: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"owner\" }>;\n  operator: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"operator\" }>;\n};\n\nexport const FN_SELECTOR = \"0xe985e9c5\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"address\",\n    name: \"owner\",\n  },\n  {\n    type: \"address\",\n    name: \"operator\",\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"bool\",\n  },\n] as const;\n\n/**\n * Checks if the `isApprovedForAll` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `isApprovedForAll` method is supported.\n * @extension ERC721\n * @example\n * ```ts\n * import { isIsApprovedForAllSupported } from \"thirdweb/extensions/erc721\";\n * const supported = isIsApprovedForAllSupported([\"0x...\"]);\n * ```\n */\nexport function isIsApprovedForAllSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"isApprovedForAll\" function.\n * @param options - The options for the isApprovedForAll function.\n * @returns The encoded ABI parameters.\n * @extension ERC721\n * @example\n * ```ts\n * import { encodeIsApprovedForAllParams } from \"thirdweb/extensions/erc721\";\n * const result = encodeIsApprovedForAllParams({\n *  owner: ...,\n *  operator: ...,\n * });\n * ```\n */\nexport function encodeIsApprovedForAllParams(options: IsApprovedForAllParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.owner, options.operator]);\n}\n\n/**\n * Encodes the \"isApprovedForAll\" function into a Hex string with its parameters.\n * @param options - The options for the isApprovedForAll function.\n * @returns The encoded hexadecimal string.\n * @extension ERC721\n * @example\n * ```ts\n * import { encodeIsApprovedForAll } from \"thirdweb/extensions/erc721\";\n * const result = encodeIsApprovedForAll({\n *  owner: ...,\n *  operator: ...,\n * });\n * ```\n */\nexport function encodeIsApprovedForAll(options: IsApprovedForAllParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeIsApprovedForAllParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Decodes the result of the isApprovedForAll function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC721\n * @example\n * ```ts\n * import { decodeIsApprovedForAllResult } from \"thirdweb/extensions/erc721\";\n * const result = decodeIsApprovedForAllResultResult(\"...\");\n * ```\n */\nexport function decodeIsApprovedForAllResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"isApprovedForAll\" function on the contract.\n * @param options - The options for the isApprovedForAll function.\n * @returns The parsed result of the function call.\n * @extension ERC721\n * @example\n * ```ts\n * import { isApprovedForAll } from \"thirdweb/extensions/erc721\";\n *\n * const result = await isApprovedForAll({\n *  contract,\n *  owner: ...,\n *  operator: ...,\n * });\n *\n * ```\n */\nexport async function isApprovedForAll(\n  options: BaseTransactionOptions<IsApprovedForAllParams>,\n) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [options.owner, options.operator],\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBO,IAAM,cAAc;AAC3B,IAAM,YAAY;EAChB;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;;AAGV,IAAM,aAAa;EACjB;IACE,MAAM;;;AAeJ,SAAU,4BAA4B,oBAA4B;AACtE,SAAO,aAAa;IAClB;IACA,QAAQ,CAAC,aAAa,WAAW,UAAU;GAC5C;AACH;AAgBM,SAAU,6BAA6B,SAA+B;AAC1E,SAAO,oBAAoB,WAAW,CAAC,QAAQ,OAAO,QAAQ,QAAQ,CAAC;AACzE;AAgBM,SAAU,uBAAuB,SAA+B;AAGpE,SAAQ,cACN,6BAA6B,OAAO,EAAE,MACpC,CAAC;AAEP;AAaM,SAAU,6BAA6B,QAAW;AACtD,SAAO,oBAAoB,YAAY,MAAM,EAAE,CAAC;AAClD;AAmBA,eAAsB,iBACpB,SAAuD;AAEvD,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAAC,aAAa,WAAW,UAAU;IAC3C,QAAQ,CAAC,QAAQ,OAAO,QAAQ,QAAQ;GACzC;AACH;", "names": []}