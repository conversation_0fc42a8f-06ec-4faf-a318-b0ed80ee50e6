import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/world.dosi.vault/index.js
var wallet = {
  id: "world.dosi.vault",
  name: "DOSI Vault",
  homepage: "https://vault.dosi.world/",
  image_id: "0a0d223e-6bf7-4e12-a5b4-1720deb02000",
  app: {
    browser: "https://vault.dosi.world/",
    ios: "https://apps.apple.com/kr/app/dosi-vault/id1664013594",
    android: "https://play.google.com/store/apps/details?id=world.dosi.vault",
    mac: null,
    windows: null,
    linux: null,
    chrome: "https://chrome.google.com/webstore/detail/dosi-vault/blpiicikpimmklhoiploliaenjmecabp?hl=en",
    firefox: "https://addons.mozilla.org/ko/firefox/addon/dosi-vault/?utm_source=addons.mozilla.org&utm_medium=referral&utm_content=search",
    safari: null,
    edge: null,
    opera: null
  },
  rdns: null,
  mobile: {
    native: "app.dosivault://",
    universal: "https://dosivault.page.link/qL6j"
  },
  desktop: {
    native: null,
    universal: "https://vault.dosi.world/"
  }
};
export {
  wallet
};
//# sourceMappingURL=world.dosi-66NWFVFI.js.map
