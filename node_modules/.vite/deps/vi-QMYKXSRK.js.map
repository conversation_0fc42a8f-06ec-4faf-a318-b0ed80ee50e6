{"version": 3, "sources": ["../../thirdweb/src/react/web/ui/ConnectWallet/locale/vi.ts"], "sourcesContent": ["import type { ConnectLocale } from \"./types.js\";\n\nconst connectLocaleVi: ConnectLocale = {\n  id: \"vi_VN\",\n  signIn: \"Đăng nhập\",\n  defaultButtonTitle: \"Kết nối ví\",\n  connecting: \"Đang kết nối\",\n  switchNetwork: \"Chuyển mạng\",\n  switchingNetwork: \"Đang chuyển mạng\",\n  defaultModalTitle: \"Đăng nhập\",\n  recommended: \"Khuyên dùng\",\n  installed: \"Đã cài đặt\",\n  buy: \"Mua\",\n  continueAsGuest: \"Continue as guest\",\n  connectAWallet: \"Kết nối ví\",\n  newToWallets: \"Tìm hiểu về ví điện tử\",\n  getStarted: \"B<PERSON><PERSON> đầu\",\n  guest: \"Guest\",\n  send: \"<PERSON><PERSON><PERSON>\",\n  receive: \"Nhận\",\n  currentNetwork: \"Mạng lưới hiện tại\",\n  switchAccount: \"Chuyển tài khoản\",\n  requestTestnetFunds: \"Nhận Testnet Funds\",\n  transactions: \"Transactions\",\n  payTransactions: \"Fiat Transactions\",\n  walletTransactions: \"Wallet Transactions\",\n  viewAllTransactions: \"Tất cả giao dịch\",\n  backupWallet: \"Sao lưu ví\",\n  guestWalletWarning:\n    \"Đây là ví tạm thời cho phiên đăng nhập của bạn. Vui lòng sao lưu ví nếu bạn không muốn dánh mất thông tin\",\n  switchTo: \"Đổi qua ví\", // Used in \"Switch to <Wallet-Name>\"\n  connectedToSmartWallet: \"Ví Thông Minh\",\n  confirmInWallet: \"Xác nhận bằng ví\",\n  disconnectWallet: \"Ngắt kết nối ví\",\n  copyAddress: \"Sao chép địa chỉ\",\n  personalWallet: \"Ví cá nhân\",\n  smartWallet: \"Ví Thông Minh\",\n  or: \"hoặc\",\n  goBackButton: \"Quay lại\",\n  passkeys: {\n    title: \"Khóa truy cập\",\n    linkPasskey: \"Liên kết khóa truy cập\",\n  },\n  welcomeScreen: {\n    defaultTitle: \"Cánh cổng dẫn tới thế giới phi tập trung\",\n    defaultSubtitle: \"Kết nối ví để bắt đầu\",\n  },\n  agreement: {\n    prefix: \"Bằng việc kết nối, bạn đồng ý với\",\n    termsOfService: \"Điều khoản dịch vụ\",\n    and: \"và\",\n    privacyPolicy: \"Chính sách bảo mật\",\n  },\n  networkSelector: {\n    title: \"Chọn mạng lưới\",\n    mainnets: \"Mainnets\",\n    testnets: \"Testnets\",\n    allNetworks: \"Tất cả\",\n    addCustomNetwork: \"Thêm mạng lưới\",\n    inputPlaceholder: \"Tìm theo tên hoặc ID\",\n    categoryLabel: {\n      recentlyUsed: \"Gần đây\",\n      popular: \"Thông dụng\",\n      others: \"Tất cả mạng lưới\",\n    },\n    loading: \"Đang tải\",\n    failedToSwitch: \"Không thể đổi mạng lưới\",\n  },\n  receiveFundsScreen: {\n    title: \"Nhận tiền\",\n    instruction: \"Sử dụng địa chỉ ví này để nhận tiền\",\n  },\n  sendFundsScreen: {\n    title: \"Gửi tiền\",\n    submitButton: \"Gửi\",\n    token: \"Token\",\n    sendTo: \"Gửi tới\",\n    amount: \"Số lượng\",\n    successMessage: \"Giao dịch thành công\",\n    invalidAddress: \"Địa chỉ không hợp lệ\",\n    noTokensFound: \"Không tìm thấy token\",\n    searchToken: \"Tìm kiếm hoặc dán địa chỉ token\",\n    transactionFailed: \"Giao dịch thất baị\",\n    transactionRejected: \"Giao dịch bị huỷ\",\n    insufficientFunds: \"Không đủ vốn\",\n    selectTokenTitle: \"Chọn Token\",\n    sending: \"Đang gửi\",\n  },\n  signatureScreen: {\n    instructionScreen: {\n      title: \"Đăng nhập\",\n      instruction: \"Kí vào yêu cầu tin nhắn trong ví của bạn để tiếp tục\",\n      signInButton: \"Đăng nhập\",\n      disconnectWallet: \"Ngắt kết nối ví\",\n    },\n    signingScreen: {\n      title: \"Đang đăng nhập\",\n      prompt: \"Ký vào yêu cầu tin nhắn trong ví của bạn\",\n      promptForSafe:\n        \"Kí vào yêu cầu tin nhắn trong ví của bạn và chấp nhận giao dịch trong ứng dụng Safe\",\n      approveTransactionInSafe: \"Xác nhận giao dịch bằng ví Safe\",\n      tryAgain: \"Thử lại\",\n      failedToSignIn: \"Đăng nhập thất bại\",\n      inProgress: \"Đang đợi xác nhận\",\n    },\n  },\n  manageWallet: {\n    title: \"Quản lý ví\",\n    linkedProfiles: \"Tài khoản\",\n    linkProfile: \"Thêm tính năng xác thực\",\n    connectAnApp: \"Kết nối ứng dụng\",\n    exportPrivateKey: \"Sao lưu private key\",\n  },\n  viewFunds: {\n    title: \"Tài sản\",\n    viewTokens: \"Tokens\",\n    viewNFTs: \"NFTs\",\n    viewAssets: \"Tài sản\",\n  },\n};\n\nexport default connectLocaleVi;\n"], "mappings": ";;;AAEA,IAAM,kBAAiC;EACrC,IAAI;EACJ,QAAQ;EACR,oBAAoB;EACpB,YAAY;EACZ,eAAe;EACf,kBAAkB;EAClB,mBAAmB;EACnB,aAAa;EACb,WAAW;EACX,KAAK;EACL,iBAAiB;EACjB,gBAAgB;EAChB,cAAc;EACd,YAAY;EACZ,OAAO;EACP,MAAM;EACN,SAAS;EACT,gBAAgB;EAChB,eAAe;EACf,qBAAqB;EACrB,cAAc;EACd,iBAAiB;EACjB,oBAAoB;EACpB,qBAAqB;EACrB,cAAc;EACd,oBACE;EACF,UAAU;;EACV,wBAAwB;EACxB,iBAAiB;EACjB,kBAAkB;EAClB,aAAa;EACb,gBAAgB;EAChB,aAAa;EACb,IAAI;EACJ,cAAc;EACd,UAAU;IACR,OAAO;IACP,aAAa;;EAEf,eAAe;IACb,cAAc;IACd,iBAAiB;;EAEnB,WAAW;IACT,QAAQ;IACR,gBAAgB;IAChB,KAAK;IACL,eAAe;;EAEjB,iBAAiB;IACf,OAAO;IACP,UAAU;IACV,UAAU;IACV,aAAa;IACb,kBAAkB;IAClB,kBAAkB;IAClB,eAAe;MACb,cAAc;MACd,SAAS;MACT,QAAQ;;IAEV,SAAS;IACT,gBAAgB;;EAElB,oBAAoB;IAClB,OAAO;IACP,aAAa;;EAEf,iBAAiB;IACf,OAAO;IACP,cAAc;IACd,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,gBAAgB;IAChB,gBAAgB;IAChB,eAAe;IACf,aAAa;IACb,mBAAmB;IACnB,qBAAqB;IACrB,mBAAmB;IACnB,kBAAkB;IAClB,SAAS;;EAEX,iBAAiB;IACf,mBAAmB;MACjB,OAAO;MACP,aAAa;MACb,cAAc;MACd,kBAAkB;;IAEpB,eAAe;MACb,OAAO;MACP,QAAQ;MACR,eACE;MACF,0BAA0B;MAC1B,UAAU;MACV,gBAAgB;MAChB,YAAY;;;EAGhB,cAAc;IACZ,OAAO;IACP,gBAAgB;IAChB,aAAa;IACb,cAAc;IACd,kBAAkB;;EAEpB,WAAW;IACT,OAAO;IACP,YAAY;IACZ,UAAU;IACV,YAAY;;;AAIhB,IAAA,aAAe;", "names": []}