{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.shido/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.shido\",\n  name: \"<PERSON><PERSON>\",\n  homepage: \"https://www.shido.io/\",\n  image_id: \"673b4fdc-8c65-41e0-d0f8-c2eb6c8e5500\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/shido-app/id6473452165\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.shido.wallet&hl=en&gl=US&pli=1\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"shido://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}