{"version": 3, "sources": ["../../mipd/src/utils.ts", "../../mipd/src/store.ts", "../../thirdweb/src/utils/web/isMobile.ts", "../../thirdweb/src/utils/web/openWindow.ts", "../../thirdweb/src/wallets/coinbase/coinbase-wallet.ts", "../../thirdweb/src/wallets/in-app/web/ecosystem.ts", "../../thirdweb/src/wallets/in-app/web/in-app.ts", "../../thirdweb/src/wallets/injected/index.ts", "../../thirdweb/src/wallets/smart/smart-wallet.ts", "../../thirdweb/src/wallets/create-wallet.ts", "../../thirdweb/src/wallets/injected/mipdStore.ts"], "sourcesContent": ["import type { EIP1193Provider } from './register.js'\nimport type {\n  EIP6963AnnounceProviderEvent,\n  EIP6963ProviderDetail,\n} from './types.js'\n\n////////////////////////////////////////////////////////////////////////////\n// Announce Provider\n\nexport type AnnounceProviderParameters = EIP6963ProviderDetail<\n  EIP1193Provider,\n  string\n>\nexport type AnnounceProviderReturnType = () => void\n\n/**\n * Announces an EIP-1193 Provider.\n */\nexport function announceProvider(\n  detail: AnnounceProviderParameters,\n): AnnounceProviderReturnType {\n  const event: CustomEvent<EIP6963ProviderDetail> = new CustomEvent(\n    'eip6963:announceProvider',\n    { detail: Object.freeze(detail) },\n  )\n\n  window.dispatchEvent(event)\n\n  const handler = () => window.dispatchEvent(event)\n  window.addEventListener('eip6963:requestProvider', handler)\n  return () => window.removeEventListener('eip6963:requestProvider', handler)\n}\n\n////////////////////////////////////////////////////////////////////////////\n// Request Providers\n\nexport type RequestProvidersParameters = (\n  providerDetail: EIP6963ProviderDetail,\n) => void\nexport type RequestProvidersReturnType = (() => void) | undefined\n\n/**\n * Watches for EIP-1193 Providers to be announced.\n */\nexport function requestProviders(\n  listener: RequestProvidersParameters,\n): RequestProvidersReturnType {\n  if (typeof window === 'undefined') return\n  const handler = (event: EIP6963AnnounceProviderEvent) =>\n    listener(event.detail)\n\n  window.addEventListener('eip6963:announceProvider', handler)\n\n  window.dispatchEvent(new CustomEvent('eip6963:requestProvider'))\n\n  return () => window.removeEventListener('eip6963:announceProvider', handler)\n}\n", "import type { Rdns } from './register.js'\nimport type { EIP6963ProviderDetail } from './types.js'\nimport { requestProviders } from './utils.js'\n\nexport type Listener = (\n  providerDetails: readonly EIP6963ProviderDetail[],\n  meta?:\n    | {\n        added?: readonly EIP6963ProviderDetail[] | undefined\n        removed?: readonly EIP6963ProviderDetail[] | undefined\n      }\n    | undefined,\n) => void\n\nexport type Store = {\n  /**\n   * Clears the store, including all provider details.\n   */\n  clear(): void\n  /**\n   * Destroys the store, including all provider details and listeners.\n   */\n  destroy(): void\n  /**\n   * Finds a provider detail by its RDNS (Reverse Domain Name Identifier).\n   */\n  findProvider(args: { rdns: Rdns }): EIP6963ProviderDetail | undefined\n  /**\n   * Returns all provider details that have been emitted.\n   */\n  getProviders(): readonly EIP6963ProviderDetail[]\n  /**\n   * Resets the store, and emits an event to request provider details.\n   */\n  reset(): void\n  /**\n   * Subscribes to emitted provider details.\n   */\n  subscribe(\n    listener: Listener,\n    args?: { emitImmediately?: boolean | undefined } | undefined,\n  ): () => void\n\n  /**\n   * @internal\n   * Current state of listening listeners.\n   */\n  _listeners(): Set<Listener>\n}\n\nexport function createStore(): Store {\n  const listeners: Set<Listener> = new Set()\n  let providerDetails: readonly EIP6963ProviderDetail[] = []\n\n  const request = () =>\n    requestProviders((providerDetail) => {\n      if (\n        providerDetails.some(\n          ({ info }) => info.uuid === providerDetail.info.uuid,\n        )\n      )\n        return\n\n      providerDetails = [...providerDetails, providerDetail]\n      listeners.forEach((listener) =>\n        listener(providerDetails, { added: [providerDetail] }),\n      )\n    })\n  let unwatch = request()\n\n  return {\n    _listeners() {\n      return listeners\n    },\n    clear() {\n      listeners.forEach((listener) =>\n        listener([], { removed: [...providerDetails] }),\n      )\n      providerDetails = []\n    },\n    destroy() {\n      this.clear()\n      listeners.clear()\n      unwatch?.()\n    },\n    findProvider({ rdns }) {\n      return providerDetails.find(\n        (providerDetail) => providerDetail.info.rdns === rdns,\n      )\n    },\n    getProviders() {\n      return providerDetails\n    },\n    reset() {\n      this.clear()\n      unwatch?.()\n      unwatch = request()\n    },\n    subscribe(listener, { emitImmediately } = {}) {\n      listeners.add(listener)\n      if (emitImmediately) listener(providerDetails, { added: providerDetails })\n      return () => listeners.delete(listener)\n    },\n  }\n}\n", "import { detectOS } from \"../detect-platform.js\";\n\n/**\n * @internal\n */\nexport function isAndroid(): boolean {\n  // can only detect if useragent is defined\n  if (typeof navigator === \"undefined\") {\n    return false;\n  }\n  const os = detectOS(navigator.userAgent);\n  return os ? os.toLowerCase().includes(\"android\") : false;\n}\n\n/**\n * @internal\n */\nexport function isIOS(): boolean {\n  // can only detect if useragent is defined\n  if (typeof navigator === \"undefined\") {\n    return false;\n  }\n  const os = detectOS(navigator.userAgent);\n  return os\n    ? os.toLowerCase().includes(\"ios\") ||\n        (os.toLowerCase().includes(\"mac\") && navigator.maxTouchPoints > 1)\n    : false;\n}\n\n/**\n * @internal\n */\nexport function isMobile(): boolean {\n  return isAndroid() || isIOS();\n}\n", "/**\n * @internal\n */\nexport function openWindow(uri: string) {\n  const isInsideIframe = window !== window.top;\n  if (isInsideIframe) {\n    window.open(uri);\n  } else {\n    if (uri.startsWith(\"http\")) {\n      // taken from for https://github.com/rainbow-me/rainbowkit/\n\n      // Using 'window.open' causes issues on iOS in non-Safari browsers and\n      // WebViews where a blank tab is left behind after connecting.\n      // This is especially bad in some WebView scenarios (e.g. following a\n      // link from Twitter) where the user doesn't have any mechanism for\n      // closing the blank tab.\n      // For whatever reason, links with a target of \"_blank\" don't suffer\n      // from this problem, and programmatically clicking a detached link\n      // element with the same attributes also avoids the issue.\n\n      const link = document.createElement(\"a\");\n      link.href = uri;\n      link.target = \"_blank\";\n      link.rel = \"noreferrer noopener\";\n      link.click();\n    } else {\n      window.location.href = uri;\n    }\n  }\n}\n", "/**\n * internal helper functions\n */\n\nimport type { ProviderInterface } from \"@coinbase/wallet-sdk\";\nimport { trackConnect } from \"../../analytics/track/connect.js\";\nimport type { Chain } from \"../../chains/types.js\";\nimport { getCachedChainIfExists } from \"../../chains/utils.js\";\nimport { COINBASE } from \"../constants.js\";\nimport type { Account, Wallet } from \"../interfaces/wallet.js\";\nimport { createWalletEmitter } from \"../wallet-emitter.js\";\nimport type { CreateWalletArgs } from \"../wallet-types.js\";\n\n/**\n * @internal\n */\nexport function coinbaseWalletSDK(args: {\n  createOptions?: CreateWalletArgs<typeof COINBASE>[1];\n  providerFactory: () => Promise<ProviderInterface>;\n  onConnectRequested?: (provider: ProviderInterface) => Promise<void>;\n}): Wallet<typeof COINBASE> {\n  const { createOptions } = args;\n  const emitter = createWalletEmitter<typeof COINBASE>();\n  let account: Account | undefined = undefined;\n  let chain: Chain | undefined = undefined;\n\n  function reset() {\n    account = undefined;\n    chain = undefined;\n  }\n\n  let handleDisconnect = async () => {};\n\n  let handleSwitchChain = async (newChain: Chain) => {\n    chain = newChain;\n  };\n\n  const unsubscribeChainChanged = emitter.subscribe(\n    \"chainChanged\",\n    (newChain) => {\n      chain = newChain;\n    },\n  );\n\n  const unsubscribeDisconnect = emitter.subscribe(\"disconnect\", () => {\n    reset();\n    unsubscribeChainChanged();\n    unsubscribeDisconnect();\n  });\n\n  emitter.subscribe(\"accountChanged\", (_account) => {\n    account = _account;\n  });\n\n  return {\n    id: COINBASE,\n    subscribe: emitter.subscribe,\n    getChain() {\n      if (!chain) {\n        return undefined;\n      }\n\n      chain = getCachedChainIfExists(chain.id) || chain;\n      return chain;\n    },\n    getConfig: () => createOptions,\n    getAccount: () => account,\n    autoConnect: async (options) => {\n      const { autoConnectCoinbaseWalletSDK } = await import(\n        \"./coinbase-web.js\"\n      );\n      const provider = await args.providerFactory();\n      const [connectedAccount, connectedChain, doDisconnect, doSwitchChain] =\n        await autoConnectCoinbaseWalletSDK(options, emitter, provider);\n      // set the states\n      account = connectedAccount;\n      chain = connectedChain;\n      handleDisconnect = doDisconnect;\n      handleSwitchChain = doSwitchChain;\n      trackConnect({\n        client: options.client,\n        walletType: COINBASE,\n        walletAddress: account.address,\n        chainId: chain.id,\n      });\n      // return account\n      return account;\n    },\n    connect: async (options) => {\n      const { connectCoinbaseWalletSDK } = await import(\"./coinbase-web.js\");\n      const provider = await args.providerFactory();\n      const [connectedAccount, connectedChain, doDisconnect, doSwitchChain] =\n        await connectCoinbaseWalletSDK(options, emitter, provider);\n\n      // set the states\n      account = connectedAccount;\n      chain = connectedChain;\n      handleDisconnect = doDisconnect;\n      handleSwitchChain = doSwitchChain;\n      trackConnect({\n        client: options.client,\n        walletType: COINBASE,\n        walletAddress: account.address,\n        chainId: chain.id,\n      });\n      // return account\n      return account;\n    },\n    disconnect: async () => {\n      reset();\n      await handleDisconnect();\n    },\n    switchChain: async (newChain) => {\n      await handleSwitchChain(newChain);\n    },\n    onConnectRequested: async () => {\n      if (args.onConnectRequested) {\n        const provider = await args.providerFactory();\n        return args.onConnectRequested?.(provider);\n      }\n    },\n  };\n}\n", "import type { ThirdwebClient } from \"../../../client/client.js\";\nimport type { Wallet } from \"../../interfaces/wallet.js\";\nimport type {\n  CreateWalletArgs,\n  EcosystemWalletId,\n} from \"../../wallet-types.js\";\nimport { createInAppWallet } from \"../core/wallet/in-app-core.js\";\n\n/**\n * Creates an [Ecosystem Wallet](https://portal.thirdweb.com/connect/wallet/overview) based on various authentication methods. Full list of available authentication methods [here](/connect/wallet/sign-in-methods/configure).\n *\n * Can also be configured to use Account Abstraction to directly connect to a ERC4337 smart account based on those authentication methods.\n *\n * Refer to [inAppWallet](/typescript/v5/inAppWallet) for detailed usage examples.\n *\n * @param createOptions - configuration options\n * Refer to [EcosystemWalletCreationOptions](https://portal.thirdweb.com/references/typescript/v5/EcosystemWalletCreationOptions) for more details.\n * @returns The created ecosystem wallet.\n *\n *\n * @example\n * ### Logging into an ecosystem wallet\n *\n * Below is the general code snippet needed to connect via a given auth strategy to an ecosystem wallet. For more variants on the various auth strategies, refer to [inAppWallet](/typescript/v5/inAppWallet).\n *\n * ```ts\n * import { ecosystemWallet } from \"thirdweb/wallets\";\n *\n * const wallet = ecosystemWallet(\"ecosystem.hooli\");\n *\n * const account = await wallet.connect({\n *   client,\n *   chain,\n *   strategy: \"google\",\n * });\n * ```\n *\n * [View all connection options](https://portal.thirdweb.com/references/typescript/v5/EcosystemWalletConnectionOptions).\n *\n * ### Connect to a restricted ecosystem wallet with your designated partner ID\n *\n * The partner ID will be provided to you by the ecosystem with which you're integrating.\n *\n * ```ts\n * import { ecosystemWallet } from \"thirdweb/wallets\";\n * const wallet = ecosystemWallet(\"ecosystem.hooli\", {\n *  partnerId: \"...\"\n * });\n * ```\n *\n *\n * @wallet\n */\nexport function ecosystemWallet(\n  ...args: CreateWalletArgs<EcosystemWalletId>\n): Wallet<EcosystemWalletId> {\n  const [ecosystemId, createOptions] = args;\n  const ecosystem = {\n    id: ecosystemId,\n    partnerId: createOptions?.partnerId,\n  };\n  return createInAppWallet({\n    ecosystem,\n    createOptions: {\n      auth: {\n        ...createOptions?.auth,\n        options: [], // controlled by ecosystem\n      },\n      partnerId: ecosystem.partnerId,\n    },\n    connectorFactory: async (client: ThirdwebClient) => {\n      const { InAppWebConnector } = await import(\"./lib/web-connector.js\");\n      return new InAppWebConnector({\n        client,\n        ecosystem,\n        storage: createOptions?.storage,\n      });\n    },\n  }) as Wallet<EcosystemWalletId>;\n}\n", "import type { ThirdwebClient } from \"../../../client/client.js\";\nimport type { Wallet } from \"../../interfaces/wallet.js\";\nimport { createInAppWallet } from \"../core/wallet/in-app-core.js\";\nimport type { InAppWalletCreationOptions } from \"../core/wallet/types.js\";\n\n/**\n * Creates an app scoped wallet for users based on various authentication methods. Full list of available authentication methods [here](https://portal.thirdweb.com/connect/wallet/sign-in-methods/configure).\n *\n * Can also be configured to use Account Abstraction to directly connect to a ERC4337 smart account based on those authentication methods.\n *\n * @param createOptions - configuration options\n * Refer to [InAppWalletCreationOptions](https://portal.thirdweb.com/references/typescript/v5/InAppWalletCreationOptions) to see the available options.\n * @returns The created in-app wallet.\n * @example\n *\n * ### Login with socials\n *\n * ```ts\n * import { inAppWallet } from \"thirdweb/wallets\";\n *\n * const wallet = inAppWallet();\n *\n * const account = await wallet.connect({\n *   client,\n *   chain,\n *   strategy: \"google\", // or \"apple\", \"facebook\",\"discord\", \"github\", \"twitch\", \"x\", \"telegram\", \"line\", \"coinbase\", etc\n * });\n * ```\n *\n * [View all available social auth methods](https://portal.thirdweb.com/connect/wallet/sign-in-methods/configure)\n *\n * ### Enable smart accounts and sponsor gas for your users:\n *\n * With the `executionMode` option, you can enable smart accounts and sponsor gas for your users.\n *\n * **Using EIP-7702** (recommended):\n *\n * On chains with EIP-7702 enabled, you can upgrade the inapp wallet to a smart account, keeping the same address and performance as the regular EOA.\n *\n * ```ts\n * import { inAppWallet } from \"thirdweb/wallets\";\n * import { sepolia } from \"thirdweb/chains\";\n *\n * const wallet = inAppWallet({\n *  executionMode: {\n *   mode: \"EIP7702\",\n *   sponsorGas: true,\n *  },\n * });\n *\n * // account will be a smart account with sponsored gas enabled\n * const account = await wallet.connect({\n *   client,\n *   strategy: \"google\",\n * });\n * ```\n *\n * **Using EIP-4337**:\n *\n * On chains without EIP-7702 enabled, you can still use smart accounts using EIP-4337, this will return a different address (the smart contract address) than the regular EOA.\n *\n * ```ts\n * import { inAppWallet } from \"thirdweb/wallets/in-app\";\n *\n * const wallet = inAppWallet({\n *  executionMode: {\n *   mode: \"EIP4337\",\n *   smartAccount: {\n *    chain: sepolia, // chain required for EIP-4337\n *    sponsorGas: true,\n *   }\n *  },\n * });\n * ```\n *\n * ### Login with email\n *\n * To login with email, you can use the `preAuthenticate` function to first send a verification code to the user's email, then login with the verification code.\n *\n * ```ts\n * import { inAppWallet, preAuthenticate } from \"thirdweb/wallets/in-app\";\n *\n * const wallet = inAppWallet();\n *\n * // sends a verification code to the provided email\n * await preAuthenticate({\n *   client,\n *   strategy: \"email\",\n *   email: \"<EMAIL>\",\n * });\n *\n * // login with the verification code\n * const account = await wallet.connect({\n *   client,\n *   chain,\n *   strategy: \"email\",\n *   email: \"<EMAIL>\",\n *   verificationCode: \"123456\",\n * });\n * ```\n *\n * ### Login with phone number\n *\n * Similar to email, you can login with a phone number by first sending a verification code to the user's phone number, then login with the verification code.\n *\n * ```ts\n * import { inAppWallet, preAuthenticate } from \"thirdweb/wallets/in-app\";\n *\n * const wallet = inAppWallet();\n *\n * // sends a verification code to the provided phone number\n * await preAuthenticate({\n *   client,\n *   strategy: \"phone\",\n *   phoneNumber: \"+**********\",\n * });\n *\n * // login with the verification code\n * const account = await wallet.connect({\n *   client,\n *   chain,\n *   strategy: \"phone\",\n *   honeNumber: \"+**********\",\n *   verificationCode: \"123456\",\n * });\n * ```\n *\n * ### Login with another wallet (SIWE)\n *\n * You can also login to the in-app wallet with another existing wallet by signing a standard Sign in with Ethereum (SIWE) message.\n *\n * ```ts\n * import { inAppWallet, createWallet } from \"thirdweb/wallets\";\n *\n * const rabby = createWallet(\"io.rabby\");\n * const inAppWallet = inAppWallet();\n *\n * const account = await inAppWallet.connect({\n *    strategy: \"wallet\",\n *    chain: mainnet,\n *    wallet: rabby,\n *    client: MY_CLIENT\n * });\n * ```\n *\n * ### Login with passkey\n *\n * You can also login with a passkey. This mode requires specifying whether it should create a new passkey, or sign in with an existing passkey. We recommend checking if the user has a passkey stored in their browser to automatically login with it.\n *\n * ```ts\n * import { inAppWallet, hasStoredPasskey } from \"thirdweb/wallets/in-app\";\n *\n * const wallet = inAppWallet();\n *\n * const wallet = inAppWallet();\n * const hasPasskey = await hasStoredPasskey(client);\n * await wallet.connect({\n *   client,\n *   strategy: \"passkey\",\n *  type: hasPasskey ? \"sign-in\" : \"sign-up\",\n * });\n * ```\n *\n * ### Connect to a guest account\n *\n * You can also connect to a guest account, this will create a new account for the user instantly and store it in the browser's local storage.\n *\n * You can later \"upgrade\" this account by linking another auth method, like email or phone for example. This will preserve the account's address and history.\n *\n * ```ts\n * import { inAppWallet } from \"thirdweb/wallets\";\n *\n * const wallet = inAppWallet();\n *\n * const account = await wallet.connect({\n *   client,\n *   strategy: \"guest\",\n * });\n * ```\n *\n * ### Connect to a backend account\n *\n * For usage in backends, you can create wallets with the `backend` strategy and a stable walletSecret.\n *\n * Make sure to keep that walletSecret safe as it is the key to access that wallet, never expose it to the client.\n *\n * ```ts\n * import { inAppWallet } from \"thirdweb/wallets\";\n *\n * const wallet = inAppWallet();\n *\n * const account = await wallet.connect({\n *   client,\n *   strategy: \"backend\",\n *   walletSecret: \"...\", // Your own secret, keep it safe\n * });\n * ```\n *\n * ### Connect with custom JWT (any OIDC provider)\n *\n * You can use any OIDC provider to authenticate your users. Make sure to configure it in your dashboard under in-app wallet settings.\n *\n * ```ts\n * import { inAppWallet } from \"thirdweb/wallets\";\n *\n * const wallet = inAppWallet();\n *\n * const account = await wallet.connect({\n *   client,\n *   strategy: \"jwt\",\n *   jwt: \"your_jwt_here\",\n * });\n * ```\n *\n * ### Connect with custom endpoint\n *\n * You can also use your own endpoint to authenticate your users. Make sure to configure it in your dashboard under in-app wallet settings.\n *\n * ```ts\n * import { inAppWallet } from \"thirdweb/wallets\";\n *\n * const wallet = inAppWallet();\n *\n * const account = await wallet.connect({\n *   client,\n *   strategy: \"auth_endpoint\",\n *   payload: \"your_auth_payload_here\",\n * });\n * ```\n *\n * ### Specify a logo, icon and name for your login page (Connect UI)\n *\n * You can specify a logo, icon and name for your login page to customize how in-app wallets are displayed in the Connect UI components (ConnectButton and ConnectEmbed).\n *\n * ```ts\n * import { inAppWallet } from \"thirdweb/wallets\";\n * const wallet = inAppWallet({\n *  metadata: {\n *    name: \"My App\",\n *    icon: \"https://example.com/icon.png\",\n *    image: {\n *      src: \"https://example.com/logo.png\",\n *      alt: \"My logo\",\n *      width: 100,\n *      height: 100,\n *   },\n *  },\n * });\n * ```\n *\n * ### Hide the ability to export the private key within the Connect Modal UI\n *\n * By default, the Connect Modal will show a button to export the private key of the wallet. You can hide this button by setting the `hidePrivateKeyExport` option to `true`.\n *\n * ```ts\n * import { inAppWallet } from \"thirdweb/wallets\";\n * const wallet = inAppWallet({\n *  hidePrivateKeyExport: true\n * });\n * ```\n *\n * ### Open the Oauth window in the same tab\n *\n * By default, the Oauth window will open in a popup window. You can change this behavior by setting the `auth.mode` option to `\"redirect\"`.\n *\n * ```ts\n * import { inAppWallet } from \"thirdweb/wallets\";\n * const wallet = inAppWallet({\n *  auth: {\n *    mode: \"redirect\"\n *  }\n * });\n * ```\n *\n * ### Override storage for the wallet state\n *\n * By default, wallet state is stored in the browser's local storage if in the browser, or in-memory storage if not in the browser. You can override this behavior by providing a custom storage object, useful for server side and CLI integrations.\n *\n * ```ts\n * import { inAppWallet } from \"thirdweb/wallets\";\n * import { AsyncStorage } from \"thirdweb/storage\";\n *\n * const myStorage: AsyncStorage = {\n *  getItem: async (key) => {\n *    return customGet(`CUSTOM_STORAGE_KEY${key}`);\n *  },\n *  setItem: async (key, value) => {\n *    return customSet(`CUSTOM_STORAGE_KEY${key}`, value);\n *  },\n *  removeItem: async (key) => {\n *    return customRemove(`CUSTOM_STORAGE_KEY${key}`);\n *  },\n * };\n *\n * const wallet = inAppWallet({\n *  storage: myStorage,\n * });\n * ```\n *\n * @returns The created in-app wallet.\n * @wallet\n */\nexport function inAppWallet(\n  createOptions?: InAppWalletCreationOptions,\n): Wallet<\"inApp\"> {\n  return createInAppWallet({\n    createOptions,\n    connectorFactory: async (client: ThirdwebClient) => {\n      const { InAppWebConnector } = await import(\"./lib/web-connector.js\");\n      return new InAppWebConnector({\n        client,\n        passkeyDomain: createOptions?.auth?.passkeyDomain,\n        storage: createOptions?.storage,\n      });\n    },\n  }) as Wallet<\"inApp\">;\n}\n", "import type { EIP1193<PERSON>rovider } from \"viem\";\nimport {\n  type SignTypedDataParameters,\n  getTypesForEIP712Domain,\n  serializeTypedData,\n  validateTypedData,\n} from \"viem\";\nimport { trackTransaction } from \"../../analytics/track/transaction.js\";\nimport type { Chain } from \"../../chains/types.js\";\nimport { getCachedChain, getChainMetadata } from \"../../chains/utils.js\";\nimport type { ThirdwebClient } from \"../../client/client.js\";\nimport { getAddress } from \"../../utils/address.js\";\nimport {\n  type Hex,\n  numberToHex,\n  stringToHex,\n  uint8ArrayToHex,\n} from \"../../utils/encoding/hex.js\";\nimport { parseTypedData } from \"../../utils/signatures/helpers/parse-typed-data.js\";\nimport type { InjectedSupportedWalletIds } from \"../__generated__/wallet-ids.js\";\nimport type { Account, SendTransactionOption } from \"../interfaces/wallet.js\";\nimport type { DisconnectFn, SwitchChainFn } from \"../types.js\";\nimport { getValidPublicRPCUrl } from \"../utils/chains.js\";\nimport { normalizeChainId } from \"../utils/normalizeChainId.js\";\nimport type { WalletEmitter } from \"../wallet-emitter.js\";\nimport type { WalletId } from \"../wallet-types.js\";\nimport { injectedProvider } from \"./mipdStore.js\";\n\n// TODO: save the provider in data\nexport function getInjectedProvider(walletId: WalletId) {\n  const provider = injectedProvider(walletId);\n  if (!provider) {\n    throw new Error(`No injected provider found for wallet: \"${walletId}\"`);\n  }\n\n  return provider;\n}\n\n/**\n * @internal\n */\nexport async function connectEip1193Wallet({\n  id,\n  provider,\n  emitter,\n  client,\n  chain,\n}: {\n  id: InjectedSupportedWalletIds | ({} & string);\n  provider: EIP1193Provider;\n  client: ThirdwebClient;\n  chain?: Chain;\n  emitter: WalletEmitter<InjectedSupportedWalletIds>;\n}): Promise<ReturnType<typeof onConnect>> {\n  let addresses: string[] | undefined;\n  const retries = 3;\n  let attempts = 0;\n  // retry 3 times, some providers take a while to return accounts on connect\n  while (!addresses?.[0] && attempts < retries) {\n    try {\n      addresses = await provider.request({\n        method: \"eth_requestAccounts\",\n      });\n    } catch (e) {\n      console.error(e);\n      if (extractErrorMessage(e)?.toLowerCase()?.includes(\"rejected\")) {\n        throw e;\n      }\n      await new Promise((resolve) => setTimeout(resolve, 500));\n    }\n    attempts++;\n  }\n\n  const addr = addresses?.[0];\n  if (!addr) {\n    throw new Error(\"Failed to connect to wallet, no accounts available\");\n  }\n\n  // use the first account\n  const address = getAddress(addr);\n\n  // get the chainId the provider is on\n  const chainId = await provider\n    .request({ method: \"eth_chainId\" })\n    .then(normalizeChainId)\n    .catch((e) => {\n      throw new Error(\"Error reading chainId from provider\", e);\n    });\n\n  let connectedChain =\n    chain && chain.id === chainId ? chain : getCachedChain(chainId);\n\n  try {\n    // if we want a specific chainId and it is not the same as the provider chainId, trigger switchChain\n    // we check for undefined chain ID since some chain-specific wallets like Abstract will not send a chain ID on connection\n    if (chain && typeof chain.id !== \"undefined\" && chain.id !== chainId) {\n      await switchChain(provider, chain);\n      connectedChain = chain;\n    }\n  } catch {\n    console.warn(\n      `Error switching to chain ${chain?.id} - defaulting to wallet chain (${chainId})`,\n    );\n  }\n\n  return onConnect({\n    provider,\n    address,\n    chain: connectedChain,\n    emitter,\n    client,\n    id,\n  });\n}\n\n/**\n * @internal\n */\nexport async function autoConnectEip1193Wallet({\n  id,\n  provider,\n  emitter,\n  client,\n  chain,\n}: {\n  id: InjectedSupportedWalletIds | ({} & string);\n  provider: EIP1193Provider;\n  emitter: WalletEmitter<InjectedSupportedWalletIds>;\n  client: ThirdwebClient;\n  chain?: Chain;\n}): Promise<ReturnType<typeof onConnect>> {\n  // connected accounts\n  const addresses = await provider.request({\n    method: \"eth_accounts\",\n  });\n\n  const addr = addresses[0];\n  if (!addr) {\n    throw new Error(\"Failed to connect to wallet, no accounts available\");\n  }\n\n  // use the first account\n  const address = getAddress(addr);\n\n  // get the chainId the provider is on\n  const chainId = await provider\n    .request({ method: \"eth_chainId\" })\n    .then(normalizeChainId);\n\n  const connectedChain =\n    chain && chain.id === chainId ? chain : getCachedChain(chainId);\n\n  return onConnect({\n    provider,\n    address,\n    chain: connectedChain,\n    emitter,\n    client,\n    id,\n  });\n}\n\nfunction createAccount({\n  provider,\n  address,\n  client,\n  id,\n}: {\n  provider: EIP1193Provider;\n  address: string;\n  client: ThirdwebClient;\n  id: WalletId | ({} & string);\n}) {\n  const account: Account = {\n    address: getAddress(address),\n    async sendTransaction(tx: SendTransactionOption) {\n      const gasFees = tx.gasPrice\n        ? {\n            gasPrice: tx.gasPrice ? numberToHex(tx.gasPrice) : undefined,\n          }\n        : {\n            maxFeePerGas: tx.maxFeePerGas\n              ? numberToHex(tx.maxFeePerGas)\n              : undefined,\n            maxPriorityFeePerGas: tx.maxPriorityFeePerGas\n              ? numberToHex(tx.maxPriorityFeePerGas)\n              : undefined,\n          };\n      const params = [\n        {\n          ...gasFees,\n          nonce: tx.nonce ? numberToHex(tx.nonce) : undefined,\n          accessList: tx.accessList,\n          value: tx.value ? numberToHex(tx.value) : undefined,\n          gas: tx.gas ? numberToHex(tx.gas) : undefined,\n          from: this.address,\n          to: tx.to ? getAddress(tx.to) : undefined,\n          data: tx.data,\n          ...tx.eip712,\n        },\n      ];\n\n      const transactionHash = (await provider.request({\n        method: \"eth_sendTransaction\",\n        // @ts-expect-error - overriding types here\n        params,\n      })) as Hex;\n\n      trackTransaction({\n        client,\n        chainId: tx.chainId,\n        walletAddress: getAddress(address),\n        walletType: id,\n        transactionHash,\n        contractAddress: tx.to ?? undefined,\n        gasPrice: tx.gasPrice,\n      });\n\n      return {\n        transactionHash,\n      };\n    },\n    async signMessage({ message }) {\n      if (!account.address) {\n        throw new Error(\"Provider not setup\");\n      }\n\n      const messageToSign = (() => {\n        if (typeof message === \"string\") {\n          return stringToHex(message);\n        }\n        if (message.raw instanceof Uint8Array) {\n          return uint8ArrayToHex(message.raw);\n        }\n        return message.raw;\n      })();\n\n      return await provider.request({\n        method: \"personal_sign\",\n        params: [messageToSign, getAddress(account.address)],\n      });\n    },\n    async signTypedData(typedData) {\n      if (!provider || !account.address) {\n        throw new Error(\"Provider not setup\");\n      }\n      const parsedTypedData = parseTypedData(typedData);\n\n      const { domain, message, primaryType } =\n        parsedTypedData as unknown as SignTypedDataParameters;\n\n      const types = {\n        EIP712Domain: getTypesForEIP712Domain({ domain }),\n        ...parsedTypedData.types,\n      };\n\n      // Need to do a runtime validation check on addresses, byte ranges, integer ranges, etc\n      // as we can't statically check this with TypeScript.\n      validateTypedData({ domain, message, primaryType, types });\n\n      const stringifiedData = serializeTypedData({\n        domain: domain ?? {},\n        message,\n        primaryType,\n        types,\n      });\n\n      return await provider.request({\n        method: \"eth_signTypedData_v4\",\n        params: [getAddress(account.address), stringifiedData],\n      });\n    },\n    async watchAsset(asset) {\n      const result = await provider.request(\n        {\n          method: \"wallet_watchAsset\",\n          params: asset,\n        },\n        { retryCount: 0 },\n      );\n      return result;\n    },\n  };\n\n  return account;\n}\n\n/**\n * Call this method when the wallet provider is connected or auto connected\n * @internal\n */\nasync function onConnect({\n  provider,\n  address,\n  chain,\n  emitter,\n  client,\n  id,\n}: {\n  provider: EIP1193Provider;\n  address: string;\n  chain: Chain;\n  emitter: WalletEmitter<InjectedSupportedWalletIds>;\n  client: ThirdwebClient;\n  id: WalletId | ({} & string);\n}): Promise<[Account, Chain, DisconnectFn, SwitchChainFn]> {\n  const account = createAccount({ provider, address, client, id });\n  async function disconnect() {\n    provider.removeListener(\"accountsChanged\", onAccountsChanged);\n    provider.removeListener(\"chainChanged\", onChainChanged);\n    provider.removeListener(\"disconnect\", onDisconnect);\n  }\n\n  async function onDisconnect() {\n    disconnect();\n    emitter.emit(\"disconnect\", undefined);\n  }\n\n  function onAccountsChanged(accounts: string[]) {\n    if (accounts[0]) {\n      const newAccount = createAccount({\n        provider,\n        address: getAddress(accounts[0]),\n        client,\n        id,\n      });\n\n      emitter.emit(\"accountChanged\", newAccount);\n      emitter.emit(\"accountsChanged\", accounts);\n    } else {\n      onDisconnect();\n    }\n  }\n\n  function onChainChanged(newChainId: string) {\n    const newChain = getCachedChain(normalizeChainId(newChainId));\n    emitter.emit(\"chainChanged\", newChain);\n  }\n\n  if (provider.on) {\n    provider.on(\"accountsChanged\", onAccountsChanged);\n    provider.on(\"chainChanged\", onChainChanged);\n    provider.on(\"disconnect\", onDisconnect);\n  }\n\n  return [\n    account,\n    chain,\n    onDisconnect,\n    (newChain) => switchChain(provider, newChain),\n  ] as const;\n}\n\n/**\n * @internal\n */\nasync function switchChain(provider: EIP1193Provider, chain: Chain) {\n  const hexChainId = numberToHex(chain.id);\n  try {\n    await provider.request({\n      method: \"wallet_switchEthereumChain\",\n      params: [{ chainId: hexChainId }],\n    });\n  } catch {\n    // if chain does not exist, add the chain\n    const apiChain = await getChainMetadata(chain);\n    await provider.request({\n      method: \"wallet_addEthereumChain\",\n      params: [\n        {\n          chainId: hexChainId,\n          chainName: apiChain.name,\n          nativeCurrency: apiChain.nativeCurrency,\n          rpcUrls: getValidPublicRPCUrl(apiChain), // no client id on purpose here\n          blockExplorerUrls: apiChain.explorers?.map((x) => x.url),\n        },\n      ],\n    });\n  }\n}\n\nfunction extractErrorMessage(e: unknown) {\n  if (e instanceof Error) {\n    return e.message;\n  }\n  if (typeof e === \"string\") {\n    return e;\n  }\n  if (typeof e === \"object\" && e !== null) {\n    return JSON.stringify(e);\n  }\n  return String(e);\n}\n", "import { trackConnect } from \"../../analytics/track/connect.js\";\nimport type { Chain } from \"../../chains/types.js\";\nimport { getCachedChainIfExists } from \"../../chains/utils.js\";\nimport { getContract } from \"../../contract/contract.js\";\nimport { isZkSync<PERSON>hain } from \"../../utils/any-evm/zksync/isZkSyncChain.js\";\nimport { isContractDeployed } from \"../../utils/bytecode/is-contract-deployed.js\";\nimport type { Account, Wallet } from \"../interfaces/wallet.js\";\nimport { createWalletEmitter } from \"../wallet-emitter.js\";\nimport type { WalletConnectionOption } from \"../wallet-types.js\";\nimport { getDefaultAccountFactory } from \"./lib/constants.js\";\nimport type { SmartWalletOptions } from \"./types.js\";\n\n/**\n * Creates a ERC4337 smart wallet based on a admin account.\n *\n * Smart wallets are smart contract wallets that enable multiple benefits for users:\n *\n * - Sponsor gas fees for transactions\n * - Multiple owners\n * - Session keys\n * - Batch transactions\n * - Predictable addresses\n * - Programmable features\n *\n * [Learn more about account abstraction](https://portal.thirdweb.com/connect/account-abstraction/how-it-works)\n *\n * @param createOptions - The options for creating the wallet.\n * Refer to [SmartWalletCreationOptions](https://portal.thirdweb.com/references/typescript/v5/SmartWalletCreationOptions) for more details.\n * @returns The created smart wallet.\n * @example\n *\n * ## Connect to a smart wallet\n *\n * To connect to a smart wallet, you need to provide an admin account as the `personalAccount` option.\n *\n * Any wallet can be used as an admin account, including an in-app wallets.\n *\n * The `sponsorGas` option is used to enable sponsored gas for transactions automatically.\n *\n * ```ts\n * import { smartWallet, inAppWallet } from \"thirdweb/wallets\";\n * import { sepolia } from \"thirdweb/chains\";\n * import { sendTransaction } from \"thirdweb\";\n *\n * const wallet = smartWallet({\n *  chain: sepolia,\n *  sponsorGas: true, // enable sponsored transactions\n * });\n *\n * // any wallet can be used as an admin account\n * // in this example we use an in-app wallet\n * const adminWallet = inAppWallet();\n * const personalAccount = await adminWallet.connect({\n *   client,\n *   chain: sepolia,\n *   strategy: \"google\",\n * });\n *\n * const smartAccount = await wallet.connect({\n *   client,\n *   personalAccount, // pass the admin account\n * });\n *\n * // sending sponsored transactions with the smartAccount\n * await sendTransaction({\n *   account: smartAccount,\n *   transaction,\n * });\n * ```\n *\n * ## Using a custom account factory\n *\n * You can pass a custom account factory to the `smartWallet` function to use a your own account factory.\n *\n * ```ts\n * import { smartWallet } from \"thirdweb/wallets\";\n * import { sepolia } from \"thirdweb/chains\";\n *\n * const wallet = smartWallet({\n *  chain: sepolia,\n *  sponsorGas: true, // enable sponsored transactions\n *  factoryAddress: \"0x...\", // custom factory address\n * });\n * ```\n *\n * ## Using v0.7 Entrypoint\n *\n * Both v0.6 (default) and v0.7 ERC4337 Entrypoints are supported. To use the v0.7 Entrypoint, simply pass in a compatible account factory.\n *\n * You can use the predeployed `DEFAULT_ACCOUNT_FACTORY_V0_7` or deploy your own [AccountFactory  v0.7](https://thirdweb.com/thirdweb.eth/AccountFactory_0_7).\n *\n * ```ts\n * import { smartWallet, DEFAULT_ACCOUNT_FACTORY_V0_7 } from \"thirdweb/wallets/smart\";\n * import { sepolia } from \"thirdweb/chains\";\n *\n * const wallet = smartWallet({\n *  chain: sepolia,\n *  sponsorGas: true, // enable sponsored transactions\n *  factoryAddress: DEFAULT_ACCOUNT_FACTORY_V0_7, // 0.7 factory address\n * });\n * ```\n *\n * ## Configuring the smart wallet\n *\n * You can pass options to the `smartWallet` function to configure the smart wallet.\n *\n * ```ts\n * import { smartWallet } from \"thirdweb/wallets\";\n * import { sepolia } from \"thirdweb/chains\";\n *\n * const wallet = smartWallet({\n *  chain: sepolia,\n *  sponsorGas: true, // enable sponsored transactions\n *  factoryAddress: \"0x...\", // custom factory address\n *  overrides: {\n *    accountAddress: \"0x...\", // override account address\n *    accountSalt: \"0x...\", // override account salt\n *    entrypointAddress: \"0x...\", // override entrypoint address\n *    tokenPaymaster: TokenPaymaster.BASE_USDC, // enable erc20 paymaster\n *    bundlerUrl: \"https://...\", // override bundler url\n *    paymaster: (userOp) => { ... }, // override paymaster\n *    ...\n *  }\n * });\n * ```\n *\n * Refer to [SmartWalletOptions](https://portal.thirdweb.com/references/typescript/v5/SmartWalletOptions) for more details.\n *\n * @wallet\n */\nexport function smartWallet(\n  createOptions: SmartWalletOptions,\n): Wallet<\"smart\"> {\n  const emitter = createWalletEmitter<\"smart\">();\n  let account: Account | undefined = undefined;\n  let adminAccount: Account | undefined = undefined;\n  let chain: Chain | undefined = undefined;\n  let lastConnectOptions: WalletConnectionOption<\"smart\"> | undefined;\n\n  return {\n    id: \"smart\",\n    subscribe: emitter.subscribe,\n    getChain() {\n      if (!chain) {\n        return undefined;\n      }\n\n      chain = getCachedChainIfExists(chain.id) || chain;\n      return chain;\n    },\n    getConfig: () => createOptions,\n    getAccount: () => account,\n    getAdminAccount: () => adminAccount,\n    autoConnect: async (options) => {\n      const { connectSmartAccount: connectSmartWallet } = await import(\n        \"./index.js\"\n      );\n      const [connectedAccount, connectedChain] = await connectSmartWallet(\n        options,\n        createOptions,\n      );\n      // set the states\n      lastConnectOptions = options;\n      account = connectedAccount;\n      chain = connectedChain;\n      trackConnect({\n        client: options.client,\n        walletType: \"smart\",\n        walletAddress: account.address,\n        chainId: chain.id,\n      });\n      // return account\n      return account;\n    },\n    connect: async (options) => {\n      const { connectSmartAccount } = await import(\"./index.js\");\n      const [connectedAccount, connectedChain] = await connectSmartAccount(\n        options,\n        createOptions,\n      );\n      // set the states\n      adminAccount = options.personalAccount;\n      lastConnectOptions = options;\n      account = connectedAccount;\n      chain = connectedChain;\n      trackConnect({\n        client: options.client,\n        walletType: \"smart\",\n        walletAddress: account.address,\n        chainId: chain.id,\n      });\n      // return account\n      emitter.emit(\"accountChanged\", account);\n      return account;\n    },\n    disconnect: async () => {\n      if (account) {\n        const { disconnectSmartAccount } = await import(\"./index.js\");\n        await disconnectSmartAccount(account);\n      }\n      account = undefined;\n      adminAccount = undefined;\n      chain = undefined;\n      emitter.emit(\"disconnect\", undefined);\n    },\n    switchChain: async (newChain: Chain) => {\n      if (!lastConnectOptions) {\n        throw new Error(\"Cannot switch chain without a previous connection\");\n      }\n      const isZksyncChain = await isZkSyncChain(newChain);\n      if (!isZksyncChain) {\n        // check if factory is deployed\n        const factory = getContract({\n          address:\n            createOptions.factoryAddress ||\n            getDefaultAccountFactory(\n              createOptions.overrides?.entrypointAddress,\n            ),\n          chain: newChain,\n          client: lastConnectOptions.client,\n        });\n        const isDeployed = await isContractDeployed(factory);\n        if (!isDeployed) {\n          throw new Error(\n            `Factory contract not deployed on chain: ${newChain.id}`,\n          );\n        }\n      }\n      const { connectSmartAccount } = await import(\"./index.js\");\n      const [connectedAccount, connectedChain] = await connectSmartAccount(\n        { ...lastConnectOptions, chain: newChain },\n        createOptions,\n      );\n      // set the states\n      account = connectedAccount;\n      chain = connectedChain;\n      emitter.emit(\"accountChanged\", connectedAccount);\n      emitter.emit(\"chainChanged\", connectedChain);\n    },\n  };\n}\n", "import type { Chain } from \"../chains/types.js\";\nimport type {\n  InjectedSupportedWalletIds,\n  WCSupportedWalletIds,\n} from \"./__generated__/wallet-ids.js\";\nimport type { Account, Wallet } from \"./interfaces/wallet.js\";\nimport type {\n  CreateWalletArgs,\n  EcosystemWalletId,\n  WalletAutoConnectionOption,\n  WalletId,\n} from \"./wallet-types.js\";\n\nimport { trackConnect } from \"../analytics/track/connect.js\";\nimport { getCachedChainIfExists } from \"../chains/utils.js\";\nimport { webLocalStorage } from \"../utils/storage/webStorage.js\";\nimport { isMobile } from \"../utils/web/isMobile.js\";\nimport { openWindow } from \"../utils/web/openWindow.js\";\nimport { coinbaseWalletSDK } from \"./coinbase/coinbase-wallet.js\";\nimport { getCoinbaseWebProvider } from \"./coinbase/coinbase-web.js\";\nimport { COINBASE } from \"./constants.js\";\nimport { isEcosystemWallet } from \"./ecosystem/is-ecosystem-wallet.js\";\nimport { ecosystemWallet } from \"./in-app/web/ecosystem.js\";\nimport { inAppWallet } from \"./in-app/web/in-app.js\";\nimport { getInjectedProvider } from \"./injected/index.js\";\nimport { smartWallet } from \"./smart/smart-wallet.js\";\nimport type { WCConnectOptions } from \"./wallet-connect/types.js\";\nimport { createWalletEmitter } from \"./wallet-emitter.js\";\n\n// TODO: figure out how to define the type without tuple args type and using function overloads\n\n/**\n * Creates a wallet based on the provided ID and arguments.\n *\n * - Supports 500+ wallets\n * - Handles both injected browser wallets and WalletConnect sessions\n *\n * [View all available wallets](https://portal.thirdweb.com/typescript/v5/supported-wallets)\n *\n * @param args - The arguments for creating the wallet.\n * @param args.id - The ID of the wallet to create, this will be autocompleted by your IDE.\n * [View all available wallets](https://portal.thirdweb.com/typescript/v5/supported-wallets)\n * @param args.createOptions - The options for creating the wallet.\n * The arguments are different for each wallet type.\n * Refer to the [WalletCreationOptions](https://portal.thirdweb.com/references/typescript/v5/WalletCreationOptions) type for more details.\n * @returns - The created wallet.\n * @example\n *\n * ## Connecting the wallet\n *\n * Once created, you can connect the wallet to your app by calling the `connect` method.\n *\n * The `connect` method returns a promise that resolves to the connected account.\n *\n * Each wallet type can have different connect options. [View the different connect options](https://portal.thirdweb.com/references/typescript/v5/WalletConnectionOption)\n *\n * ## Connecting to an injected wallet\n *\n * ```ts\n * import { createWallet } from \"thirdweb/wallets\";\n *\n * const metamaskWallet = createWallet(\"io.metamask\");\n *\n * const account = await metamaskWallet.connect({\n *  client,\n * });\n * ```\n *\n * You can check if a wallet is installed by calling the [injectedProvider](https://portal.thirdweb.com/references/typescript/v5/injectedProvider) method.\n *\n * ## Connecting via WalletConnect modal\n *\n * ```ts\n * import { createWallet } from \"thirdweb/wallets\";\n *\n * const metamaskWallet = createWallet(\"io.metamask\");\n *\n * await metamask.connect({\n *   client,\n *   walletConnect: {\n *     projectId: \"YOUR_PROJECT_ID\",\n *     showQrModal: true,\n *     appMetadata: {\n *       name: \"My App\",\n *       url: \"https://my-app.com\",\n *       description: \"my app description\",\n *       logoUrl: \"https://path/to/my-app/logo.svg\",\n *     },\n *   },\n * });\n * ```\n * [View ConnectWallet connection options](https://portal.thirdweb.com/references/typescript/v5/WCConnectOptions)\n *\n * ## Connecting with coinbase wallet\n *\n * ```ts\n * import { createWallet } from \"thirdweb/wallets\";\n *\n * const cbWallet = createWallet(\"com.coinbase.wallet\", {\n *   appMetadata: {\n *     name: \"My App\",\n *     url: \"https://my-app.com\",\n *     description: \"my app description\",\n *     logoUrl: \"https://path/to/my-app/logo.svg\",\n *   },\n *   walletConfig: {\n *     // options: 'all' | 'smartWalletOnly' | 'eoaOnly'\n *     options: 'all',\n *   },\n * });\n *\n * const account = await cbWallet.connect({\n *  client,\n * });\n * ```\n *\n * [View Coinbase wallet creation options](https://portal.thirdweb.com/references/typescript/v5/CoinbaseWalletCreationOptions)\n *\n * ## Connecting with a smart wallet\n *\n * ```ts\n * import { createWallet } from \"thirdweb/wallets\";\n *\n * const wallet = createWallet(\"smart\", {\n *   chain: sepolia,\n *   sponsorGas: true,\n * });\n *\n * const account = await wallet.connect({\n *  client,\n *  personalAccount, // pass the admin account\n * });\n * ```\n *\n * @wallet\n */\nexport function createWallet<const ID extends WalletId>(\n  ...args: CreateWalletArgs<ID>\n): Wallet<ID> {\n  const [id, creationOptions] = args;\n\n  switch (true) {\n    /**\n     * SMART WALLET\n     */\n    case id === \"smart\": {\n      return smartWallet(\n        creationOptions as CreateWalletArgs<\"smart\">[1],\n      ) as Wallet<ID>;\n    }\n    /**\n     * IN-APP WALLET\n     */\n    case id === \"embedded\" || id === \"inApp\": {\n      return inAppWallet(\n        creationOptions as CreateWalletArgs<\"inApp\">[1],\n      ) as Wallet<ID>;\n    }\n    /**\n     * ECOSYSTEM WALLET\n     */\n    case isEcosystemWallet(id):\n      return ecosystemWallet(\n        ...(args as CreateWalletArgs<EcosystemWalletId>),\n      ) as Wallet<ID>;\n\n    /**\n     * COINBASE WALLET VIA SDK\n     * -> if no injected coinbase found, we'll use the coinbase SDK\n     */\n    case id === COINBASE: {\n      const options = creationOptions as CreateWalletArgs<typeof COINBASE>[1];\n      return coinbaseWalletSDK({\n        createOptions: options,\n        providerFactory: () => getCoinbaseWebProvider(options),\n        onConnectRequested: async (provider) => {\n          // on the web, make sure to show the coinbase popup IMMEDIATELY on connection requested\n          // otherwise the popup might get blocked in safari\n          // TODO awaiting the provider is fast only thanks to preloading that happens in our components\n          // these probably need to actually imported / created synchronously to be used headless properly\n          const { showCoinbasePopup } = await import(\"./coinbase/utils.js\");\n          return showCoinbasePopup(provider);\n        },\n      }) as Wallet<ID>;\n    }\n    /**\n     * WALLET CONNECT AND INJECTED WALLETS + walletConnect standalone\n     */\n    default: {\n      const emitter = createWalletEmitter<ID>();\n      let account: Account | undefined = undefined;\n      let chain: Chain | undefined = undefined;\n      let unsubscribeChain: (() => void) | undefined = undefined;\n\n      function reset() {\n        account = undefined;\n        chain = undefined;\n      }\n\n      let handleDisconnect = async () => {};\n\n      const unsubscribeDisconnect = emitter.subscribe(\"disconnect\", () => {\n        reset();\n        unsubscribeChain?.();\n        unsubscribeDisconnect();\n      });\n\n      emitter.subscribe(\"accountChanged\", (_account) => {\n        account = _account;\n      });\n\n      let handleSwitchChain: (chain: Chain) => Promise<void> = async () => {\n        throw new Error(\"Not implemented yet\");\n      };\n\n      // on mobile, deeplink to the wallet app for session handling\n      const sessionHandler = isMobile()\n        ? (uri: string) => openWindow(uri)\n        : undefined;\n\n      const wallet: Wallet<ID> = {\n        id,\n        subscribe: emitter.subscribe,\n        getConfig: () => args[1],\n        getChain() {\n          if (!chain) {\n            return undefined;\n          }\n\n          chain = getCachedChainIfExists(chain.id) || chain;\n          return chain;\n        },\n        getAccount: () => account,\n        autoConnect: async (\n          options: WalletAutoConnectionOption<\n            WCSupportedWalletIds | InjectedSupportedWalletIds\n          >,\n        ) => {\n          const { injectedProvider } = await import(\"./injected/mipdStore.js\");\n          // injected wallet priority for autoConnect\n          if (id !== \"walletConnect\" && injectedProvider(id)) {\n            const { autoConnectEip1193Wallet } = await import(\n              \"./injected/index.js\"\n            );\n\n            const [\n              connectedAccount,\n              connectedChain,\n              doDisconnect,\n              doSwitchChain,\n            ] = await autoConnectEip1193Wallet({\n              id: id as InjectedSupportedWalletIds,\n              provider: getInjectedProvider(id),\n              emitter,\n              chain: options.chain,\n              client: options.client,\n            });\n            // set the states\n            account = connectedAccount;\n            chain = connectedChain;\n            handleDisconnect = doDisconnect;\n            handleSwitchChain = doSwitchChain;\n            unsubscribeChain = emitter.subscribe(\"chainChanged\", (newChain) => {\n              chain = newChain;\n            });\n            trackConnect({\n              client: options.client,\n              walletType: id,\n              walletAddress: account.address,\n              chainId: chain.id,\n            });\n            // return account\n            return account;\n          }\n\n          if (options && \"client\" in options) {\n            const { autoConnectWC } = await import(\n              \"./wallet-connect/controller.js\"\n            );\n\n            const [\n              connectedAccount,\n              connectedChain,\n              doDisconnect,\n              doSwitchChain,\n            ] = await autoConnectWC(\n              options,\n              emitter,\n              wallet.id as WCSupportedWalletIds,\n              webLocalStorage,\n              sessionHandler,\n            );\n            // set the states\n            account = connectedAccount;\n            chain = connectedChain;\n            handleDisconnect = doDisconnect;\n            handleSwitchChain = doSwitchChain;\n            trackConnect({\n              client: options.client,\n              walletType: id,\n              walletAddress: account.address,\n              chainId: chain.id,\n            });\n            // return account\n            return account;\n          }\n          throw new Error(\"Failed to auto connect\");\n        },\n        connect: async (options) => {\n          async function wcConnect(wcOptions: WCConnectOptions) {\n            const { connectWC } = await import(\n              \"./wallet-connect/controller.js\"\n            );\n\n            const [\n              connectedAccount,\n              connectedChain,\n              doDisconnect,\n              doSwitchChain,\n            ] = await connectWC(\n              wcOptions,\n              emitter,\n              wallet.id as WCSupportedWalletIds | \"walletConnect\",\n              webLocalStorage,\n              sessionHandler,\n            );\n            // set the states\n            account = connectedAccount;\n            chain = connectedChain;\n            handleDisconnect = doDisconnect;\n            handleSwitchChain = doSwitchChain;\n            trackConnect({\n              client: wcOptions.client,\n              walletType: id,\n              walletAddress: account.address,\n              chainId: chain.id,\n            });\n            return account;\n          }\n\n          if (id === \"walletConnect\") {\n            const { client, chain: _chain, ...walletConnectOptions } = options;\n\n            return wcConnect({\n              client,\n              chain: _chain,\n              walletConnect: {\n                ...walletConnectOptions,\n              },\n            });\n          }\n\n          // prefer walletconnect over injected if explicitely passing walletConnect options\n          const forceWalletConnectOption =\n            options && \"walletConnect\" in options;\n\n          const { injectedProvider } = await import(\"./injected/mipdStore.js\");\n          if (injectedProvider(id) && !forceWalletConnectOption) {\n            const { connectEip1193Wallet } = await import(\n              \"./injected/index.js\"\n            );\n\n            const [\n              connectedAccount,\n              connectedChain,\n              doDisconnect,\n              doSwitchChain,\n            ] = await connectEip1193Wallet({\n              id: id as InjectedSupportedWalletIds,\n              provider: getInjectedProvider(id),\n              client: options.client,\n              chain: options.chain,\n              emitter,\n            });\n            // set the states\n            account = connectedAccount;\n            chain = connectedChain;\n            handleDisconnect = doDisconnect;\n            handleSwitchChain = doSwitchChain;\n            unsubscribeChain = emitter.subscribe(\"chainChanged\", (newChain) => {\n              chain = newChain;\n            });\n            trackConnect({\n              client: options.client,\n              walletType: id,\n              walletAddress: account.address,\n              chainId: chain.id,\n            });\n            // return account\n            return account;\n          }\n\n          if (options && \"client\" in options) {\n            return wcConnect(options);\n          }\n          throw new Error(\"Failed to connect\");\n        },\n        // these get overridden in connect and autoConnect\n        disconnect: async () => {\n          reset();\n          await handleDisconnect();\n        },\n        switchChain: (c) => handleSwitchChain(c),\n      };\n      return wallet;\n    }\n  }\n}\n\n/**\n * Creates a wallet that allows connecting to any wallet that supports the WalletConnect protocol.\n * @returns The created smart wallet.\n * @example\n * ```ts\n * import { walletConnect } from \"thirdweb/wallets\";\n *\n * const wallet = walletConnect();\n *\n * const account = await wallet.connect({\n *  client\n * });\n * ```\n * @wallet\n */\nexport function walletConnect() {\n  return createWallet(\"walletConnect\");\n}\n", "import { type EIP6963ProviderDetail, type Store, createStore } from \"mipd\";\nimport { isBrowser } from \"../../utils/platform.js\";\nimport type { InjectedSupportedWalletIds } from \"../__generated__/wallet-ids.js\";\nimport { METAMASK } from \"../constants.js\";\nimport { createWallet } from \"../create-wallet.js\";\nimport type { Ethereum } from \"../interfaces/ethereum.js\";\nimport type { Wallet } from \"../interfaces/wallet.js\";\nimport type { WalletId } from \"../wallet-types.js\";\n\ndeclare module \"mipd\" {\n  export interface Register {\n    rdns: WalletId;\n  }\n}\n\n// if we're in the browser -> create the store once immediately\nconst mipdStore: Store | undefined = /* @__PURE__ */ (() =>\n  isBrowser() ? createStore() : undefined)();\n\n/**\n * Get Injected Provider for given wallet by passing a wallet ID (rdns) using [EIP-6963](https://eips.ethereum.org/EIPS/eip-6963) Provider Discovery.\n * @param walletId - The Wallet Id (rdns) to check.\n * @example\n * ```ts\n * import { injectedProvider } from \"thirdweb/wallets\";\n *\n * const metamaskProvider = injectedProvider(\"io.metamask\");\n *\n * if (metamaskProvider) {\n *  console.log(\"Metamask is installed\");\n * }\n * ```\n * @returns The details of the Injected Provider if it exists. `undefined` otherwise.\n * @walletUtils\n */\nexport function injectedProvider(walletId: WalletId): Ethereum | undefined {\n  const injectedProviderDetail = getInstalledWalletProviders().find(\n    (p) => p.info.rdns === walletId,\n  );\n\n  return injectedProviderDetail?.provider as Ethereum | undefined;\n}\n\n/**\n * Get All currently installed wallets.\n * Uses EIP-6963 to discover installed browser extension wallets.\n * @returns a list of installed wallets\n */\nexport function getInstalledWallets(): Wallet[] {\n  const providers = getInstalledWalletProviders();\n  const walletIds = providers.map((provider) => provider.info.rdns);\n  return walletIds.map((w) => createWallet(w as InjectedSupportedWalletIds));\n}\n\n/**\n * Get Injected Provider Details for given wallet ID (rdns)\n * @internal\n */\nfunction getMIPDStore() {\n  if (!mipdStore) {\n    return undefined;\n  }\n  return mipdStore;\n}\n\nexport function getInstalledWalletProviders(): readonly EIP6963ProviderDetail[] {\n  const providers = getMIPDStore()?.getProviders() || [];\n\n  for (const provider of providers) {\n    // Map io.metamask.mobile to io.metamask rdns to fix double entry issue in MetaMask mobile browser\n    if ((provider.info.rdns as string) === \"io.metamask.mobile\") {\n      provider.info.rdns = METAMASK;\n      break;\n    }\n  }\n\n  return providers;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CM,SAAU,iBACd,UAAoC;AAEpC,MAAI,OAAO,WAAW;AAAa;AACnC,QAAM,UAAU,CAAC,UACf,SAAS,MAAM,MAAM;AAEvB,SAAO,iBAAiB,4BAA4B,OAAO;AAE3D,SAAO,cAAc,IAAI,YAAY,yBAAyB,CAAC;AAE/D,SAAO,MAAM,OAAO,oBAAoB,4BAA4B,OAAO;AAC7E;;;ACNM,SAAU,cAAW;AACzB,QAAM,YAA2B,oBAAI,IAAG;AACxC,MAAI,kBAAoD,CAAA;AAExD,QAAM,UAAU,MACd,iBAAiB,CAAC,mBAAkB;AAClC,QACE,gBAAgB,KACd,CAAC,EAAE,KAAI,MAAO,KAAK,SAAS,eAAe,KAAK,IAAI;AAGtD;AAEF,sBAAkB,CAAC,GAAG,iBAAiB,cAAc;AACrD,cAAU,QAAQ,CAAC,aACjB,SAAS,iBAAiB,EAAE,OAAO,CAAC,cAAc,EAAC,CAAE,CAAC;EAE1D,CAAC;AACH,MAAI,UAAU,QAAO;AAErB,SAAO;IACL,aAAU;AACR,aAAO;IACT;IACA,QAAK;AACH,gBAAU,QAAQ,CAAC,aACjB,SAAS,CAAA,GAAI,EAAE,SAAS,CAAC,GAAG,eAAe,EAAC,CAAE,CAAC;AAEjD,wBAAkB,CAAA;IACpB;IACA,UAAO;AACL,WAAK,MAAK;AACV,gBAAU,MAAK;AACf;IACF;IACA,aAAa,EAAE,KAAI,GAAE;AACnB,aAAO,gBAAgB,KACrB,CAAC,mBAAmB,eAAe,KAAK,SAAS,IAAI;IAEzD;IACA,eAAY;AACV,aAAO;IACT;IACA,QAAK;AACH,WAAK,MAAK;AACV;AACA,gBAAU,QAAO;IACnB;IACA,UAAU,UAAU,EAAE,gBAAe,IAAK,CAAA,GAAE;AAC1C,gBAAU,IAAI,QAAQ;AACtB,UAAI;AAAiB,iBAAS,iBAAiB,EAAE,OAAO,gBAAe,CAAE;AACzE,aAAO,MAAM,UAAU,OAAO,QAAQ;IACxC;;AAEJ;;;ACnGM,SAAU,YAAS;AAEvB,MAAI,OAAO,cAAc,aAAa;AACpC,WAAO;EACT;AACA,QAAM,KAAK,SAAS,UAAU,SAAS;AACvC,SAAO,KAAK,GAAG,YAAW,EAAG,SAAS,SAAS,IAAI;AACrD;AAKM,SAAU,QAAK;AAEnB,MAAI,OAAO,cAAc,aAAa;AACpC,WAAO;EACT;AACA,QAAM,KAAK,SAAS,UAAU,SAAS;AACvC,SAAO,KACH,GAAG,YAAW,EAAG,SAAS,KAAK,KAC5B,GAAG,YAAW,EAAG,SAAS,KAAK,KAAK,UAAU,iBAAiB,IAClE;AACN;AAKM,SAAU,WAAQ;AACtB,SAAO,UAAS,KAAM,MAAK;AAC7B;;;AC/BM,SAAU,WAAW,KAAW;AACpC,QAAM,iBAAiB,WAAW,OAAO;AACzC,MAAI,gBAAgB;AAClB,WAAO,KAAK,GAAG;EACjB,OAAO;AACL,QAAI,IAAI,WAAW,MAAM,GAAG;AAY1B,YAAM,OAAO,SAAS,cAAc,GAAG;AACvC,WAAK,OAAO;AACZ,WAAK,SAAS;AACd,WAAK,MAAM;AACX,WAAK,MAAK;IACZ,OAAO;AACL,aAAO,SAAS,OAAO;IACzB;EACF;AACF;;;ACbM,SAAU,kBAAkB,MAIjC;AACC,QAAM,EAAE,cAAa,IAAK;AAC1B,QAAM,UAAU,oBAAmB;AACnC,MAAI,UAA+B;AACnC,MAAI,QAA2B;AAE/B,WAAS,QAAK;AACZ,cAAU;AACV,YAAQ;EACV;AAEA,MAAI,mBAAmB,YAAW;EAAE;AAEpC,MAAI,oBAAoB,OAAO,aAAmB;AAChD,YAAQ;EACV;AAEA,QAAM,0BAA0B,QAAQ,UACtC,gBACA,CAAC,aAAY;AACX,YAAQ;EACV,CAAC;AAGH,QAAM,wBAAwB,QAAQ,UAAU,cAAc,MAAK;AACjE,UAAK;AACL,4BAAuB;AACvB,0BAAqB;EACvB,CAAC;AAED,UAAQ,UAAU,kBAAkB,CAAC,aAAY;AAC/C,cAAU;EACZ,CAAC;AAED,SAAO;IACL,IAAI;IACJ,WAAW,QAAQ;IACnB,WAAQ;AACN,UAAI,CAAC,OAAO;AACV,eAAO;MACT;AAEA,cAAQ,uBAAuB,MAAM,EAAE,KAAK;AAC5C,aAAO;IACT;IACA,WAAW,MAAM;IACjB,YAAY,MAAM;IAClB,aAAa,OAAO,YAAW;AAC7B,YAAM,EAAE,6BAA4B,IAAK,MAAM,OAC7C,4BAAmB;AAErB,YAAM,WAAW,MAAM,KAAK,gBAAe;AAC3C,YAAM,CAAC,kBAAkB,gBAAgB,cAAc,aAAa,IAClE,MAAM,6BAA6B,SAAS,SAAS,QAAQ;AAE/D,gBAAU;AACV,cAAQ;AACR,yBAAmB;AACnB,0BAAoB;AACpB,mBAAa;QACX,QAAQ,QAAQ;QAChB,YAAY;QACZ,eAAe,QAAQ;QACvB,SAAS,MAAM;OAChB;AAED,aAAO;IACT;IACA,SAAS,OAAO,YAAW;AACzB,YAAM,EAAE,yBAAwB,IAAK,MAAM,OAAO,4BAAmB;AACrE,YAAM,WAAW,MAAM,KAAK,gBAAe;AAC3C,YAAM,CAAC,kBAAkB,gBAAgB,cAAc,aAAa,IAClE,MAAM,yBAAyB,SAAS,SAAS,QAAQ;AAG3D,gBAAU;AACV,cAAQ;AACR,yBAAmB;AACnB,0BAAoB;AACpB,mBAAa;QACX,QAAQ,QAAQ;QAChB,YAAY;QACZ,eAAe,QAAQ;QACvB,SAAS,MAAM;OAChB;AAED,aAAO;IACT;IACA,YAAY,YAAW;AACrB,YAAK;AACL,YAAM,iBAAgB;IACxB;IACA,aAAa,OAAO,aAAY;AAC9B,YAAM,kBAAkB,QAAQ;IAClC;IACA,oBAAoB,YAAW;AAnHnC;AAoHM,UAAI,KAAK,oBAAoB;AAC3B,cAAM,WAAW,MAAM,KAAK,gBAAe;AAC3C,gBAAO,UAAK,uBAAL,8BAA0B;MACnC;IACF;;AAEJ;;;ACrEM,SAAU,mBACX,MAAyC;AAE5C,QAAM,CAAC,aAAa,aAAa,IAAI;AACrC,QAAM,YAAY;IAChB,IAAI;IACJ,WAAW,+CAAe;;AAE5B,SAAO,kBAAkB;IACvB;IACA,eAAe;MACb,MAAM;QACJ,GAAG,+CAAe;QAClB,SAAS,CAAA;;;MAEX,WAAW,UAAU;;IAEvB,kBAAkB,OAAO,WAA0B;AACjD,YAAM,EAAE,kBAAiB,IAAK,MAAM,OAAO,6BAAwB;AACnE,aAAO,IAAI,kBAAkB;QAC3B;QACA;QACA,SAAS,+CAAe;OACzB;IACH;GACD;AACH;;;AC+NM,SAAU,YACd,eAA0C;AAE1C,SAAO,kBAAkB;IACvB;IACA,kBAAkB,OAAO,WAA0B;AAjTvD;AAkTM,YAAM,EAAE,kBAAiB,IAAK,MAAM,OAAO,6BAAwB;AACnE,aAAO,IAAI,kBAAkB;QAC3B;QACA,gBAAe,oDAAe,SAAf,mBAAqB;QACpC,SAAS,+CAAe;OACzB;IACH;GACD;AACH;;;AC/RM,SAAU,oBAAoB,UAAkB;AACpD,QAAM,WAAW,iBAAiB,QAAQ;AAC1C,MAAI,CAAC,UAAU;AACb,UAAM,IAAI,MAAM,2CAA2C,QAAQ,GAAG;EACxE;AAEA,SAAO;AACT;AAKA,eAAsB,qBAAqB,EACzC,IACA,UACA,SACA,QACA,MAAK,GAON;AApDD;AAqDE,MAAI;AACJ,QAAM,UAAU;AAChB,MAAI,WAAW;AAEf,SAAO,EAAC,uCAAY,OAAM,WAAW,SAAS;AAC5C,QAAI;AACF,kBAAY,MAAM,SAAS,QAAQ;QACjC,QAAQ;OACT;IACH,SAAS,GAAG;AACV,cAAQ,MAAM,CAAC;AACf,WAAI,+BAAoB,CAAC,MAArB,mBAAwB,kBAAxB,mBAAuC,SAAS,aAAa;AAC/D,cAAM;MACR;AACA,YAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,GAAG,CAAC;IACzD;AACA;EACF;AAEA,QAAM,OAAO,uCAAY;AACzB,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,oDAAoD;EACtE;AAGA,QAAM,UAAU,WAAW,IAAI;AAG/B,QAAM,UAAU,MAAM,SACnB,QAAQ,EAAE,QAAQ,cAAa,CAAE,EACjC,KAAK,gBAAgB,EACrB,MAAM,CAAC,MAAK;AACX,UAAM,IAAI,MAAM,uCAAuC,CAAC;EAC1D,CAAC;AAEH,MAAI,iBACF,SAAS,MAAM,OAAO,UAAU,QAAQ,eAAe,OAAO;AAEhE,MAAI;AAGF,QAAI,SAAS,OAAO,MAAM,OAAO,eAAe,MAAM,OAAO,SAAS;AACpE,YAAM,YAAY,UAAU,KAAK;AACjC,uBAAiB;IACnB;EACF,QAAQ;AACN,YAAQ,KACN,4BAA4B,+BAAO,EAAE,kCAAkC,OAAO,GAAG;EAErF;AAEA,SAAO,UAAU;IACf;IACA;IACA,OAAO;IACP;IACA;IACA;GACD;AACH;AAKA,eAAsB,yBAAyB,EAC7C,IACA,UACA,SACA,QACA,MAAK,GAON;AAEC,QAAM,YAAY,MAAM,SAAS,QAAQ;IACvC,QAAQ;GACT;AAED,QAAM,OAAO,UAAU,CAAC;AACxB,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,oDAAoD;EACtE;AAGA,QAAM,UAAU,WAAW,IAAI;AAG/B,QAAM,UAAU,MAAM,SACnB,QAAQ,EAAE,QAAQ,cAAa,CAAE,EACjC,KAAK,gBAAgB;AAExB,QAAM,iBACJ,SAAS,MAAM,OAAO,UAAU,QAAQ,eAAe,OAAO;AAEhE,SAAO,UAAU;IACf;IACA;IACA,OAAO;IACP;IACA;IACA;GACD;AACH;AAEA,SAAS,cAAc,EACrB,UACA,SACA,QACA,GAAE,GAMH;AACC,QAAM,UAAmB;IACvB,SAAS,WAAW,OAAO;IAC3B,MAAM,gBAAgB,IAAyB;AAC7C,YAAM,UAAU,GAAG,WACf;QACE,UAAU,GAAG,WAAW,YAAY,GAAG,QAAQ,IAAI;UAErD;QACE,cAAc,GAAG,eACb,YAAY,GAAG,YAAY,IAC3B;QACJ,sBAAsB,GAAG,uBACrB,YAAY,GAAG,oBAAoB,IACnC;;AAEV,YAAM,SAAS;QACb;UACE,GAAG;UACH,OAAO,GAAG,QAAQ,YAAY,GAAG,KAAK,IAAI;UAC1C,YAAY,GAAG;UACf,OAAO,GAAG,QAAQ,YAAY,GAAG,KAAK,IAAI;UAC1C,KAAK,GAAG,MAAM,YAAY,GAAG,GAAG,IAAI;UACpC,MAAM,KAAK;UACX,IAAI,GAAG,KAAK,WAAW,GAAG,EAAE,IAAI;UAChC,MAAM,GAAG;UACT,GAAG,GAAG;;;AAIV,YAAM,kBAAmB,MAAM,SAAS,QAAQ;QAC9C,QAAQ;;QAER;OACD;AAED,uBAAiB;QACf;QACA,SAAS,GAAG;QACZ,eAAe,WAAW,OAAO;QACjC,YAAY;QACZ;QACA,iBAAiB,GAAG,MAAM;QAC1B,UAAU,GAAG;OACd;AAED,aAAO;QACL;;IAEJ;IACA,MAAM,YAAY,EAAE,QAAO,GAAE;AAC3B,UAAI,CAAC,QAAQ,SAAS;AACpB,cAAM,IAAI,MAAM,oBAAoB;MACtC;AAEA,YAAM,iBAAiB,MAAK;AAC1B,YAAI,OAAO,YAAY,UAAU;AAC/B,iBAAO,YAAY,OAAO;QAC5B;AACA,YAAI,QAAQ,eAAe,YAAY;AACrC,iBAAO,gBAAgB,QAAQ,GAAG;QACpC;AACA,eAAO,QAAQ;MACjB,GAAE;AAEF,aAAO,MAAM,SAAS,QAAQ;QAC5B,QAAQ;QACR,QAAQ,CAAC,eAAe,WAAW,QAAQ,OAAO,CAAC;OACpD;IACH;IACA,MAAM,cAAc,WAAS;AAC3B,UAAI,CAAC,YAAY,CAAC,QAAQ,SAAS;AACjC,cAAM,IAAI,MAAM,oBAAoB;MACtC;AACA,YAAM,kBAAkB,eAAe,SAAS;AAEhD,YAAM,EAAE,QAAQ,SAAS,YAAW,IAClC;AAEF,YAAM,QAAQ;QACZ,cAAc,wBAAwB,EAAE,OAAM,CAAE;QAChD,GAAG,gBAAgB;;AAKrB,wBAAkB,EAAE,QAAQ,SAAS,aAAa,MAAK,CAAE;AAEzD,YAAM,kBAAkB,mBAAmB;QACzC,QAAQ,UAAU,CAAA;QAClB;QACA;QACA;OACD;AAED,aAAO,MAAM,SAAS,QAAQ;QAC5B,QAAQ;QACR,QAAQ,CAAC,WAAW,QAAQ,OAAO,GAAG,eAAe;OACtD;IACH;IACA,MAAM,WAAW,OAAK;AACpB,YAAM,SAAS,MAAM,SAAS,QAC5B;QACE,QAAQ;QACR,QAAQ;SAEV,EAAE,YAAY,EAAC,CAAE;AAEnB,aAAO;IACT;;AAGF,SAAO;AACT;AAMA,eAAe,UAAU,EACvB,UACA,SACA,OACA,SACA,QACA,GAAE,GAQH;AACC,QAAM,UAAU,cAAc,EAAE,UAAU,SAAS,QAAQ,GAAE,CAAE;AAC/D,iBAAe,aAAU;AACvB,aAAS,eAAe,mBAAmB,iBAAiB;AAC5D,aAAS,eAAe,gBAAgB,cAAc;AACtD,aAAS,eAAe,cAAc,YAAY;EACpD;AAEA,iBAAe,eAAY;AACzB,eAAU;AACV,YAAQ,KAAK,cAAc,MAAS;EACtC;AAEA,WAAS,kBAAkB,UAAkB;AAC3C,QAAI,SAAS,CAAC,GAAG;AACf,YAAM,aAAa,cAAc;QAC/B;QACA,SAAS,WAAW,SAAS,CAAC,CAAC;QAC/B;QACA;OACD;AAED,cAAQ,KAAK,kBAAkB,UAAU;AACzC,cAAQ,KAAK,mBAAmB,QAAQ;IAC1C,OAAO;AACL,mBAAY;IACd;EACF;AAEA,WAAS,eAAe,YAAkB;AACxC,UAAM,WAAW,eAAe,iBAAiB,UAAU,CAAC;AAC5D,YAAQ,KAAK,gBAAgB,QAAQ;EACvC;AAEA,MAAI,SAAS,IAAI;AACf,aAAS,GAAG,mBAAmB,iBAAiB;AAChD,aAAS,GAAG,gBAAgB,cAAc;AAC1C,aAAS,GAAG,cAAc,YAAY;EACxC;AAEA,SAAO;IACL;IACA;IACA;IACA,CAAC,aAAa,YAAY,UAAU,QAAQ;;AAEhD;AAKA,eAAe,YAAY,UAA2B,OAAY;AAnWlE;AAoWE,QAAM,aAAa,YAAY,MAAM,EAAE;AACvC,MAAI;AACF,UAAM,SAAS,QAAQ;MACrB,QAAQ;MACR,QAAQ,CAAC,EAAE,SAAS,WAAU,CAAE;KACjC;EACH,QAAQ;AAEN,UAAM,WAAW,MAAM,iBAAiB,KAAK;AAC7C,UAAM,SAAS,QAAQ;MACrB,QAAQ;MACR,QAAQ;QACN;UACE,SAAS;UACT,WAAW,SAAS;UACpB,gBAAgB,SAAS;UACzB,SAAS,qBAAqB,QAAQ;;UACtC,oBAAmB,cAAS,cAAT,mBAAoB,IAAI,CAAC,MAAM,EAAE;;;KAGzD;EACH;AACF;AAEA,SAAS,oBAAoB,GAAU;AACrC,MAAI,aAAa,OAAO;AACtB,WAAO,EAAE;EACX;AACA,MAAI,OAAO,MAAM,UAAU;AACzB,WAAO;EACT;AACA,MAAI,OAAO,MAAM,YAAY,MAAM,MAAM;AACvC,WAAO,KAAK,UAAU,CAAC;EACzB;AACA,SAAO,OAAO,CAAC;AACjB;;;ACtQM,SAAU,YACd,eAAiC;AAEjC,QAAM,UAAU,oBAAmB;AACnC,MAAI,UAA+B;AACnC,MAAI,eAAoC;AACxC,MAAI,QAA2B;AAC/B,MAAI;AAEJ,SAAO;IACL,IAAI;IACJ,WAAW,QAAQ;IACnB,WAAQ;AACN,UAAI,CAAC,OAAO;AACV,eAAO;MACT;AAEA,cAAQ,uBAAuB,MAAM,EAAE,KAAK;AAC5C,aAAO;IACT;IACA,WAAW,MAAM;IACjB,YAAY,MAAM;IAClB,iBAAiB,MAAM;IACvB,aAAa,OAAO,YAAW;AAC7B,YAAM,EAAE,qBAAqB,mBAAkB,IAAK,MAAM,OACxD,qBAAY;AAEd,YAAM,CAAC,kBAAkB,cAAc,IAAI,MAAM,mBAC/C,SACA,aAAa;AAGf,2BAAqB;AACrB,gBAAU;AACV,cAAQ;AACR,mBAAa;QACX,QAAQ,QAAQ;QAChB,YAAY;QACZ,eAAe,QAAQ;QACvB,SAAS,MAAM;OAChB;AAED,aAAO;IACT;IACA,SAAS,OAAO,YAAW;AACzB,YAAM,EAAE,oBAAmB,IAAK,MAAM,OAAO,qBAAY;AACzD,YAAM,CAAC,kBAAkB,cAAc,IAAI,MAAM,oBAC/C,SACA,aAAa;AAGf,qBAAe,QAAQ;AACvB,2BAAqB;AACrB,gBAAU;AACV,cAAQ;AACR,mBAAa;QACX,QAAQ,QAAQ;QAChB,YAAY;QACZ,eAAe,QAAQ;QACvB,SAAS,MAAM;OAChB;AAED,cAAQ,KAAK,kBAAkB,OAAO;AACtC,aAAO;IACT;IACA,YAAY,YAAW;AACrB,UAAI,SAAS;AACX,cAAM,EAAE,uBAAsB,IAAK,MAAM,OAAO,qBAAY;AAC5D,cAAM,uBAAuB,OAAO;MACtC;AACA,gBAAU;AACV,qBAAe;AACf,cAAQ;AACR,cAAQ,KAAK,cAAc,MAAS;IACtC;IACA,aAAa,OAAO,aAAmB;AA7M3C;AA8MM,UAAI,CAAC,oBAAoB;AACvB,cAAM,IAAI,MAAM,mDAAmD;MACrE;AACA,YAAM,gBAAgB,MAAM,cAAc,QAAQ;AAClD,UAAI,CAAC,eAAe;AAElB,cAAM,UAAU,YAAY;UAC1B,SACE,cAAc,kBACd,0BACE,mBAAc,cAAd,mBAAyB,iBAAiB;UAE9C,OAAO;UACP,QAAQ,mBAAmB;SAC5B;AACD,cAAM,aAAa,MAAM,mBAAmB,OAAO;AACnD,YAAI,CAAC,YAAY;AACf,gBAAM,IAAI,MACR,2CAA2C,SAAS,EAAE,EAAE;QAE5D;MACF;AACA,YAAM,EAAE,oBAAmB,IAAK,MAAM,OAAO,qBAAY;AACzD,YAAM,CAAC,kBAAkB,cAAc,IAAI,MAAM,oBAC/C,EAAE,GAAG,oBAAoB,OAAO,SAAQ,GACxC,aAAa;AAGf,gBAAU;AACV,cAAQ;AACR,cAAQ,KAAK,kBAAkB,gBAAgB;AAC/C,cAAQ,KAAK,gBAAgB,cAAc;IAC7C;;AAEJ;;;ACxGM,SAAU,gBACX,MAA0B;AAE7B,QAAM,CAAC,IAAI,eAAe,IAAI;AAE9B,UAAQ,MAAM;IAIZ,KAAK,OAAO,SAAS;AACnB,aAAO,YACL,eAA+C;IAEnD;IAIA,MAAK,OAAO,cAAc,OAAO,UAAS;AACxC,aAAO,YACL,eAA+C;IAEnD;IAIA,KAAK,kBAAkB,EAAE;AACvB,aAAO,gBACL,GAAI,IAA4C;IAOpD,KAAK,OAAO,UAAU;AACpB,YAAM,UAAU;AAChB,aAAO,kBAAkB;QACvB,eAAe;QACf,iBAAiB,MAAM,uBAAuB,OAAO;QACrD,oBAAoB,OAAO,aAAY;AAKrC,gBAAM,EAAE,kBAAiB,IAAK,MAAM,OAAO,qBAAqB;AAChE,iBAAO,kBAAkB,QAAQ;QACnC;OACD;IACH;IAIA,SAAS;AAMP,UAAS,QAAT,WAAc;AACZ,kBAAU;AACV,gBAAQ;MACV;AARA,YAAM,UAAU,oBAAmB;AACnC,UAAI,UAA+B;AACnC,UAAI,QAA2B;AAC/B,UAAI,mBAA6C;AAOjD,UAAI,mBAAmB,YAAW;MAAE;AAEpC,YAAM,wBAAwB,QAAQ,UAAU,cAAc,MAAK;AACjE,cAAK;AACL;AACA,8BAAqB;MACvB,CAAC;AAED,cAAQ,UAAU,kBAAkB,CAAC,aAAY;AAC/C,kBAAU;MACZ,CAAC;AAED,UAAI,oBAAqD,YAAW;AAClE,cAAM,IAAI,MAAM,qBAAqB;MACvC;AAGA,YAAM,iBAAiB,SAAQ,IAC3B,CAAC,QAAgB,WAAW,GAAG,IAC/B;AAEJ,YAAM,SAAqB;QACzB;QACA,WAAW,QAAQ;QACnB,WAAW,MAAM,KAAK,CAAC;QACvB,WAAQ;AACN,cAAI,CAAC,OAAO;AACV,mBAAO;UACT;AAEA,kBAAQ,uBAAuB,MAAM,EAAE,KAAK;AAC5C,iBAAO;QACT;QACA,YAAY,MAAM;QAClB,aAAa,OACX,YAGE;AACF,gBAAM,EAAE,kBAAAA,kBAAgB,IAAK,MAAM,OAAO,yBAAyB;AAEnE,cAAI,OAAO,mBAAmBA,kBAAiB,EAAE,GAAG;AAClD,kBAAM,EAAE,0BAAAC,0BAAwB,IAAK,MAAM,OACzC,wBAAqB;AAGvB,kBAAM,CACJ,kBACA,gBACA,cACA,aAAa,IACX,MAAMA,0BAAyB;cACjC;cACA,UAAU,oBAAoB,EAAE;cAChC;cACA,OAAO,QAAQ;cACf,QAAQ,QAAQ;aACjB;AAED,sBAAU;AACV,oBAAQ;AACR,+BAAmB;AACnB,gCAAoB;AACpB,+BAAmB,QAAQ,UAAU,gBAAgB,CAAC,aAAY;AAChE,sBAAQ;YACV,CAAC;AACD,yBAAa;cACX,QAAQ,QAAQ;cAChB,YAAY;cACZ,eAAe,QAAQ;cACvB,SAAS,MAAM;aAChB;AAED,mBAAO;UACT;AAEA,cAAI,WAAW,YAAY,SAAS;AAClC,kBAAM,EAAE,cAAa,IAAK,MAAM,OAC9B,0BAAgC;AAGlC,kBAAM,CACJ,kBACA,gBACA,cACA,aAAa,IACX,MAAM,cACR,SACA,SACA,OAAO,IACP,iBACA,cAAc;AAGhB,sBAAU;AACV,oBAAQ;AACR,+BAAmB;AACnB,gCAAoB;AACpB,yBAAa;cACX,QAAQ,QAAQ;cAChB,YAAY;cACZ,eAAe,QAAQ;cACvB,SAAS,MAAM;aAChB;AAED,mBAAO;UACT;AACA,gBAAM,IAAI,MAAM,wBAAwB;QAC1C;QACA,SAAS,OAAO,YAAW;AACzB,yBAAe,UAAU,WAA2B;AAClD,kBAAM,EAAE,UAAS,IAAK,MAAM,OAC1B,0BAAgC;AAGlC,kBAAM,CACJ,kBACA,gBACA,cACA,aAAa,IACX,MAAM,UACR,WACA,SACA,OAAO,IACP,iBACA,cAAc;AAGhB,sBAAU;AACV,oBAAQ;AACR,+BAAmB;AACnB,gCAAoB;AACpB,yBAAa;cACX,QAAQ,UAAU;cAClB,YAAY;cACZ,eAAe,QAAQ;cACvB,SAAS,MAAM;aAChB;AACD,mBAAO;UACT;AAEA,cAAI,OAAO,iBAAiB;AAC1B,kBAAM,EAAE,QAAQ,OAAO,QAAQ,GAAG,qBAAoB,IAAK;AAE3D,mBAAO,UAAU;cACf;cACA,OAAO;cACP,eAAe;gBACb,GAAG;;aAEN;UACH;AAGA,gBAAM,2BACJ,WAAW,mBAAmB;AAEhC,gBAAM,EAAE,kBAAAD,kBAAgB,IAAK,MAAM,OAAO,yBAAyB;AACnE,cAAIA,kBAAiB,EAAE,KAAK,CAAC,0BAA0B;AACrD,kBAAM,EAAE,sBAAAE,sBAAoB,IAAK,MAAM,OACrC,wBAAqB;AAGvB,kBAAM,CACJ,kBACA,gBACA,cACA,aAAa,IACX,MAAMA,sBAAqB;cAC7B;cACA,UAAU,oBAAoB,EAAE;cAChC,QAAQ,QAAQ;cAChB,OAAO,QAAQ;cACf;aACD;AAED,sBAAU;AACV,oBAAQ;AACR,+BAAmB;AACnB,gCAAoB;AACpB,+BAAmB,QAAQ,UAAU,gBAAgB,CAAC,aAAY;AAChE,sBAAQ;YACV,CAAC;AACD,yBAAa;cACX,QAAQ,QAAQ;cAChB,YAAY;cACZ,eAAe,QAAQ;cACvB,SAAS,MAAM;aAChB;AAED,mBAAO;UACT;AAEA,cAAI,WAAW,YAAY,SAAS;AAClC,mBAAO,UAAU,OAAO;UAC1B;AACA,gBAAM,IAAI,MAAM,mBAAmB;QACrC;;QAEA,YAAY,YAAW;AACrB,gBAAK;AACL,gBAAM,iBAAgB;QACxB;QACA,aAAa,CAAC,MAAM,kBAAkB,CAAC;;AAEzC,aAAO;IACT;EACF;AACF;AAiBM,SAAU,gBAAa;AAC3B,SAAO,aAAa,eAAe;AACrC;;;AC1ZA,IAAM,aAAgD,MACpD,UAAS,IAAK,YAAW,IAAK,QAAU;AAkBpC,SAAU,iBAAiB,UAAkB;AACjD,QAAM,yBAAyB,4BAA2B,EAAG,KAC3D,CAAC,MAAM,EAAE,KAAK,SAAS,QAAQ;AAGjC,SAAO,iEAAwB;AACjC;AAOM,SAAU,sBAAmB;AACjC,QAAM,YAAY,4BAA2B;AAC7C,QAAM,YAAY,UAAU,IAAI,CAAC,aAAa,SAAS,KAAK,IAAI;AAChE,SAAO,UAAU,IAAI,CAAC,MAAM,aAAa,CAA+B,CAAC;AAC3E;AAMA,SAAS,eAAY;AACnB,MAAI,CAAC,WAAW;AACd,WAAO;EACT;AACA,SAAO;AACT;AAEM,SAAU,8BAA2B;AAjE3C;AAkEE,QAAM,cAAY,kBAAY,MAAZ,mBAAgB,mBAAkB,CAAA;AAEpD,aAAW,YAAY,WAAW;AAEhC,QAAK,SAAS,KAAK,SAAoB,sBAAsB;AAC3D,eAAS,KAAK,OAAO;AACrB;IACF;EACF;AAEA,SAAO;AACT;", "names": ["injectedProvider", "autoConnectEip1193Wallet", "connectEip1193Wallet"]}