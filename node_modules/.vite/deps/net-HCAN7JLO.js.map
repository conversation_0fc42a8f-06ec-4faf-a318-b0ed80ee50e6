{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/net.gateweb3/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"net.gateweb3\",\n  name: \"GateWallet\",\n  homepage: \"https://www.gateweb3.net/web3\",\n  image_id: \"6e528abf-7a7d-47bd-d84d-481f169b1200\",\n  app: {\n    browser: null,\n    ios: \"https://www.gate.io/mobileapp\",\n    android: \"https://www.gate.io/mobileapp\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"gtweb3wallet://\",\n    universal: null,\n  },\n  desktop: {\n    native: \"gtweb3wallet://\",\n    universal: \"https://www.gateweb3.net/web3\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}