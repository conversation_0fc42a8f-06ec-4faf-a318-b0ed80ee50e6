import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/org.uniswap/index.js
var wallet = {
  id: "org.uniswap",
  name: "Uniswap Wallet",
  homepage: "https://uniswap.org",
  image_id: "6033c33c-0773-48e3-a12f-e7fbf409e700",
  app: {
    browser: null,
    ios: "https://apps.apple.com/us/app/uniswap-wallet/id6443944476",
    android: "https://play.google.com/store/apps/details?id=com.uniswap.mobile",
    mac: null,
    windows: null,
    linux: null,
    chrome: null,
    firefox: null,
    safari: null,
    edge: null,
    opera: null
  },
  rdns: null,
  mobile: {
    native: "uniswap://",
    universal: "https://uniswap.org/app"
  },
  desktop: {
    native: null,
    universal: null
  }
};
export {
  wallet
};
//# sourceMappingURL=org-6PJKCDUZ.js.map
