{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/xyz.coca/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"xyz.coca\",\n  name: \"COCA Wallet\",\n  homepage: \"https://www.coca.xyz/\",\n  image_id: \"34c9a3a1-a331-4c30-f7bc-182861ccca00\",\n  app: {\n    browser: \"https://wirexapp.com/wirex-wallet\",\n    ios: \"https://apps.apple.com/app/id1594165139\",\n    android: \"https://play.google.com/store/apps/details?id=com.wirex.wallet\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"wirexwallet://\",\n    universal:\n      \"https://wwallet.app.link/wc?uri=wc:00e46b69-d0cc-4b3e-b6a2-cee442f97188@1?bridge=https%3A%2F%2Fbridge.walletconnect.org&key=91303dedf64285cbbaf9120f6e9d160a5c8aa3deb67017a3874cd272323f48ae\",\n  },\n  desktop: {\n    native: null,\n    universal: \"https://wirexapp.com/wirex-wallet\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WACE;;EAEJ,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}