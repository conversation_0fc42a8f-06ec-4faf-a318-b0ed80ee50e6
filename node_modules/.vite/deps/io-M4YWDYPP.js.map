{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.nabox/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.nabox\",\n  name: \"Nabox\",\n  homepage: \"https://nabox.io/\",\n  image_id: \"3b75e9f7-2ca8-4a33-ed2b-4e8a0c048d00\",\n  app: {\n    browser:\n      \"https://chrome.google.com/webstore/detail/nabox-wallet/********************************?hl=en\",\n    ios: \"https://testflight.apple.com/join/Ux18h5Nv\",\n    android: \"https://play.google.com/store/apps/details?id=com.wallet.nabox\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: null,\n    universal: \"https://nabox.io/app/*\",\n  },\n  desktop: {\n    native: null,\n    universal:\n      \"https://chrome.google.com/webstore/detail/nabox-wallet/********************************?hl=en\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SACE;IACF,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WACE;;;", "names": []}