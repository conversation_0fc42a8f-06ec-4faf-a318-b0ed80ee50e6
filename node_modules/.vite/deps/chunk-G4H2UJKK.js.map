{"version": 3, "sources": ["../../thirdweb/src/utils/storage/webStorage.ts"], "sourcesContent": ["import type { AsyncStorage } from \"./AsyncStorage.js\";\n\nexport const webLocalStorage: AsyncStorage = {\n  async getItem(key: string) {\n    try {\n      if (typeof window !== \"undefined\" && window.localStorage) {\n        return localStorage.getItem(key);\n      }\n    } catch {\n      // ignore\n    }\n\n    return null;\n  },\n  async setItem(key: string, value: string) {\n    try {\n      if (typeof window !== \"undefined\" && window.localStorage) {\n        localStorage.setItem(key, value);\n      }\n    } catch {\n      // ignore\n    }\n  },\n  async removeItem(key: string) {\n    if (typeof window !== \"undefined\" && window.localStorage) {\n      localStorage.removeItem(key);\n    }\n  },\n};\n"], "mappings": ";AAEO,IAAM,kBAAgC;EAC3C,MAAM,QAAQ,KAAW;AACvB,QAAI;AACF,UAAI,OAAO,WAAW,eAAe,OAAO,cAAc;AACxD,eAAO,aAAa,QAAQ,GAAG;MACjC;IACF,QAAQ;IAER;AAEA,WAAO;EACT;EACA,MAAM,QAAQ,KAAa,OAAa;AACtC,QAAI;AACF,UAAI,OAAO,WAAW,eAAe,OAAO,cAAc;AACxD,qBAAa,QAAQ,KAAK,KAAK;MACjC;IACF,QAAQ;IAER;EACF;EACA,MAAM,WAAW,KAAW;AAC1B,QAAI,OAAO,WAAW,eAAe,OAAO,cAAc;AACxD,mBAAa,WAAW,GAAG;IAC7B;EACF;;", "names": []}