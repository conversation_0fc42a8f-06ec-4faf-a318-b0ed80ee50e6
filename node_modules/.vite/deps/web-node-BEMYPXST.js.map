{"version": 3, "sources": ["../../thirdweb/src/storage/upload/web-node.ts"], "sourcesContent": ["import type { ThirdwebClient } from \"../../client/client.js\";\nimport { getThirdwebDomains } from \"../../utils/domains.js\";\nimport { getClientFetch } from \"../../utils/fetch.js\";\nimport { IS_TEST } from \"../../utils/process.js\";\nimport { addToMockStorage } from \"../mock.js\";\nimport type { UploadOptions, UploadableFile } from \"./types.js\";\n\nexport async function uploadBatch<const TFiles extends UploadableFile[]>(\n  client: ThirdwebClient,\n  form: FormData,\n  fileNames: string[],\n  options?: UploadOptions<TFiles>,\n) {\n  if (IS_TEST) {\n    return addToMockStorage(form);\n  }\n\n  const headers: HeadersInit = {};\n\n  const res = await getClientFetch(client)(\n    `https://${getThirdwebDomains().storage}/ipfs/upload`,\n    {\n      method: \"POST\",\n      headers,\n      body: form,\n      requestTimeoutMs:\n        client.config?.storage?.fetch?.requestTimeoutMs || 120000,\n      // force auth token usage for storage uploads\n      useAuthToken: true,\n    },\n  );\n\n  if (!res.ok) {\n    if (res.status === 401) {\n      throw new Error(\n        \"Unauthorized - You don't have permission to use this service.\",\n      );\n    }\n    if (res.status === 402) {\n      throw new Error(\n        \"You have reached your storage limit. Please add a valid payment method to continue using the service.\",\n      );\n    }\n    if (res.status === 403) {\n      throw new Error(\n        \"Forbidden - You don't have permission to use this service.\",\n      );\n    }\n    throw new Error(\n      `Failed to upload files to IPFS - ${res.status} - ${res.statusText}`,\n    );\n  }\n\n  const body = await res.json();\n\n  const cid = body.IpfsHash;\n  if (!cid) {\n    throw new Error(\"Failed to upload files to IPFS - Bad CID\");\n  }\n\n  if (options?.uploadWithoutDirectory) {\n    return [`ipfs://${cid}`];\n  }\n  return fileNames.map((name) => `ipfs://${cid}/${name}`);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAOA,eAAsB,YACpB,QACA,MACA,WACA,SAA+B;AAVjC;AAYE,MAAI,SAAS;AACX,WAAO,iBAAiB,IAAI;EAC9B;AAEA,QAAM,UAAuB,CAAA;AAE7B,QAAM,MAAM,MAAM,eAAe,MAAM,EACrC,WAAW,mBAAkB,EAAG,OAAO,gBACvC;IACE,QAAQ;IACR;IACA,MAAM;IACN,oBACE,wBAAO,WAAP,mBAAe,YAAf,mBAAwB,UAAxB,mBAA+B,qBAAoB;;IAErD,cAAc;GACf;AAGH,MAAI,CAAC,IAAI,IAAI;AACX,QAAI,IAAI,WAAW,KAAK;AACtB,YAAM,IAAI,MACR,+DAA+D;IAEnE;AACA,QAAI,IAAI,WAAW,KAAK;AACtB,YAAM,IAAI,MACR,uGAAuG;IAE3G;AACA,QAAI,IAAI,WAAW,KAAK;AACtB,YAAM,IAAI,MACR,4DAA4D;IAEhE;AACA,UAAM,IAAI,MACR,oCAAoC,IAAI,MAAM,MAAM,IAAI,UAAU,EAAE;EAExE;AAEA,QAAM,OAAO,MAAM,IAAI,KAAI;AAE3B,QAAM,MAAM,KAAK;AACjB,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,MAAM,0CAA0C;EAC5D;AAEA,MAAI,mCAAS,wBAAwB;AACnC,WAAO,CAAC,UAAU,GAAG,EAAE;EACzB;AACA,SAAO,UAAU,IAAI,CAAC,SAAS,UAAU,GAAG,IAAI,IAAI,EAAE;AACxD;", "names": []}