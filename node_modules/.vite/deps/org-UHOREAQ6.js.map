{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/org.arianee/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"org.arianee\",\n  name: \"<PERSON><PERSON> Wallet\",\n  homepage: \"https://arianee.org\",\n  image_id: \"13b7fe36-909a-4c83-4f06-5740829a3900\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/fr/app/arianee-wallet/id1435782507\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.arianee.wallet&hl=ln&gl=US\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"com.arianee.wallet://\",\n    universal: \"https://i.arian.ee\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}