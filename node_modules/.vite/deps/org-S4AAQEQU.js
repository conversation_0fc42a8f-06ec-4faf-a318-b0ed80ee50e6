import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/org.ecoinwallet/index.js
var wallet = {
  id: "org.ecoinwallet",
  name: "ECOIN Wallet",
  homepage: "https://ecoinwallet.org",
  image_id: "9639c263-d590-4862-ba9f-d5c7c1878d00",
  app: {
    browser: null,
    ios: null,
    android: "https://play.google.com/store/apps/details?id=org.ecoinwallet&referrer=utm_source%3Dwalletconnect%26utm_medium%3Dreown%26utm_content%3Dlink",
    mac: null,
    windows: null,
    linux: null,
    chrome: null,
    firefox: null,
    safari: null,
    edge: null,
    opera: null
  },
  rdns: null,
  mobile: {
    native: "ecoinwallet://",
    universal: "https://ecoinwallet.org/link"
  },
  desktop: {
    native: null,
    universal: null
  }
};
export {
  wallet
};
//# sourceMappingURL=org-S4AAQEQU.js.map
