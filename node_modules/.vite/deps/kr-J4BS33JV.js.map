{"version": 3, "sources": ["../../thirdweb/src/react/web/wallets/shared/locale/kr.ts"], "sourcesContent": ["import type { InAppWalletLocale } from \"./types.js\";\nexport default {\n  signInWithGoogle: \"Google\",\n  signInWithFacebook: \"Facebook\",\n  signInWithApple: \"Apple\",\n  signInWithDiscord: \"Discord\",\n  emailPlaceholder: \"이메일 주소\",\n  submitEmail: \"계속하기\",\n  signIn: \"로그인\",\n  or: \"Or\",\n  emailRequired: \"이메일 주소가 필요합니다\",\n  invalidEmail: \"잘못된 이메일 주소\",\n  maxAccountsExceeded: \"계정 최대 수를 초과했습니다. 앱 개발자에게 알려주세요.\",\n  socialLoginScreen: {\n    title: \"로그인\",\n    instruction: \"팝업 창에서 계정에 로그인하세요\",\n    failed: \"로그인에 실패했습니다\",\n    retry: \"다시 시도\",\n  },\n  emailLoginScreen: {\n    title: \"로그인\",\n    enterCodeSendTo: \"전송된 인증 코드를 입력하세요\",\n    newDeviceDetected: \"새 기기가 감지되었습니다\",\n    enterRecoveryCode: \"처음 가입할 때 이메일로 전송된 복구 코드를 입력하세요\",\n    invalidCode: \"잘못된 인증 코드\",\n    invalidCodeOrRecoveryCode: \"잘못된 인증 코드 또는 복구 코드\",\n    verify: \"확인\",\n    failedToSendCode: \"인증 코드 전송에 실패했습니다\",\n    sendingCode: \"인증 코드 전송 중\",\n    resendCode: \"인증 코드 다시 보내기\",\n  },\n  createPassword: {\n    title: \"비밀번호 생성\",\n    instruction:\n      \"계정의 비밀번호를 설정하세요. 새 기기에서 연결할 때 이 비밀번호가 필요합니다.\",\n    saveInstruction: \"반드시 저장하세요\",\n    inputPlaceholder: \"비밀번호를 입력하세요\",\n    confirmation: \"비밀번호를 저장했습니다\",\n    submitButton: \"비밀번호 설정\",\n    failedToSetPassword: \"비밀번호 설정 실패\",\n  },\n  enterPassword: {\n    title: \"비밀번호를 입력하세요\",\n    instruction: \"계정의 비밀번호를 입력하세요\",\n    inputPlaceholder: \"비밀번호를 입력하세요\",\n    submitButton: \"확인\",\n    wrongPassword: \"잘못된 비밀번호\",\n  },\n  signInWithEmail: \"이메일로 로그인\",\n  invalidPhone: \"잘못된 전화번호\",\n  phonePlaceholder: \"전화번호\",\n  signInWithPhone: \"전화번호로 로그인\",\n  phoneRequired: \"전화번호가 필요합니다\",\n  passkey: \"비밀번호\",\n  linkWallet: \"지갑 연결\",\n  loginAsGuest: \"게스트로 로그인\",\n  signInWithWallet: \"지갑으로 로그인\",\n} satisfies InAppWalletLocale;\n"], "mappings": ";;;AACA,IAAA,aAAe;EACb,kBAAkB;EAClB,oBAAoB;EACpB,iBAAiB;EACjB,mBAAmB;EACnB,kBAAkB;EAClB,aAAa;EACb,QAAQ;EACR,IAAI;EACJ,eAAe;EACf,cAAc;EACd,qBAAqB;EACrB,mBAAmB;IACjB,OAAO;IACP,aAAa;IACb,QAAQ;IACR,OAAO;;EAET,kBAAkB;IAChB,OAAO;IACP,iBAAiB;IACjB,mBAAmB;IACnB,mBAAmB;IACnB,aAAa;IACb,2BAA2B;IAC3B,QAAQ;IACR,kBAAkB;IAClB,aAAa;IACb,YAAY;;EAEd,gBAAgB;IACd,OAAO;IACP,aACE;IACF,iBAAiB;IACjB,kBAAkB;IAClB,cAAc;IACd,cAAc;IACd,qBAAqB;;EAEvB,eAAe;IACb,OAAO;IACP,aAAa;IACb,kBAAkB;IAClB,cAAc;IACd,eAAe;;EAEjB,iBAAiB;EACjB,cAAc;EACd,kBAAkB;EAClB,iBAAiB;EACjB,eAAe;EACf,SAAS;EACT,YAAY;EACZ,cAAc;EACd,kBAAkB;;", "names": []}