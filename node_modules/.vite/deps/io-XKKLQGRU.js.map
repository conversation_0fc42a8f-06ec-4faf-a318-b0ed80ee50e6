{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.passpay/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.passpay\",\n  name: \"PassPay Wallet\",\n  homepage: \"https://www.passpay.io/\",\n  image_id: \"a1c337f5-c156-4ce8-763b-b4cc65f1c200\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/US/app/passpay-wallet-nft-%E4%BB%AE%E6%83%B3%E9%80%9A%E8%B2%A8-%E3%82%A6%E3%82%A9%E3%83%AC%E3%83%83%E3%83%88/id1645009398\",\n    android: null,\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"com.wallet.passpay://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}