{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/org.mugambo/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"org.mugambo\",\n  name: \"rss wallet\",\n  homepage: \"https://mugambo.org\",\n  image_id: \"920c743d-950f-4d53-64ec-d342e272e500\",\n  app: {\n    browser: null,\n    ios: null,\n    android: \"https://bo.centapey.com/rsswallet_2.0.2.3.apk\",\n    mac: null,\n    windows: null,\n    linux: \"https://bo.centapey.com/rsswallet_2.0.2.3.apk\",\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"rsswallet://rss.app.link\",\n    universal: \"https://rss.app.link/rsswallet\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}