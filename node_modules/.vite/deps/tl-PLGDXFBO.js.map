{"version": 3, "sources": ["../../thirdweb/src/react/web/ui/ConnectWallet/locale/tl.ts"], "sourcesContent": ["import type { ConnectLocale } from \"./types.js\";\n\nconst connectWalletLocalTl: ConnectLocale = {\n  id: \"tl_PH\",\n  signIn: \"Mag-sign in\",\n  defaultButtonTitle: \"Ikonekta ang Wallet\",\n  connecting: \"<PERSON>mokonekta\",\n  switchNetwork: \"Palitan ang Network\",\n  switchingNetwork: \"Papalitan ang Network\",\n  defaultModalTitle: \"Kumonekta\",\n  recommended: \"Inirerekomenda\",\n  installed: \"Naka-install\",\n  continueAsGuest: \"Magpatuloy bilang guest\",\n  connectAWallet: \"Kumonekta ng wallet\",\n  newToWallets: \"Bago sa mga wallet?\",\n  getStarted: \"<PERSON><PERSON><PERSON>\",\n  guest: \"Guest\",\n  buy: \"Bumili\",\n  transactions: \"Mga Transaksyon\",\n  payTransactions: \"Mga Transaksyong Fiat\",\n  walletTransactions: \"Mga Transaksyon sa Wallet\",\n  viewAllTransactions: \"Tingnan ang Lahat ng Transaksyon\",\n  send: \"Ipadala\",\n  receive: \"Matanggap\",\n  currentNetwork: \"Kasalukuyang network\",\n  switchAccount: \"<PERSON><PERSON><PERSON> ang Account\",\n  requestTestnetFunds: \"Humingi ng Testnet Funds\",\n  backupWallet: \"I-backup ang Wallet\",\n  guestWalletWarning:\n    \"Ito ay isang pansamantalang guest wallet. I-download mo ang backup para hindi ka mawalan ng access dito.\",\n  switchTo: \"Palitan ang\", // Used in \"Switch to <Wallet-Name>\"\n  connectedToSmartWallet: \"Smart Account\",\n  confirmInWallet: \"Kumpirmahin sa wallet\",\n  disconnectWallet: \"I-disconnect ang Wallet\",\n  copyAddress: \"Kopyahin ang Address\",\n  personalWallet: \"Personal na Wallet\",\n  smartWallet: \"Smart Wallet\",\n  or: \"O\",\n  goBackButton: \"Bumalik\",\n  passkeys: {\n    title: \"Mga Passkey\",\n    linkPasskey: \"I-link ang Passkey\",\n  },\n  welcomeScreen: {\n    defaultTitle: \"Ang iyong daan patungo sa decentralized na mundo\",\n    defaultSubtitle: \"Kumonekta ng wallet para magsimula\",\n  },\n  agreement: {\n    prefix: \"Sa pamamagitan ng pagkonekta, sumasang-ayon ka sa\",\n    termsOfService: \"Mga Tuntunin ng Serbisyo\",\n    and: \"&\",\n    privacyPolicy: \"Patakaran sa Privacy\",\n  },\n  networkSelector: {\n    title: \"Pumili ng Network\",\n    mainnets: \"Mainnets\",\n    testnets: \"Testnets\",\n    allNetworks: \"Lahat\",\n    addCustomNetwork: \"Magdagdag ng Custom Network\",\n    inputPlaceholder: \"Maghanap ng Network o Chain ID\",\n    categoryLabel: {\n      recentlyUsed: \"Kamakailang Ginamit\",\n      popular: \"Sikat\",\n      others: \"Lahat ng Networks\",\n    },\n    loading: \"Loading\",\n    failedToSwitch: \"Hindi nagawa ang pagpapalit ng network\",\n  },\n  receiveFundsScreen: {\n    title: \"Matanggap ang Pondo\",\n    instruction: \"Kopyahin ang address ng wallet para ipadala ang pondo dito\",\n  },\n  sendFundsScreen: {\n    title: \"Ipadala ang Pondo\",\n    submitButton: \"Ipadala\",\n    token: \"Token\",\n    sendTo: \"Ipadala sa\",\n    amount: \"Halaga\",\n    successMessage: \"Tagumpay ang Transaksyon\",\n    invalidAddress: \"Hindi wastong Address\",\n    noTokensFound: \"Walang Nakitang Tokens\",\n    searchToken: \"Maghanap o I-paste ang token address\",\n    transactionFailed: \"Nabigo ang Transaksyon\",\n    transactionRejected: \"Tinanggihan ang Transaksyon\",\n    insufficientFunds: \"Kulang ang Pondo\",\n    selectTokenTitle: \"Pumili ng Token\",\n    sending: \"Ipinapadala\",\n  },\n  signatureScreen: {\n    instructionScreen: {\n      title: \"Mag-sign in\",\n      instruction:\n        \"Mangyaring pirmahan ang kahilingan ng mensahe sa iyong wallet para magpatuloy\",\n      signInButton: \"Mag-sign in\",\n      disconnectWallet: \"I-disconnect ang Wallet\",\n    },\n    signingScreen: {\n      title: \"Pagsisign-in\",\n      prompt: \"Pirmahan ang kahilingan ng signature sa iyong wallet\",\n      promptForSafe:\n        \"Pirmahan ang kahilingan ng signature sa iyong wallet at aprubahan ang transaksyon sa Safe\",\n      approveTransactionInSafe: \"Aprubahan ang transaksyon sa Safe\",\n      tryAgain: \"Subukan muli\",\n      failedToSignIn: \"Hindi nagawa ang pagsisign-in\",\n      inProgress: \"Naghihintay ng Kumpirmasyon\",\n    },\n  },\n  manageWallet: {\n    title: \"Pamahalaan ang Wallet\",\n    linkedProfiles: \"Linked Profiles\",\n    linkProfile: \"Link a Profile\",\n    connectAnApp: \"Ikonekta ang isang App\",\n    exportPrivateKey: \"I-export ang Pribadong Susi\",\n  },\n  viewFunds: {\n    title: \"Tingnan ang Pondo\",\n    viewNFTs: \"Tingnan ang mga NFT\",\n    viewTokens: \"Tingnan ang mga Token\",\n    viewAssets: \"Tingnan ang mga Asset\",\n  },\n};\n\nexport default connectWalletLocalTl;\n"], "mappings": ";;;AAEA,IAAM,uBAAsC;EAC1C,IAAI;EACJ,QAAQ;EACR,oBAAoB;EACpB,YAAY;EACZ,eAAe;EACf,kBAAkB;EAClB,mBAAmB;EACnB,aAAa;EACb,WAAW;EACX,iBAAiB;EACjB,gBAAgB;EAChB,cAAc;EACd,YAAY;EACZ,OAAO;EACP,KAAK;EACL,cAAc;EACd,iBAAiB;EACjB,oBAAoB;EACpB,qBAAqB;EACrB,MAAM;EACN,SAAS;EACT,gBAAgB;EAChB,eAAe;EACf,qBAAqB;EACrB,cAAc;EACd,oBACE;EACF,UAAU;;EACV,wBAAwB;EACxB,iBAAiB;EACjB,kBAAkB;EAClB,aAAa;EACb,gBAAgB;EAChB,aAAa;EACb,IAAI;EACJ,cAAc;EACd,UAAU;IACR,OAAO;IACP,aAAa;;EAEf,eAAe;IACb,cAAc;IACd,iBAAiB;;EAEnB,WAAW;IACT,QAAQ;IACR,gBAAgB;IAChB,KAAK;IACL,eAAe;;EAEjB,iBAAiB;IACf,OAAO;IACP,UAAU;IACV,UAAU;IACV,aAAa;IACb,kBAAkB;IAClB,kBAAkB;IAClB,eAAe;MACb,cAAc;MACd,SAAS;MACT,QAAQ;;IAEV,SAAS;IACT,gBAAgB;;EAElB,oBAAoB;IAClB,OAAO;IACP,aAAa;;EAEf,iBAAiB;IACf,OAAO;IACP,cAAc;IACd,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,gBAAgB;IAChB,gBAAgB;IAChB,eAAe;IACf,aAAa;IACb,mBAAmB;IACnB,qBAAqB;IACrB,mBAAmB;IACnB,kBAAkB;IAClB,SAAS;;EAEX,iBAAiB;IACf,mBAAmB;MACjB,OAAO;MACP,aACE;MACF,cAAc;MACd,kBAAkB;;IAEpB,eAAe;MACb,OAAO;MACP,QAAQ;MACR,eACE;MACF,0BAA0B;MAC1B,UAAU;MACV,gBAAgB;MAChB,YAAY;;;EAGhB,cAAc;IACZ,OAAO;IACP,gBAAgB;IAChB,aAAa;IACb,cAAc;IACd,kBAAkB;;EAEpB,WAAW;IACT,OAAO;IACP,UAAU;IACV,YAAY;IACZ,YAAY;;;AAIhB,IAAA,aAAe;", "names": []}