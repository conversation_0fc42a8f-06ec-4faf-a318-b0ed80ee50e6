{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/xyz.echooo/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"xyz.echooo\",\n  name: \"Echooo Wallet\",\n  homepage: \"https://www.echooo.xyz/\",\n  image_id: \"a7b1de20-bafd-4ab9-c31d-7d398cc90a00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/echooo-crypto-aa-wallet-defi/id6446883725\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.smartwallet.app&hl=en_US&gl=US\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome:\n      \"https://chromewebstore.google.com/detail/echooo-wallet/lcmncloheoekhbmljjlhdlaobkedjbgd\",\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"echooo://echooo.valleysound.xyz/vss/walletconnect\",\n    universal: \"https://api.valleysound.xyz/vss/*\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QACE;IACF,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}