{"version": 3, "sources": ["../../thirdweb/src/react/web/wallets/ecosystem/EcosystemWalletFormUI.tsx", "../../thirdweb/src/react/web/wallets/ecosystem/EcosystemWalletHeader.tsx", "../../thirdweb/src/react/web/wallets/ecosystem/EcosystemWalletConnectUI.tsx"], "sourcesContent": ["\"use client\";\nimport { useState } from \"react\";\nimport type { Chain } from \"../../../../chains/types.js\";\nimport type { ThirdwebClient } from \"../../../../client/client.js\";\nimport type { Wallet } from \"../../../../wallets/interfaces/wallet.js\";\nimport type { EcosystemWalletId } from \"../../../../wallets/wallet-types.js\";\nimport { TOS } from \"../../ui/ConnectWallet/Modal/TOS.js\";\nimport { useScreenContext } from \"../../ui/ConnectWallet/Modal/screen.js\";\nimport { PoweredByThirdweb } from \"../../ui/ConnectWallet/PoweredByTW.js\";\nimport type { ConnectLocale } from \"../../ui/ConnectWallet/locale/types.js\";\nimport { Spacer } from \"../../ui/components/Spacer.js\";\nimport { Container, ModalHeader } from \"../../ui/components/basic.js\";\nimport { ConnectWalletSocialOptions } from \"../shared/ConnectWalletSocialOptions.js\";\nimport type { InAppWalletLocale } from \"../shared/locale/types.js\";\nimport { EcosystemWalletHeader } from \"./EcosystemWalletHeader.js\";\n\ntype EcosystemWalletFormUIProps = {\n  select: () => void;\n  done: () => void;\n  locale: InAppWalletLocale;\n  wallet: Wallet<EcosystemWalletId>;\n  goBack?: () => void;\n  size: \"compact\" | \"wide\";\n  meta: {\n    title?: string;\n    titleIconUrl?: string;\n    showThirdwebBranding?: boolean;\n    termsOfServiceUrl?: string;\n    privacyPolicyUrl?: string;\n    requireApproval?: boolean;\n  };\n  client: ThirdwebClient;\n  chain: Chain | undefined;\n  connectLocale: ConnectLocale;\n  isLinking?: boolean;\n};\n\n/**\n * @internal\n */\nexport function EcosystemWalletFormUIScreen(props: EcosystemWalletFormUIProps) {\n  const isCompact = props.size === \"compact\";\n  const { initialScreen, screen } = useScreenContext();\n  // This is only used when requireApproval is true to accept the TOS\n  const [isApproved, setIsApproved] = useState(false);\n\n  const onBack =\n    screen === props.wallet && initialScreen === props.wallet\n      ? undefined\n      : props.goBack;\n\n  return (\n    <Container\n      fullHeight\n      flex=\"column\"\n      p=\"lg\"\n      animate=\"fadein\"\n      style={{\n        minHeight: \"250px\",\n      }}\n    >\n      {props.isLinking ? (\n        <ModalHeader\n          title={props.connectLocale.manageWallet.linkProfile}\n          onBack={onBack}\n        />\n      ) : (\n        <EcosystemWalletHeader\n          client={props.client}\n          onBack={isCompact ? onBack : undefined}\n          wallet={props.wallet}\n        />\n      )}\n      <Spacer y=\"lg\" />\n\n      <Container\n        expand\n        flex=\"column\"\n        center=\"y\"\n        p={isCompact ? undefined : \"lg\"}\n      >\n        <ConnectWalletSocialOptions\n          disabled={props.meta.requireApproval && !isApproved}\n          {...props}\n        />\n      </Container>\n\n      {isCompact &&\n        (props.meta.showThirdwebBranding !== false ||\n          props.meta.termsOfServiceUrl ||\n          props.meta.privacyPolicyUrl) && <Spacer y=\"xl\" />}\n\n      <Container flex=\"column\" gap=\"lg\">\n        <TOS\n          termsOfServiceUrl={props.meta.termsOfServiceUrl}\n          privacyPolicyUrl={props.meta.privacyPolicyUrl}\n          locale={props.connectLocale.agreement}\n          requireApproval={props.meta.requireApproval}\n          onApprove={() => {\n            setIsApproved(!isApproved);\n          }}\n          isApproved={isApproved}\n        />\n\n        {props.meta.showThirdwebBranding !== false && <PoweredByThirdweb />}\n      </Container>\n    </Container>\n  );\n}\n", "\"use client\";\nimport type { ThirdwebClient } from \"../../../../client/client.js\";\nimport type { Wallet } from \"../../../../wallets/interfaces/wallet.js\";\nimport type { EcosystemWalletId } from \"../../../../wallets/wallet-types.js\";\nimport { iconSize, radius } from \"../../../core/design-system/index.js\";\nimport { useWalletInfo } from \"../../../core/utils/wallet.js\";\nimport { Img } from \"../../ui/components/Img.js\";\nimport { Skeleton } from \"../../ui/components/Skeleton.js\";\nimport { ModalHeader } from \"../../ui/components/basic.js\";\nimport { ModalTitle } from \"../../ui/components/modalElements.js\";\n\n/**\n * @internal\n */\nexport function EcosystemWalletHeader(props: {\n  wallet: Wallet<EcosystemWalletId>;\n  client: ThirdwebClient;\n  onBack?: () => void;\n}) {\n  const walletInfo = useWalletInfo(props.wallet.id);\n\n  return (\n    <ModalHeader\n      onBack={props.onBack}\n      title={\n        walletInfo.isLoading ? (\n          <Skeleton height=\"24px\" width=\"200px\" />\n        ) : (\n          <>\n            {!walletInfo.data?.image_id ? null : (\n              <Img\n                src={walletInfo.data?.image_id}\n                style={{\n                  borderRadius: radius.sm,\n                }}\n                width={iconSize.md}\n                height={iconSize.md}\n                client={props.client}\n              />\n            )}\n            <ModalTitle>{walletInfo.data?.name}</ModalTitle>\n          </>\n        )\n      }\n      leftAligned\n    />\n  );\n}\n", "\"use client\";\nimport type { Chain } from \"../../../../chains/types.js\";\nimport type { ThirdwebClient } from \"../../../../client/client.js\";\nimport type { Wallet } from \"../../../../wallets/interfaces/wallet.js\";\nimport type { EcosystemWalletId } from \"../../../../wallets/wallet-types.js\";\nimport {\n  useSelectionData,\n  useSetSelectionData,\n} from \"../../providers/wallet-ui-states-provider.js\";\nimport type { ConnectLocale } from \"../../ui/ConnectWallet/locale/types.js\";\nimport { WalletAuth } from \"../in-app/WalletAuth.js\";\nimport { useInAppWalletLocale } from \"../in-app/useInAppWalletLocale.js\";\nimport type { ConnectWalletSelectUIState } from \"../shared/ConnectWalletSocialOptions.js\";\nimport { GuestLogin } from \"../shared/GuestLogin.js\";\nimport { LoadingScreen } from \"../shared/LoadingScreen.js\";\nimport { OTPLoginUI } from \"../shared/OTPLoginUI.js\";\nimport { PassKeyLogin } from \"../shared/PassKeyLogin.js\";\nimport { SocialLogin } from \"../shared/SocialLogin.js\";\nimport { EcosystemWalletFormUIScreen } from \"./EcosystemWalletFormUI.js\";\n\n/**\n *\n * @internal\n */\nfunction EcosystemWalletConnectUI(props: {\n  wallet: Wallet<EcosystemWalletId>;\n  done: () => void;\n  goBack?: () => void;\n  client: ThirdwebClient;\n  chain: Chain | undefined;\n  connectLocale: ConnectLocale;\n  size: \"compact\" | \"wide\";\n  meta: {\n    title?: string;\n    titleIconUrl?: string;\n    showThirdwebBranding?: boolean;\n    termsOfServiceUrl?: string;\n    privacyPolicyUrl?: string;\n  };\n  walletConnect: { projectId?: string } | undefined;\n  isLinking?: boolean;\n}) {\n  const data = useSelectionData();\n  const setSelectionData = useSetSelectionData();\n  const state = data as ConnectWalletSelectUIState;\n  const localeId = props.connectLocale.id;\n  const locale = useInAppWalletLocale(localeId);\n\n  if (!locale) {\n    return <LoadingScreen />;\n  }\n\n  const goBackToMain = () => {\n    if (props.size === \"compact\") {\n      props.goBack?.();\n    }\n    setSelectionData({});\n  };\n\n  const done = () => {\n    props.done();\n    setSelectionData({});\n  };\n\n  const otpUserInfo = state?.emailLogin\n    ? { email: state.emailLogin }\n    : state?.phoneLogin\n      ? { phone: state.phoneLogin }\n      : undefined;\n\n  if (otpUserInfo) {\n    return (\n      <OTPLoginUI\n        userInfo={otpUserInfo}\n        locale={locale}\n        done={done}\n        goBack={goBackToMain}\n        wallet={props.wallet}\n        chain={props.chain}\n        client={props.client}\n        size={props.size}\n        isLinking={props.isLinking}\n      />\n    );\n  }\n\n  if (state?.passkeyLogin) {\n    return (\n      <PassKeyLogin\n        locale={props.connectLocale}\n        wallet={props.wallet}\n        done={done}\n        onBack={goBackToMain}\n        chain={props.chain}\n        client={props.client}\n        size={props.size}\n        isLinking={props.isLinking}\n      />\n    );\n  }\n\n  if (state?.socialLogin) {\n    return (\n      <SocialLogin\n        socialAuth={state.socialLogin.type}\n        locale={locale}\n        done={done}\n        goBack={goBackToMain}\n        wallet={props.wallet}\n        state={state}\n        chain={props.chain}\n        client={props.client}\n        size={props.size}\n        connectLocale={props.connectLocale}\n        isLinking={props.isLinking}\n      />\n    );\n  }\n\n  if (state?.walletLogin) {\n    return (\n      <WalletAuth\n        meta={props.meta}\n        chain={props.chain}\n        inAppLocale={locale}\n        walletConnect={props.walletConnect}\n        wallet={props.wallet}\n        client={props.client}\n        size={props.size}\n        done={done}\n        onBack={goBackToMain || (() => setSelectionData({}))}\n        locale={props.connectLocale}\n        isLinking={state.walletLogin.linking}\n      />\n    );\n  }\n\n  if (state?.guestLogin) {\n    return (\n      <GuestLogin\n        locale={locale}\n        done={done}\n        goBack={goBackToMain}\n        wallet={props.wallet}\n        state={state}\n        client={props.client}\n        size={props.size}\n        connectLocale={props.connectLocale}\n      />\n    );\n  }\n\n  return (\n    <EcosystemWalletFormUIScreen\n      select={() => {}}\n      locale={locale}\n      done={done}\n      goBack={props.goBack}\n      wallet={props.wallet}\n      chain={props.chain}\n      client={props.client}\n      size={props.size}\n      connectLocale={props.connectLocale}\n      meta={props.meta}\n      isLinking={props.isLinking}\n    />\n  );\n}\n\nexport default EcosystemWalletConnectUI;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,mBAAyB;;;;ACanB,SAAU,sBAAsB,OAIrC;AAlBD;AAmBE,QAAM,aAAa,cAAc,MAAM,OAAO,EAAE;AAEhD,aACE,mBAAAA,KAAC,aAAW,EACV,QAAQ,MAAM,QACd,OACE,WAAW,gBACT,mBAAAA,KAAC,UAAQ,EAAC,QAAO,QAAO,OAAM,QAAO,CAAA,QAErC,mBAAAC,MAAA,mBAAAC,UAAA,EAAA,UAAA,CACG,GAAC,gBAAW,SAAX,mBAAiB,YAAW,WAC5B,mBAAAF,KAAC,KAAG,EACF,MAAK,gBAAW,SAAX,mBAAiB,UACtB,OAAO;IACL,cAAc,OAAO;KAEvB,OAAO,SAAS,IAChB,QAAQ,SAAS,IACjB,QAAQ,MAAM,OAAM,CAAA,OAGxB,mBAAAA,KAAC,YAAU,EAAA,WAAE,gBAAW,SAAX,mBAAiB,KAAI,CAAA,CAAc,EAAA,CAAA,GAItD,aAAW,KAAA,CAAA;AAGjB;;;ADPM,SAAU,4BAA4B,OAAiC;AAC3E,QAAM,YAAY,MAAM,SAAS;AACjC,QAAM,EAAE,eAAe,OAAM,IAAK,iBAAgB;AAElD,QAAM,CAAC,YAAY,aAAa,QAAI,uBAAS,KAAK;AAElD,QAAM,SACJ,WAAW,MAAM,UAAU,kBAAkB,MAAM,SAC/C,SACA,MAAM;AAEZ,aACE,oBAAAG,MAAC,WAAS,EACR,YAAU,MACV,MAAK,UACL,GAAE,MACF,SAAQ,UACR,OAAO;IACL,WAAW;KACZ,UAAA,CAEA,MAAM,gBACL,oBAAAC,KAAC,aAAW,EACV,OAAO,MAAM,cAAc,aAAa,aACxC,OAAc,CAAA,QAGhB,oBAAAA,KAAC,uBAAqB,EACpB,QAAQ,MAAM,QACd,QAAQ,YAAY,SAAS,QAC7B,QAAQ,MAAM,OAAM,CAAA,OAGxB,oBAAAA,KAAC,QAAM,EAAC,GAAE,KAAI,CAAA,OAEd,oBAAAA,KAAC,WAAS,EACR,QAAM,MACN,MAAK,UACL,QAAO,KACP,GAAG,YAAY,SAAY,MAAI,cAE/B,oBAAAA,KAAC,4BAA0B,EACzB,UAAU,MAAM,KAAK,mBAAmB,CAAC,YAAU,GAC/C,MAAK,CAAA,EACT,CAAA,GAGH,cACE,MAAM,KAAK,yBAAyB,SACnC,MAAM,KAAK,qBACX,MAAM,KAAK,yBAAqB,oBAAAA,KAAC,QAAM,EAAC,GAAE,KAAI,CAAA,OAElD,oBAAAD,MAAC,WAAS,EAAC,MAAK,UAAS,KAAI,MAAI,UAAA,KAC/B,oBAAAC,KAAC,KAAG,EACF,mBAAmB,MAAM,KAAK,mBAC9B,kBAAkB,MAAM,KAAK,kBAC7B,QAAQ,MAAM,cAAc,WAC5B,iBAAiB,MAAM,KAAK,iBAC5B,WAAW,MAAK;AACd,kBAAc,CAAC,UAAU;EAC3B,GACA,WAAsB,CAAA,GAGvB,MAAM,KAAK,yBAAyB,aAAS,oBAAAA,KAAC,mBAAiB,CAAA,CAAA,CAAG,EAAA,CAAA,CACzD,EAAA,CAAA;AAGlB;;;AEpFA,SAAS,yBAAyB,OAiBjC;AACC,QAAM,OAAO,iBAAgB;AAC7B,QAAM,mBAAmB,oBAAmB;AAC5C,QAAM,QAAQ;AACd,QAAM,WAAW,MAAM,cAAc;AACrC,QAAM,SAAS,qBAAqB,QAAQ;AAE5C,MAAI,CAAC,QAAQ;AACX,eAAO,oBAAAC,KAAC,eAAa,CAAA,CAAA;EACvB;AAEA,QAAM,eAAe,MAAK;AApD5B;AAqDI,QAAI,MAAM,SAAS,WAAW;AAC5B,kBAAM,WAAN;IACF;AACA,qBAAiB,CAAA,CAAE;EACrB;AAEA,QAAM,OAAO,MAAK;AAChB,UAAM,KAAI;AACV,qBAAiB,CAAA,CAAE;EACrB;AAEA,QAAM,eAAc,+BAAO,cACvB,EAAE,OAAO,MAAM,WAAU,KACzB,+BAAO,cACL,EAAE,OAAO,MAAM,WAAU,IACzB;AAEN,MAAI,aAAa;AACf,eACE,oBAAAA,KAAC,YAAU,EACT,UAAU,aACV,QACA,MACA,QAAQ,cACR,QAAQ,MAAM,QACd,OAAO,MAAM,OACb,QAAQ,MAAM,QACd,MAAM,MAAM,MACZ,WAAW,MAAM,UAAS,CAAA;EAGhC;AAEA,MAAI,+BAAO,cAAc;AACvB,eACE,oBAAAA,KAAC,cAAY,EACX,QAAQ,MAAM,eACd,QAAQ,MAAM,QACd,MACA,QAAQ,cACR,OAAO,MAAM,OACb,QAAQ,MAAM,QACd,MAAM,MAAM,MACZ,WAAW,MAAM,UAAS,CAAA;EAGhC;AAEA,MAAI,+BAAO,aAAa;AACtB,eACE,oBAAAA,KAAC,aAAW,EACV,YAAY,MAAM,YAAY,MAC9B,QACA,MACA,QAAQ,cACR,QAAQ,MAAM,QACd,OACA,OAAO,MAAM,OACb,QAAQ,MAAM,QACd,MAAM,MAAM,MACZ,eAAe,MAAM,eACrB,WAAW,MAAM,UAAS,CAAA;EAGhC;AAEA,MAAI,+BAAO,aAAa;AACtB,eACE,oBAAAA,KAAC,YAAU,EACT,MAAM,MAAM,MACZ,OAAO,MAAM,OACb,aAAa,QACb,eAAe,MAAM,eACrB,QAAQ,MAAM,QACd,QAAQ,MAAM,QACd,MAAM,MAAM,MACZ,MACA,QAAQ,iBAAiB,MAAM,iBAAiB,CAAA,CAAE,IAClD,QAAQ,MAAM,eACd,WAAW,MAAM,YAAY,QAAO,CAAA;EAG1C;AAEA,MAAI,+BAAO,YAAY;AACrB,eACE,oBAAAA,KAAC,YAAU,EACT,QACA,MACA,QAAQ,cACR,QAAQ,MAAM,QACd,OACA,QAAQ,MAAM,QACd,MAAM,MAAM,MACZ,eAAe,MAAM,cAAa,CAAA;EAGxC;AAEA,aACE,oBAAAA,KAAC,6BAA2B,EAC1B,QAAQ,MAAK;EAAE,GACf,QACA,MACA,QAAQ,MAAM,QACd,QAAQ,MAAM,QACd,OAAO,MAAM,OACb,QAAQ,MAAM,QACd,MAAM,MAAM,MACZ,eAAe,MAAM,eACrB,MAAM,MAAM,MACZ,WAAW,MAAM,UAAS,CAAA;AAGhC;AAEA,IAAA,mCAAe;", "names": ["_jsx", "_jsxs", "_Fragment", "_jsxs", "_jsx", "_jsx"]}