import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/io.wallypto/index.js
var wallet = {
  id: "io.wallypto",
  name: "<PERSON><PERSON><PERSON>",
  homepage: "https://wallypto.io",
  image_id: "00684f38-f9f9-40b6-6b6e-33891434f400",
  app: {
    browser: null,
    ios: "https://apps.apple.com/us/app/wallypto-blockchain-wallet/id1639302472",
    android: "https://play.google.com/store/apps/details?id=xyz.wallypto",
    mac: null,
    windows: null,
    linux: null,
    chrome: null,
    firefox: null,
    safari: null,
    edge: null,
    opera: null
  },
  rdns: null,
  mobile: {
    native: "wallypto://",
    universal: null
  },
  desktop: {
    native: null,
    universal: null
  }
};
export {
  wallet
};
//# sourceMappingURL=io-WNSEWZCZ.js.map
