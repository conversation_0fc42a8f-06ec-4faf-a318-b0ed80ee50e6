{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/org.uniswap/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"org.uniswap\",\n  name: \"Uniswap Wallet\",\n  homepage: \"https://uniswap.org\",\n  image_id: \"6033c33c-0773-48e3-a12f-e7fbf409e700\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/uniswap-wallet/id6443944476\",\n    android: \"https://play.google.com/store/apps/details?id=com.uniswap.mobile\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"uniswap://\",\n    universal: \"https://uniswap.org/app\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}