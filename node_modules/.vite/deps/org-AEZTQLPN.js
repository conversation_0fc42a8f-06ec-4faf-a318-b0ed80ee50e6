import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/org.mathwallet/index.js
var wallet = {
  id: "org.mathwallet",
  name: "MathWallet",
  homepage: "https://mathwallet.org/",
  image_id: "26a8f588-3231-4411-60ce-5bb6b805a700",
  app: {
    browser: "https://chrome.google.com/webstore/detail/math-wallet/afbcbjpbpfadlkmhmclhkeeodmamcflc",
    ios: "https://apps.apple.com/us/app/mathwallet5/id1582612388",
    android: "https://play.google.com/store/apps/details?id=com.mathwallet.android",
    mac: null,
    windows: null,
    linux: null,
    chrome: "https://chrome.google.com/webstore/detail/math-wallet/afbcbjpbpfadlkmhmclhkeeodmamcflc",
    firefox: null,
    safari: null,
    edge: "https://microsoftedge.microsoft.com/addons/detail/math-wallet/dfeccadlilpndjjohbjdblepmjeahlmm",
    opera: null
  },
  rdns: null,
  mobile: {
    native: "mathwallet://",
    universal: "https://www.mathwallet.org"
  },
  desktop: {
    native: null,
    universal: "https://chrome.google.com/webstore/detail/math-wallet/afbcbjpbpfadlkmhmclhkeeodmamcflc"
  }
};
export {
  wallet
};
//# sourceMappingURL=org-AEZTQLPN.js.map
