{"version": 3, "sources": ["../../thirdweb/src/react/web/ui/ConnectWallet/locale/ja.ts"], "sourcesContent": ["import type { ConnectLocale } from \"./types.js\";\n\nconst connectWalletLocalJa: ConnectLocale = {\n  id: \"ja_JP\",\n  signIn: \"サインイン\",\n  defaultButtonTitle: \"ウォレット接続\",\n  connecting: \"接続中\",\n  switchNetwork: \"ネットワークを切り替える\",\n  switchingNetwork: \"ネットワークの切替中\",\n  defaultModalTitle: \"接続\",\n  recommended: \"推奨\",\n  installed: \"インストール済み\",\n  continueAsGuest: \"ゲストとして続ける\",\n  connectAWallet: \"ウォレットを接続する\",\n  newToWallets: \"ウォレットは初めてですか？\",\n  getStarted: \"始める\",\n  guest: \"ゲスト\",\n  send: \"送る\",\n  receive: \"受け取る\",\n  currentNetwork: \"現在のネットワーク\",\n  switchAccount: \"アカウントを切り替える\",\n  requestTestnetFunds: \"テストネットの資金をリクエストする\",\n  buy: \"購入\",\n  transactions: \"取引\",\n  payTransactions: \"支払い取引\",\n  walletTransactions: \"ウォレット取引\",\n  viewAllTransactions: \"全ての取引を表示\",\n  backupWallet: \"ウォレットのバックアップ\",\n  guestWalletWarning:\n    \"これは一時的なゲストウォレットです。アクセスできなくなることを防ぐため、バックアップをしてください\",\n  switchTo: \"切り替え先\", // Used in \"Switch to <Wallet-Name>\"\n  connectedToSmartWallet: \"スマートウォレットに接続済み\",\n  confirmInWallet: \"ウォレットで確認\",\n  disconnectWallet: \"ウォレットの切断\",\n  copyAddress: \"アドレスをコピー\",\n  personalWallet: \"パーソナルウォレット\",\n  smartWallet: \"スマートウォレット\",\n  or: \"または\",\n  goBackButton: \"戻る\", // TODO - check translation\n  passkeys: {\n    linkPasskey: \"パスキーをリンクする\",\n    title: \"パスキー\",\n  },\n  welcomeScreen: {\n    defaultTitle: \"分散型世界へのゲートウェイ\",\n    defaultSubtitle: \"始めるためにウォレットを接続してください\",\n  },\n  agreement: {\n    prefix: \"接続することで、以下に同意したことになります：\",\n    termsOfService: \"利用規約\",\n    and: \"および\",\n    privacyPolicy: \"プライバシーポリシー\",\n  },\n  networkSelector: {\n    title: \"ネットワークの選択\",\n    mainnets: \"メインネット\",\n    testnets: \"テストネット\",\n    allNetworks: \"すべて\",\n    addCustomNetwork: \"カスタムネットワークを追加\",\n    inputPlaceholder: \"ネットワーク名またはチェーンIDを検索\",\n    categoryLabel: {\n      recentlyUsed: \"最近使用したもの\",\n      popular: \"人気\",\n      others: \"全てのネットワーク\",\n    },\n    loading: \"読み込み中\",\n    failedToSwitch: \"ネットワークの切替に失敗しました\",\n  },\n  receiveFundsScreen: {\n    title: \"資金を受け取る\",\n    instruction:\n      \"このウォレットに資金を送るためのウォレットアドレスをコピーしてください\",\n  },\n  sendFundsScreen: {\n    title: \"資金の送付\",\n    submitButton: \"送信\",\n    token: \"トークン\",\n    sendTo: \"送信先\",\n    amount: \"金額\",\n    successMessage: \"取引成功\",\n    invalidAddress: \"無効なアドレス\",\n    noTokensFound: \"トークンが見つかりません\",\n    searchToken: \"トークンのアドレスを検索するか、貼り付けてください\",\n    transactionFailed: \"取引に失敗しました\",\n    transactionRejected: \"取引が拒否されました\",\n    insufficientFunds: \"資金が不足しています\",\n    selectTokenTitle: \"トークンを選択\",\n    sending: \"送信中\",\n  },\n  signatureScreen: {\n    instructionScreen: {\n      title: \"サインイン\",\n      instruction:\n        \"続行するためにウォレットでメッセージリクエストにサインしてください\",\n      signInButton: \"サインイン\",\n      disconnectWallet: \"ウォレットの切断\",\n    },\n    signingScreen: {\n      title: \"サインイン中\",\n      prompt: \"ウォレットで署名リクエストにサインしてください\",\n      promptForSafe:\n        \"ウォレットで署名リクエストにサインし、Safeで取引を承認してください\",\n      approveTransactionInSafe: \"Safeで取引を承認\",\n      tryAgain: \"再試行\",\n      failedToSignIn: \"サインインに失敗しました\",\n      inProgress: \"確認待ち\",\n    },\n  },\n  manageWallet: {\n    title: \"ウォレットを管理\",\n    linkedProfiles: \"リンクされたプロファイル\",\n    linkProfile: \"プロフィールをリンクする\",\n    connectAnApp: \"アプリを接続\",\n    exportPrivateKey: \"秘密鍵をエクスポート\",\n  },\n  viewFunds: {\n    title: \"資金を表示\",\n    viewNFTs: \"NFTを表示\",\n    viewTokens: \"トークンを表示\",\n    viewAssets: \"資産を表示\",\n  },\n};\n\nexport default connectWalletLocalJa;\n"], "mappings": ";;;AAEA,IAAM,uBAAsC;EAC1C,IAAI;EACJ,QAAQ;EACR,oBAAoB;EACpB,YAAY;EACZ,eAAe;EACf,kBAAkB;EAClB,mBAAmB;EACnB,aAAa;EACb,WAAW;EACX,iBAAiB;EACjB,gBAAgB;EAChB,cAAc;EACd,YAAY;EACZ,OAAO;EACP,MAAM;EACN,SAAS;EACT,gBAAgB;EAChB,eAAe;EACf,qBAAqB;EACrB,KAAK;EACL,cAAc;EACd,iBAAiB;EACjB,oBAAoB;EACpB,qBAAqB;EACrB,cAAc;EACd,oBACE;EACF,UAAU;;EACV,wBAAwB;EACxB,iBAAiB;EACjB,kBAAkB;EAClB,aAAa;EACb,gBAAgB;EAChB,aAAa;EACb,IAAI;EACJ,cAAc;;EACd,UAAU;IACR,aAAa;IACb,OAAO;;EAET,eAAe;IACb,cAAc;IACd,iBAAiB;;EAEnB,WAAW;IACT,QAAQ;IACR,gBAAgB;IAChB,KAAK;IACL,eAAe;;EAEjB,iBAAiB;IACf,OAAO;IACP,UAAU;IACV,UAAU;IACV,aAAa;IACb,kBAAkB;IAClB,kBAAkB;IAClB,eAAe;MACb,cAAc;MACd,SAAS;MACT,QAAQ;;IAEV,SAAS;IACT,gBAAgB;;EAElB,oBAAoB;IAClB,OAAO;IACP,aACE;;EAEJ,iBAAiB;IACf,OAAO;IACP,cAAc;IACd,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,gBAAgB;IAChB,gBAAgB;IAChB,eAAe;IACf,aAAa;IACb,mBAAmB;IACnB,qBAAqB;IACrB,mBAAmB;IACnB,kBAAkB;IAClB,SAAS;;EAEX,iBAAiB;IACf,mBAAmB;MACjB,OAAO;MACP,aACE;MACF,cAAc;MACd,kBAAkB;;IAEpB,eAAe;MACb,OAAO;MACP,QAAQ;MACR,eACE;MACF,0BAA0B;MAC1B,UAAU;MACV,gBAAgB;MAChB,YAAY;;;EAGhB,cAAc;IACZ,OAAO;IACP,gBAAgB;IAChB,aAAa;IACb,cAAc;IACd,kBAAkB;;EAEpB,WAAW;IACT,OAAO;IACP,UAAU;IACV,YAAY;IACZ,YAAY;;;AAIhB,IAAA,aAAe;", "names": []}