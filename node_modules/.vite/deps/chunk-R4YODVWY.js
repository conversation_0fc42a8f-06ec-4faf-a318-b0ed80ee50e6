import {
  stringify
} from "./chunk-2CIJO3V3.js";
import {
  getClientFetch
} from "./chunk-RJUQUX6Y.js";
import {
  getThirdwebBaseUrl
} from "./chunk-OSFP2VB7.js";

// node_modules/thirdweb/dist/esm/analytics/track/index.js
async function track({ client, ecosystem, data }) {
  const fetch = getClientFetch(client, ecosystem);
  const event = {
    source: "sdk",
    ...data
  };
  return fetch(`${getThirdwebBaseUrl("analytics")}/event`, {
    method: "POST",
    body: stringify(event)
  }).catch(() => {
  });
}

export {
  track
};
//# sourceMappingURL=chunk-R4YODVWY.js.map
