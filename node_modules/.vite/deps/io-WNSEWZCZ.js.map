{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.wallypto/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.wallypto\",\n  name: \"<PERSON><PERSON><PERSON>\",\n  homepage: \"https://wallypto.io\",\n  image_id: \"00684f38-f9f9-40b6-6b6e-33891434f400\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/wallypto-blockchain-wallet/id1639302472\",\n    android: \"https://play.google.com/store/apps/details?id=xyz.wallypto\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"wallypto://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}