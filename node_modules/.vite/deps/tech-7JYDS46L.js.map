{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/tech.okto/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"tech.okto\",\n  name: \"<PERSON><PERSON>\",\n  homepage: \"https://okto.tech/\",\n  image_id: \"154c69b7-9bb1-4010-5b4c-6b37eeda8900\",\n  app: {\n    browser:\n      \"https://okto.go.link/?adj_t=j39b9kp&adj_fallback=https%3A%2F%2Fokto.tech&adj_redirect_macos=https%3A%2F%2Fokto.tech\",\n    ios: \"https://apps.apple.com/in/app/okto-wallet/id6450688229\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.coindcx.okto&hl=en_IN&gl=US\",\n    mac: \"\",\n    windows: null,\n    linux: null,\n    chrome: \"https://okto.tech/\",\n    firefox: null,\n    safari: \"https://okto.tech/\",\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"okto://\",\n    universal:\n      \"https://okto.go.link/?adj_t=j39b9kp&adj_fallback=https%3A%2F%2Fokto.tech&adj_redirect_macos=https%3A%2F%2Fokto.tech\",\n  },\n  desktop: {\n    native: null,\n    universal: \"https://okto.tech/\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SACE;IACF,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WACE;;EAEJ,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}