{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/xyz.argent/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"xyz.argent\",\n  name: \"Argent\",\n  homepage: \"https://www.argent.xyz\",\n  image_id: \"215158d2-614b-49c9-410f-77aa661c3900\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/argent-defi-in-a-tap/id1358741926\",\n    android:\n      \"https://play.google.com/store/apps/details?id=im.argent.contractwalletclient&hl=en&gl=US&pli=1\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"argent://app/\",\n    universal: \"https://www.argent.xyz/app\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}