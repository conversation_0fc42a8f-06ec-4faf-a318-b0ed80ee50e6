import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/io.bladewallet/index.js
var wallet = {
  id: "io.bladewallet",
  name: "Blade Wallet",
  homepage: "https://bladewallet.io",
  image_id: "8fa87652-b043-4992-3a45-78e438d1cd00",
  app: {
    browser: null,
    ios: "https://apps.apple.com/app/apple-store/id1623849951",
    android: "https://play.google.com/store/apps/details?id=org.bladelabs.wallet",
    mac: null,
    windows: null,
    linux: null,
    chrome: "https://chrome.google.com/webstore/detail/blade-%E2%80%93-hedera-web3-digit/abogmiocnneedmmepnohnhlijcjpcifd",
    firefox: null,
    safari: null,
    edge: null,
    opera: null
  },
  rdns: null,
  mobile: {
    native: "org.bladelabs.bladewallet://",
    universal: null
  },
  desktop: {
    native: null,
    universal: "https://welcome.bladewallet.io/"
  }
};
export {
  wallet
};
//# sourceMappingURL=io-UDQDUZHN.js.map
