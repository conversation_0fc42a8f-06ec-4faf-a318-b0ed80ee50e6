{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/world.fncy/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"world.fncy\",\n  name: \"Fncy Mobile Wallet\",\n  homepage: \"https://fncy.world\",\n  image_id: \"c1c8d374-dff3-419c-96af-3515d0192100\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/fncy-blockchain-platform/id1613707166\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.metaverse.world.cube&hl=en_US&pli=1\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"metaCubeWallet://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}