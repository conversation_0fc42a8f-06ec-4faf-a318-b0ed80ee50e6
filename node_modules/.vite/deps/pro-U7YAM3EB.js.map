{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/pro.fintoken/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"pro.fintoken\",\n  name: \"FINTOKEN\",\n  homepage: \"https://fintoken.pro/\",\n  image_id: \"420ababa-3c29-4711-4487-84b93bfa5900\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/fintoken-web3-crypto-wallet/id6447503215\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.digitalasset.fintoken&pli=1\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"fintoken://\",\n    universal: \"https://ios.fintoken.pro/app\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}