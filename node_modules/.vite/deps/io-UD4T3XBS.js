import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/io.talken/index.js
var wallet = {
  id: "io.talken",
  name: "Talken Wallet",
  homepage: "https://talken.io/",
  image_id: "3c49e8e7-a4d8-4810-23ef-0a0102cce100",
  app: {
    browser: null,
    ios: "https://apps.apple.com/kr/app/talken-web3-wallet-nft-suite/id1459475831",
    android: "https://play.google.com/store/search?q=talken&c=apps&hl=en-KR",
    mac: "",
    windows: null,
    linux: null,
    chrome: null,
    firefox: null,
    safari: null,
    edge: null,
    opera: null
  },
  rdns: null,
  mobile: {
    native: "talken-wallet://",
    universal: null
  },
  desktop: {
    native: null,
    universal: null
  }
};
export {
  wallet
};
//# sourceMappingURL=io-UD4T3XBS.js.map
