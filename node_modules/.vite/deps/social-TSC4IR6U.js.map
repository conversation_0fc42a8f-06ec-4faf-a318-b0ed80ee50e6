{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/social.gm2/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"social.gm2\",\n  name: \"GM² Social\",\n  homepage: \"https://gm2.social/\",\n  image_id: \"f5c2218d-56b4-4fc8-63bf-0ece7276d600\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/gm-social/id6502584673\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.gm2.app.prod&hl=en\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"gm2://home\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}