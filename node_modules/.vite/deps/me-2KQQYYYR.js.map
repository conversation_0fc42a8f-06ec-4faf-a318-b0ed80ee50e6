{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/me.iopay/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"me.iopay\",\n  name: \"ioPay\",\n  homepage: \"https://iopay.me/\",\n  image_id: \"411d80d0-3a75-4932-560f-565d8c715e00\",\n  app: {\n    browser: \"https://iopay.me/\",\n    ios: \"https://apps.apple.com/app/apple-store/id1478086371\",\n    android: \"https://play.google.com/store/apps/details?id=io.iotex.iopay.gp\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"iopay://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: \"https://iopay.me/\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}