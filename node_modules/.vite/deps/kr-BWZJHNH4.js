import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/react/web/wallets/injected/locale/kr.js
var injectedWalletLocale = (wallet) => ({
  connectionScreen: {
    inProgress: "확인 대기 중",
    failed: "연결 실패",
    instruction: `${wallet}에서 연결 요청을 수락하세요`,
    retry: "다시 시도하세요"
  },
  getStartedScreen: {
    instruction: `Scan the QR code to download the ${wallet} app`
  },
  scanScreen: {
    instruction: `Scan the QR code with the ${wallet} app to connect`
  },
  getStartedLink: `Don't have ${wallet}?`,
  download: {
    chrome: "Chrome 확장 프로그램 다운로드",
    android: "Google Play에서 다운로드",
    iOS: "App Store에서 다운로드"
  }
});
var kr_default = injectedWalletLocale;
export {
  kr_default as default
};
//# sourceMappingURL=kr-BWZJHNH4.js.map
