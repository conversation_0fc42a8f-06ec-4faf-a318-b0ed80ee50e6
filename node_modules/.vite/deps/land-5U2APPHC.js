import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/land.liker/index.js
var wallet = {
  id: "land.liker",
  name: "Liker<PERSON><PERSON> App",
  homepage: "https://liker.land/getapp",
  image_id: "501fa316-f0df-4a1b-ead6-5523251b7100",
  app: {
    browser: null,
    ios: "https://apps.apple.com/app/liker-land/id1248232355",
    android: "https://play.google.com/store/apps/details?id=com.oice",
    mac: null,
    windows: null,
    linux: null,
    chrome: null,
    firefox: null,
    safari: null,
    edge: null,
    opera: null
  },
  rdns: null,
  mobile: {
    native: "com.oice://",
    universal: null
  },
  desktop: {
    native: null,
    universal: null
  }
};
export {
  wallet
};
//# sourceMappingURL=land-5U2APPHC.js.map
