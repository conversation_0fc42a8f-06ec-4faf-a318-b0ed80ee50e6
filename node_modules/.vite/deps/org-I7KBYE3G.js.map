{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/org.gooddollar/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"org.gooddollar\",\n  name: \"<PERSON><PERSON><PERSON><PERSON>\",\n  homepage: \"https://gooddollar.org\",\n  image_id: \"371ab65b-e2c8-4843-f18a-cbcf2ba2ed00\",\n  app: {\n    browser: null,\n    ios: null,\n    android: \"https://play.google.com/store/apps/details?id=org.gooddollar\",\n    mac: \"\",\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"gooddollar://\",\n    universal: \"https://wallet.gooddollar.org/\",\n  },\n  desktop: {\n    native: \"gooddollar://\",\n    universal: \"https://wallet.gooddollar.org/\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}