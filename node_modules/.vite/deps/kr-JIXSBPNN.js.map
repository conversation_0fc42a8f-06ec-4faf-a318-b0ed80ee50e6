{"version": 3, "sources": ["../../thirdweb/src/react/web/ui/ConnectWallet/locale/kr.ts"], "sourcesContent": ["import type { ConnectLocale } from \"./types.js\";\nconst connectLocaleKr: ConnectLocale = {\n  id: \"ko_KR\",\n  signIn: \"로그인\",\n  defaultButtonTitle: \"지갑 연결\",\n  connecting: \"연결 중\",\n  switchNetwork: \"네트워크 전환\",\n  switchingNetwork: \"네트워크 전환 중\",\n  defaultModalTitle: \"로그인\",\n  recommended: \"추천\",\n  installed: \"설치됨\",\n  buy: \"구매\",\n  continueAsGuest: \"게스트로 계속\",\n  connectAWallet: \"지갑 연결\",\n  newToWallets: \"지갑 사용이 처음이신가요?\",\n  getStarted: \"시작하기\",\n  guest: \"게스트\",\n  send: \"보내기\",\n  receive: \"받기\",\n  currentNetwork: \"현재 네트워크\",\n  switchAccount: \"계정 전환\",\n  requestTestnetFunds: \"테스트넷 자금 요청\",\n  transactions: \"거래\",\n  payTransactions: \"법정화폐 거래\",\n  walletTransactions: \"지갑 거래\",\n  viewAllTransactions: \"모든 거래 보기\",\n  backupWallet: \"지갑 백업\",\n  guestWalletWarning:\n    \"이것은 임시 게스트 지갑입니다. 접근을 잃지 않으려면 지갑을 백업하십시오\",\n  switchTo: \"전환\", // Used in \"<Wallet-Name> 으로 전환\"\n  connectedToSmartWallet: \"스마트 계정\",\n  confirmInWallet: \"지갑에서 확인\",\n  disconnectWallet: \"지갑 연결 해제\",\n  copyAddress: \"주소 복사\",\n  personalWallet: \"개인 지갑\",\n  smartWallet: \"스마트 지갑\",\n  or: \"또는\",\n  goBackButton: \"뒤로\",\n  passkeys: {\n    title: \"패스키\",\n    linkPasskey: \"패스키 연결\",\n  },\n  welcomeScreen: {\n    defaultTitle: \"분산된 세계로의 게이트웨이\",\n    defaultSubtitle: \"시작하려면 지갑을 연결하십시오\",\n  },\n  agreement: {\n    prefix: \"연결함으로써 귀하는 다음에 동의하게 됩니다\",\n    termsOfService: \"서비스 약관\",\n    and: \"&\",\n    privacyPolicy: \"개인정보 보호정책\",\n  },\n  networkSelector: {\n    title: \"네트워크 선택\",\n    mainnets: \"메인넷\",\n    testnets: \"테스트넷\",\n    allNetworks: \"모두\",\n    addCustomNetwork: \"맞춤 네트워크 추가\",\n    inputPlaceholder: \"네트워크 또는 체인 ID 검색\",\n    categoryLabel: {\n      recentlyUsed: \"최근 사용\",\n      popular: \"인기\",\n      others: \"모든 네트워크\",\n    },\n    loading: \"로딩 중\",\n    failedToSwitch: \"네트워크 전환 실패\",\n  },\n  receiveFundsScreen: {\n    title: \"자금 수신\",\n    instruction: \"자금을 이 지갑으로 보내려면 지갑 주소를 복사하십시오\",\n  },\n  sendFundsScreen: {\n    title: \"자금 송금\",\n    submitButton: \"보내기\",\n    token: \"토큰\",\n    sendTo: \"보낼 곳\",\n    amount: \"금액\",\n    successMessage: \"거래 성공\",\n    invalidAddress: \"유효하지 않은 주소\",\n    noTokensFound: \"토큰을 찾을 수 없음\",\n    searchToken: \"토큰 주소 검색 또는 붙여넣기\",\n    transactionFailed: \"거래 실패\",\n    transactionRejected: \"거래 거부됨\",\n    insufficientFunds: \"자금 부족\",\n    selectTokenTitle: \"토큰 선택\",\n    sending: \"보내는 중\",\n  },\n  signatureScreen: {\n    instructionScreen: {\n      title: \"로그인\",\n      instruction: \"계속하려면 지갑에서 메시지 요청에 서명하십시오\",\n      signInButton: \"로그인\",\n      disconnectWallet: \"지갑 연결 해제\",\n    },\n    signingScreen: {\n      title: \"로그인 중\",\n      prompt: \"지갑에서 서명 요청에 서명하십시오\",\n      promptForSafe:\n        \"지갑에서 서명 요청에 서명하고 Safe에서 거래를 승인하십시오\",\n      approveTransactionInSafe: \"Safe에서 거래 승인\",\n      tryAgain: \"다시 시도\",\n      failedToSignIn: \"로그인 실패\",\n      inProgress: \"확인 대기 중\",\n    },\n  },\n  manageWallet: {\n    title: \"지갑 관리\",\n    linkedProfiles: \"연결된 프로필\",\n    linkProfile: \"링크 프로필\",\n    connectAnApp: \"앱 연결\",\n    exportPrivateKey: \"개인 키 내보내기\",\n  },\n  viewFunds: {\n    title: \"자금 보기\",\n    viewNFTs: \"NFT 보기\",\n    viewTokens: \"토큰 보기\",\n    viewAssets: \"자산 보기\",\n  },\n};\nexport default connectLocaleKr;\n"], "mappings": ";;;AACA,IAAM,kBAAiC;EACrC,IAAI;EACJ,QAAQ;EACR,oBAAoB;EACpB,YAAY;EACZ,eAAe;EACf,kBAAkB;EAClB,mBAAmB;EACnB,aAAa;EACb,WAAW;EACX,KAAK;EACL,iBAAiB;EACjB,gBAAgB;EAChB,cAAc;EACd,YAAY;EACZ,OAAO;EACP,MAAM;EACN,SAAS;EACT,gBAAgB;EAChB,eAAe;EACf,qBAAqB;EACrB,cAAc;EACd,iBAAiB;EACjB,oBAAoB;EACpB,qBAAqB;EACrB,cAAc;EACd,oBACE;EACF,UAAU;;EACV,wBAAwB;EACxB,iBAAiB;EACjB,kBAAkB;EAClB,aAAa;EACb,gBAAgB;EAChB,aAAa;EACb,IAAI;EACJ,cAAc;EACd,UAAU;IACR,OAAO;IACP,aAAa;;EAEf,eAAe;IACb,cAAc;IACd,iBAAiB;;EAEnB,WAAW;IACT,QAAQ;IACR,gBAAgB;IAChB,KAAK;IACL,eAAe;;EAEjB,iBAAiB;IACf,OAAO;IACP,UAAU;IACV,UAAU;IACV,aAAa;IACb,kBAAkB;IAClB,kBAAkB;IAClB,eAAe;MACb,cAAc;MACd,SAAS;MACT,QAAQ;;IAEV,SAAS;IACT,gBAAgB;;EAElB,oBAAoB;IAClB,OAAO;IACP,aAAa;;EAEf,iBAAiB;IACf,OAAO;IACP,cAAc;IACd,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,gBAAgB;IAChB,gBAAgB;IAChB,eAAe;IACf,aAAa;IACb,mBAAmB;IACnB,qBAAqB;IACrB,mBAAmB;IACnB,kBAAkB;IAClB,SAAS;;EAEX,iBAAiB;IACf,mBAAmB;MACjB,OAAO;MACP,aAAa;MACb,cAAc;MACd,kBAAkB;;IAEpB,eAAe;MACb,OAAO;MACP,QAAQ;MACR,eACE;MACF,0BAA0B;MAC1B,UAAU;MACV,gBAAgB;MAChB,YAAY;;;EAGhB,cAAc;IACZ,OAAO;IACP,gBAAgB;IAChB,aAAa;IACb,cAAc;IACd,kBAAkB;;EAEpB,WAAW;IACT,OAAO;IACP,UAAU;IACV,YAAY;IACZ,YAAY;;;AAGhB,IAAA,aAAe;", "names": []}