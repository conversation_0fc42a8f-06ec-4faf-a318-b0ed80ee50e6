{"version": 3, "sources": ["../../thirdweb/src/transaction/prepare-transaction.ts"], "sourcesContent": ["import type { <PERSON><PERSON>, AbiFunction, Address } from \"abitype\";\nimport type { <PERSON>List, Hex } from \"viem\";\nimport type { Chain } from \"../chains/types.js\";\nimport type { ThirdwebClient } from \"../client/client.js\";\nimport type { ThirdwebContract } from \"../contract/contract.js\";\nimport type { PreparedMethod } from \"../utils/abi/prepare-method.js\";\nimport type { PromisedObject } from \"../utils/promise/resolve-promised-value.js\";\nimport type { SignedAuthorization } from \"./actions/eip7702/authorization.js\";\n\nexport type StaticPrepareTransactionOptions = {\n  accessList?: AccessList | undefined;\n  to?: Address | undefined;\n  data?: Hex | undefined;\n  value?: bigint | undefined;\n  gas?: bigint | undefined;\n  gasPrice?: bigint | undefined;\n  maxFeePerGas?: bigint | undefined;\n  maxPriorityFeePerGas?: bigint | undefined;\n  maxFeePerBlobGas?: bigint | undefined;\n  type?: undefined | TransactionType;\n  nonce?: number | undefined;\n  extraGas?: bigint | undefined;\n  // eip7702\n  authorizationList?: SignedAuthorization[] | undefined;\n  // zksync specific\n  eip712?: EIP712TransactionOptions | undefined;\n  // tw specific\n  chain: Chain;\n  client: ThirdwebClient;\n  // extras\n  extraCallData?: Hex;\n  erc20Value?: {\n    amountWei: bigint;\n    tokenAddress: Address;\n  };\n};\n\ntype TransactionType = \"legacy\" | \"eip1559\" | \"eip2930\" | \"eip4844\" | \"eip7702\";\n\nexport const TransactionTypeMap: Record<TransactionType, number> = {\n  legacy: 0,\n  eip1559: 1,\n  eip2930: 2,\n  eip4844: 3,\n  eip7702: 4,\n};\n\nexport type EIP712TransactionOptions = {\n  // constant or user input\n  gasPerPubdata?: bigint | undefined;\n  // optional signature, generated\n  customSignature?: Hex | undefined;\n  // optional, used to deploy contracts with the transaction\n  factoryDeps?: Hex[] | undefined;\n  // optional, paymaster contract address to invoke\n  paymaster?: Address | undefined;\n  // optional, paymaster contract input\n  paymasterInput?: Hex | undefined;\n};\n\nexport type EIP712SerializedTransaction = {\n  txType: bigint;\n  from: bigint;\n  to: bigint;\n  gasLimit: bigint;\n  gasPerPubdataByteLimit: bigint;\n  maxFeePerGas: bigint;\n  maxPriorityFeePerGas: bigint;\n  nonce: bigint;\n  value: bigint;\n  data: Hex;\n  factoryDeps: Hex[];\n  paymaster: bigint;\n  paymasterInput: Hex;\n};\n\nexport type PrepareTransactionOptions = {\n  chain: Chain;\n  client: ThirdwebClient;\n} & PromisedObject<Omit<StaticPrepareTransactionOptions, \"chain\" | \"client\">>;\n\ntype Additional<\n  abi extends Abi = [],\n  abiFn extends AbiFunction = AbiFunction,\n> = {\n  preparedMethod: () => Promise<PreparedMethod<abiFn>>;\n  contract: ThirdwebContract<abi>;\n};\n\nexport type PreparedTransaction<\n  abi extends Abi = [],\n  abiFn extends AbiFunction = AbiFunction,\n  options extends PrepareTransactionOptions = PrepareTransactionOptions,\n> = Readonly<options> & {\n  __preparedMethod?: () => Promise<PreparedMethod<abiFn>>;\n  __contract?: ThirdwebContract<abi>;\n};\n\n/**\n * Prepares a transaction with the given options.\n * @param options - The options for preparing the transaction.\n * @param info - Additional information about the ABI function.\n * @returns The prepared transaction.\n * @transaction\n * @example\n * ```ts\n * import { prepareTransaction, toWei } from \"thirdweb\";\n * import { ethereum } from \"thirdweb/chains\";\n * const transaction = prepareTransaction({\n *  to: \"******************************************\",\n *  chain: ethereum,\n *  client: thirdwebClient,\n *  value: toWei(\"1.0\"),\n *  gasPrice: 30n\n * });\n * ```\n */\nexport function prepareTransaction<\n  const abi extends Abi = [],\n  const abiFn extends AbiFunction = AbiFunction,\n>(options: PrepareTransactionOptions, info?: Additional<abi, abiFn>) {\n  if (info) {\n    // biome-ignore lint/suspicious/noExplicitAny: TODO: fix later\n    (options as any).__preparedMethod = info.preparedMethod;\n    // biome-ignore lint/suspicious/noExplicitAny: TODO: fix later\n    (options as any).__contract = info.contract;\n  }\n  return options as PreparedTransaction<abi, abiFn>;\n}\n"], "mappings": ";AAuCO,IAAM,qBAAsD;EACjE,QAAQ;EACR,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;;AAyEL,SAAU,mBAGd,SAAoC,MAA6B;AACjE,MAAI,MAAM;AAEP,YAAgB,mBAAmB,KAAK;AAExC,YAAgB,aAAa,KAAK;EACrC;AACA,SAAO;AACT;", "names": []}