import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/io.transi/index.js
var wallet = {
  id: "io.transi",
  name: "<PERSON><PERSON>",
  homepage: "https://www.transi.io/",
  image_id: "a567089d-69d5-47f6-fd99-db47a448ab00",
  app: {
    browser: "https://www.transi.io/TransiWallet",
    ios: "https://apps.apple.com/us/app/transi-chat/id1662471884",
    android: "https://www.transi.io/TransiWallet",
    mac: null,
    windows: null,
    linux: null,
    chrome: null,
    firefox: null,
    safari: null,
    edge: null,
    opera: null
  },
  rdns: null,
  mobile: {
    native: "transi://",
    universal: null
  },
  desktop: {
    native: null,
    universal: "https://www.transi.io/TransiWallet"
  }
};
export {
  wallet
};
//# sourceMappingURL=io-XLDFERU5.js.map
