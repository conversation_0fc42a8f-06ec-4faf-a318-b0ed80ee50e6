{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.getjoin.prd/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.getjoin.prd\",\n  name: \"<PERSON>O<PERSON> MOBILE APP\",\n  homepage: \"https://getjoin.io\",\n  image_id: \"bd200406-7b27-452f-bb23-14e22ac47500\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/mk/app/join-wallet/id6590635145?uo=2\",\n    android: \"https://play.google.com/store/apps/details?id=io.getjoin.prd\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: \"io.getjoin.prd\",\n  mobile: {\n    native: null,\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}