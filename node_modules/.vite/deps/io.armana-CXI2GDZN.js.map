{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.armana.portal/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.armana.portal\",\n  name: \"Armana Portal\",\n  homepage: \"https://portal.armana.io\",\n  image_id: \"fe3c264d-b595-437d-e5f9-5e5833dd4300\",\n  app: {\n    browser: \"https://arman.io/mint\",\n    ios: \"https://apps.apple.com/us/app/armana-portal/id6448726023\",\n    android: \"https://play.google.com/store/apps/details?id=io.armana.portal\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"armanaportal://\",\n    universal: \"https://portal.armana.io/wc?uri=\",\n  },\n  desktop: {\n    native: null,\n    universal: \"https://arman.io/mint\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}