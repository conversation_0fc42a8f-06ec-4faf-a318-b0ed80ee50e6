import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/react/web/wallets/injected/locale/tl.js
var injectedWalletLocaleTl = (walletName) => ({
  connectionScreen: {
    inProgress: "Naghihintay ng Kumpirmasyon",
    failed: "Nabigo ang Pagkakonekta",
    instruction: `Tanggapin ang connection request sa ${walletName} wallet`,
    retry: "Subukan Muli"
  },
  getStartedScreen: {
    instruction: `I-scan ang QR code para ma-download ang ${walletName} app`
  },
  scanScreen: {
    instruction: `I-scan ang QR code gamit ang ${walletName} app para makonekta`
  },
  getStartedLink: `Wala kang ${walletName} wallet?`,
  download: {
    chrome: "I-download ang Chrome Extension",
    android: "I-download sa Google Play",
    iOS: "I-download sa App Store"
  }
});
var tl_default = injectedWalletLocaleTl;
export {
  tl_default as default
};
//# sourceMappingURL=tl-GAQSFV3M.js.map
