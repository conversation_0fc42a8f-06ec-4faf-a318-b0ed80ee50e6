{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.magiceden.wallet/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.magiceden.wallet\",\n  name: \"<PERSON> Eden\",\n  homepage: \"https://wallet.magiceden.io/\",\n  image_id: \"62040f22-2ffd-4942-92fc-71ce68c64300\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/magic-eden-wallet/id6478631482\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.magiceden.wallet&hl=en_US?utm_source=website&utm_medium=internal&utm_campaign=mobile-wallet-ga&utm_content=button\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome:\n      \"https://chromewebstore.google.com/detail/magic-eden-wallet/mkpegjkblkkefacfnmkajcjmabijhclg\",\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: \"io.magiceden.wallet\",\n  mobile: {\n    native: \"magiceden://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QACE;IACF,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}