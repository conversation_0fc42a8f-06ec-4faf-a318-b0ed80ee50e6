{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.talken/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.talken\",\n  name: \"Talken Wallet\",\n  homepage: \"https://talken.io/\",\n  image_id: \"3c49e8e7-a4d8-4810-23ef-0a0102cce100\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/kr/app/talken-web3-wallet-nft-suite/id1459475831\",\n    android: \"https://play.google.com/store/search?q=talken&c=apps&hl=en-KR\",\n    mac: \"\",\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"talken-wallet://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}