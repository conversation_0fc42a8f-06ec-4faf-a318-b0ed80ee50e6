{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/org.thetatoken/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"org.thetatoken\",\n  name: \"Theta Wallet\",\n  homepage: \"https://www.thetatoken.org/wallet\",\n  image_id: \"d4afb810-5925-4f00-4ebb-d180fcf29000\",\n  app: {\n    browser: \"https://wallet.thetatoken.org\",\n    ios: \"https://apps.apple.com/app/theta-wallet/id1451094550\",\n    android:\n      \"https://play.google.com/store/apps/details?id=org.theta.wallet&pli=1\",\n    mac: \"\",\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"wc://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: \"https://wallet.thetatoken.org\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}