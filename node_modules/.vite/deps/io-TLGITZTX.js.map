{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.yusetoken/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.yusetoken\",\n  name: \"<PERSON><PERSON>\",\n  homepage: \"https://yusetoken.io/\",\n  image_id: \"2cd61458-59c2-4208-c8ee-98b5e0076b00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/yuse-wallet/id6449364813\",\n    android: \"https://play.google.com/store/apps/details?id=com.yuse.wallet\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"yuse://wallet://\",\n    universal: \"https://yusewallet.page.link/tobR\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}