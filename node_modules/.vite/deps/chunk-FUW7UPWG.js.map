{"version": 3, "sources": ["../../thirdweb/src/wallets/in-app/core/constants/settings.ts", "../../thirdweb/src/wallets/in-app/core/authentication/client-scoped-storage.ts"], "sourcesContent": ["/**\n * @internal\n */\nexport const IN_APP_WALLET_PATH = \"/sdk/2022-08-12/embedded-wallet\";\n\n// STORAGE\n\n/**\n * @internal\n */\nexport const WALLET_USER_DETAILS_LOCAL_STORAGE_NAME = (key: string) =>\n  `thirdwebEwsWalletUserDetails-${key}`;\n\n/**\n * @internal\n */\nexport const WALLET_USER_ID_LOCAL_STORAGE_NAME = (cliekeytId: string) =>\n  `thirdwebEwsWalletUserId-${cliekeytId}`;\n\n/**\n * @internal\n */\nconst AUTH_TOKEN_LOCAL_STORAGE_PREFIX = \"walletToken\";\n\n/**\n * @internal\n */\nexport const AUTH_TOKEN_LOCAL_STORAGE_NAME = (key: string) => {\n  return `${AUTH_TOKEN_LOCAL_STORAGE_PREFIX}-${key}`;\n};\n\n/**\n * @internal\n */\nexport const PASSKEY_CREDENTIAL_ID_LOCAL_STORAGE_NAME = (key: string) => {\n  return `passkey-credential-id-${key}`;\n};\n\n/**\n * @internal\n */\nconst DEVICE_SHARE_LOCAL_STORAGE_PREFIX = \"a\";\n\n/**\n * @internal\n */\nexport const DEVICE_SHARE_LOCAL_STORAGE_NAME = (key: string, userId: string) =>\n  `${DEVICE_SHARE_LOCAL_STORAGE_PREFIX}-${key}-${userId}`;\n\n/**\n * @internal\n */\nexport const WALLET_CONNECT_SESSIONS_LOCAL_STORAGE_NAME = (key: string) =>\n  `walletConnectSessions-${key}`;\n\n/**\n * @internal\n */\nexport const GUEST_SESSION_LOCAL_STORAGE_NAME = (key: string) =>\n  `thirdweb_guest_session_id_${key}`;\n", "import type { AsyncStorage } from \"../../../../utils/storage/AsyncStorage.js\";\nimport {\n  AUTH_TOKEN_LOCAL_STORAGE_NAME,\n  DEVICE_SHARE_LOCAL_STORAGE_NAME,\n  GUEST_SESSION_LOCAL_STORAGE_NAME,\n  PASSKEY_CREDENTIAL_ID_LOCAL_STORAGE_NAME,\n  WALLET_CONNECT_SESSIONS_LOCAL_STORAGE_NAME,\n  WALLET_USER_ID_LOCAL_STORAGE_NAME,\n} from \"../constants/settings.js\";\nimport type { Ecosystem } from \"../wallet/types.js\";\n\nconst data = new Map<string, string>();\n\n/**\n * @internal\n */\nexport class ClientScopedStorage {\n  protected key: string;\n  protected storage: AsyncStorage | null;\n  public ecosystem?: Ecosystem;\n  /**\n   * @internal\n   */\n  constructor({\n    storage,\n    clientId,\n    ecosystem,\n  }: {\n    storage: AsyncStorage | null;\n    clientId: string;\n    ecosystem?: Ecosystem;\n  }) {\n    this.storage = storage;\n    this.key = getLocalStorageKey(clientId, ecosystem?.id);\n    this.ecosystem = ecosystem;\n  }\n\n  protected async getItem(key: string): Promise<string | null> {\n    if (this.storage) {\n      return this.storage.getItem(key);\n    }\n    return data.get(key) ?? null;\n  }\n\n  protected async setItem(key: string, value: string): Promise<void> {\n    if (this.storage) {\n      return this.storage.setItem(key, value);\n    }\n    data.set(key, value);\n  }\n\n  protected async removeItem(key: string): Promise<boolean> {\n    const item = await this.getItem(key);\n    if (this.storage && item) {\n      this.storage.removeItem(key);\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * @internal\n   */\n  async getWalletConnectSessions(): Promise<string | null> {\n    return this.getItem(WALLET_CONNECT_SESSIONS_LOCAL_STORAGE_NAME(this.key));\n  }\n\n  /**\n   * @internal\n   */\n  async saveWalletConnectSessions(stringifiedSessions: string): Promise<void> {\n    await this.setItem(\n      WALLET_CONNECT_SESSIONS_LOCAL_STORAGE_NAME(this.key),\n      stringifiedSessions,\n    );\n  }\n\n  /**\n   * @internal\n   */\n  async savePasskeyCredentialId(id: string): Promise<void> {\n    await this.setItem(PASSKEY_CREDENTIAL_ID_LOCAL_STORAGE_NAME(this.key), id);\n  }\n\n  /**\n   * @internal\n   */\n  async getPasskeyCredentialId(): Promise<string | null> {\n    return this.getItem(PASSKEY_CREDENTIAL_ID_LOCAL_STORAGE_NAME(this.key));\n  }\n\n  /**\n   * @internal\n   */\n  async saveAuthCookie(cookie: string): Promise<void> {\n    await this.setItem(AUTH_TOKEN_LOCAL_STORAGE_NAME(this.key), cookie);\n  }\n  /**\n   * @internal\n   */\n  async getAuthCookie(): Promise<string | null> {\n    return this.getItem(AUTH_TOKEN_LOCAL_STORAGE_NAME(this.key));\n  }\n  /**\n   * @internal\n   */\n  async removeAuthCookie(): Promise<boolean> {\n    return this.removeItem(AUTH_TOKEN_LOCAL_STORAGE_NAME(this.key));\n  }\n\n  /**\n   * @internal\n   */\n  async saveDeviceShare(share: string, userId: string): Promise<void> {\n    await this.saveWalletUserId(userId);\n    await this.setItem(\n      DEVICE_SHARE_LOCAL_STORAGE_NAME(this.key, userId),\n      share,\n    );\n  }\n  /**\n   * @internal\n   */\n  async getDeviceShare(): Promise<string | null> {\n    const userId = await this.getWalletUserId();\n    if (userId) {\n      return this.getItem(DEVICE_SHARE_LOCAL_STORAGE_NAME(this.key, userId));\n    }\n    return null;\n  }\n  /**\n   * @internal\n   */\n  async removeDeviceShare(): Promise<boolean> {\n    const userId = await this.getWalletUserId();\n    if (userId) {\n      return this.removeItem(DEVICE_SHARE_LOCAL_STORAGE_NAME(this.key, userId));\n    }\n    return false;\n  }\n\n  /**\n   * @internal\n   */\n  async getWalletUserId(): Promise<string | null> {\n    return this.getItem(WALLET_USER_ID_LOCAL_STORAGE_NAME(this.key));\n  }\n  /**\n   * @internal\n   */\n  async saveWalletUserId(userId: string): Promise<void> {\n    await this.setItem(WALLET_USER_ID_LOCAL_STORAGE_NAME(this.key), userId);\n  }\n  /**\n   * @internal\n   */\n  async removeWalletUserId(): Promise<boolean> {\n    return this.removeItem(WALLET_USER_ID_LOCAL_STORAGE_NAME(this.key));\n  }\n\n  /**\n   * @internal\n   */\n  async getGuestSessionId(): Promise<string | null> {\n    return this.getItem(GUEST_SESSION_LOCAL_STORAGE_NAME(this.key));\n  }\n  /**\n   * @internal\n   */\n  async saveGuestSessionId(sessionId: string): Promise<void> {\n    await this.setItem(GUEST_SESSION_LOCAL_STORAGE_NAME(this.key), sessionId);\n  }\n}\n\nconst getLocalStorageKey = (clientId: string, ecosystemId?: string) => {\n  return `${clientId}${ecosystemId ? `-${ecosystemId}` : \"\"}`;\n};\n"], "mappings": ";AAGO,IAAM,qBAAqB;AAa3B,IAAM,oCAAoC,CAAC,eAChD,2BAA2B,UAAU;AAKvC,IAAM,kCAAkC;AAKjC,IAAM,gCAAgC,CAAC,QAAe;AAC3D,SAAO,GAAG,+BAA+B,IAAI,GAAG;AAClD;AAKO,IAAM,2CAA2C,CAAC,QAAe;AACtE,SAAO,yBAAyB,GAAG;AACrC;AAKA,IAAM,oCAAoC;AAKnC,IAAM,kCAAkC,CAAC,KAAa,WAC3D,GAAG,iCAAiC,IAAI,GAAG,IAAI,MAAM;AAKhD,IAAM,6CAA6C,CAAC,QACzD,yBAAyB,GAAG;AAKvB,IAAM,mCAAmC,CAAC,QAC/C,6BAA6B,GAAG;;;AChDlC,IAAM,OAAO,oBAAI,IAAG;AAKd,IAAO,sBAAP,MAA0B;;;;EAO9B,YAAY,EACV,SACA,UACA,UAAS,GAKV;AAdS,WAAA,eAAA,MAAA,OAAA;;;;;;AACA,WAAA,eAAA,MAAA,WAAA;;;;;;AACH,WAAA,eAAA,MAAA,aAAA;;;;;;AAaL,SAAK,UAAU;AACf,SAAK,MAAM,mBAAmB,UAAU,uCAAW,EAAE;AACrD,SAAK,YAAY;EACnB;EAEU,MAAM,QAAQ,KAAW;AACjC,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,QAAQ,QAAQ,GAAG;IACjC;AACA,WAAO,KAAK,IAAI,GAAG,KAAK;EAC1B;EAEU,MAAM,QAAQ,KAAa,OAAa;AAChD,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,QAAQ,QAAQ,KAAK,KAAK;IACxC;AACA,SAAK,IAAI,KAAK,KAAK;EACrB;EAEU,MAAM,WAAW,KAAW;AACpC,UAAM,OAAO,MAAM,KAAK,QAAQ,GAAG;AACnC,QAAI,KAAK,WAAW,MAAM;AACxB,WAAK,QAAQ,WAAW,GAAG;AAC3B,aAAO;IACT;AACA,WAAO;EACT;;;;EAKA,MAAM,2BAAwB;AAC5B,WAAO,KAAK,QAAQ,2CAA2C,KAAK,GAAG,CAAC;EAC1E;;;;EAKA,MAAM,0BAA0B,qBAA2B;AACzD,UAAM,KAAK,QACT,2CAA2C,KAAK,GAAG,GACnD,mBAAmB;EAEvB;;;;EAKA,MAAM,wBAAwB,IAAU;AACtC,UAAM,KAAK,QAAQ,yCAAyC,KAAK,GAAG,GAAG,EAAE;EAC3E;;;;EAKA,MAAM,yBAAsB;AAC1B,WAAO,KAAK,QAAQ,yCAAyC,KAAK,GAAG,CAAC;EACxE;;;;EAKA,MAAM,eAAe,QAAc;AACjC,UAAM,KAAK,QAAQ,8BAA8B,KAAK,GAAG,GAAG,MAAM;EACpE;;;;EAIA,MAAM,gBAAa;AACjB,WAAO,KAAK,QAAQ,8BAA8B,KAAK,GAAG,CAAC;EAC7D;;;;EAIA,MAAM,mBAAgB;AACpB,WAAO,KAAK,WAAW,8BAA8B,KAAK,GAAG,CAAC;EAChE;;;;EAKA,MAAM,gBAAgB,OAAe,QAAc;AACjD,UAAM,KAAK,iBAAiB,MAAM;AAClC,UAAM,KAAK,QACT,gCAAgC,KAAK,KAAK,MAAM,GAChD,KAAK;EAET;;;;EAIA,MAAM,iBAAc;AAClB,UAAM,SAAS,MAAM,KAAK,gBAAe;AACzC,QAAI,QAAQ;AACV,aAAO,KAAK,QAAQ,gCAAgC,KAAK,KAAK,MAAM,CAAC;IACvE;AACA,WAAO;EACT;;;;EAIA,MAAM,oBAAiB;AACrB,UAAM,SAAS,MAAM,KAAK,gBAAe;AACzC,QAAI,QAAQ;AACV,aAAO,KAAK,WAAW,gCAAgC,KAAK,KAAK,MAAM,CAAC;IAC1E;AACA,WAAO;EACT;;;;EAKA,MAAM,kBAAe;AACnB,WAAO,KAAK,QAAQ,kCAAkC,KAAK,GAAG,CAAC;EACjE;;;;EAIA,MAAM,iBAAiB,QAAc;AACnC,UAAM,KAAK,QAAQ,kCAAkC,KAAK,GAAG,GAAG,MAAM;EACxE;;;;EAIA,MAAM,qBAAkB;AACtB,WAAO,KAAK,WAAW,kCAAkC,KAAK,GAAG,CAAC;EACpE;;;;EAKA,MAAM,oBAAiB;AACrB,WAAO,KAAK,QAAQ,iCAAiC,KAAK,GAAG,CAAC;EAChE;;;;EAIA,MAAM,mBAAmB,WAAiB;AACxC,UAAM,KAAK,QAAQ,iCAAiC,KAAK,GAAG,GAAG,SAAS;EAC1E;;AAGF,IAAM,qBAAqB,CAAC,UAAkB,gBAAwB;AACpE,SAAO,GAAG,QAAQ,GAAG,cAAc,IAAI,WAAW,KAAK,EAAE;AAC3D;", "names": []}