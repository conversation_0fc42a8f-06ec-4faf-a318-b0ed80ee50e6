{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.slavi/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.slavi\",\n  name: \"Slavi Wallet\",\n  homepage: \"https://slavi.io/\",\n  image_id: \"282ce060-0beb-4236-b7b0-1b34cc6c8f00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/en/app/slavi-wallet/id1610125496?l=en\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.defiwalletmobile\",\n    mac: \"https://apps.apple.com/en/app/slavi-wallet/id1610125496?l=en\",\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"slaviwallet://\",\n    universal: \"https://www.slaviwallet.io\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}