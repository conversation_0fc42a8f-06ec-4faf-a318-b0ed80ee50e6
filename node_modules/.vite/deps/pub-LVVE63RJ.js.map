{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/pub.dg/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"pub.dg\",\n  name: \"DGPub App\",\n  homepage: \"https://dg.pub\",\n  image_id: \"9d9a2700-4ab0-4c1c-4acf-8ed0037cc500\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/jo/app/dg-pub-a-gateway-to-web3/id6478573535\",\n    android:\n      \"https://play.google.com/store/apps/details?id=pub.dg.wallet&pli=1\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"dgpub://\",\n    universal: \"https://dgpub.app.link\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}