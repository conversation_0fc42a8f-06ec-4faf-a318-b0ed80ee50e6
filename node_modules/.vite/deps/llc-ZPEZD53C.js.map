{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/llc.besc/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"llc.besc\",\n  name: \"<PERSON>Bag\",\n  homepage: \"https://besc.llc\",\n  image_id: \"5fad49d2-a138-47bb-ac87-6368d8bd9000\",\n  app: {\n    browser: null,\n    ios: null,\n    android: \"https://play.google.com/store/apps/details?id=com.beanbag.wallet\",\n    mac: \"\",\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"beanbag://wallet/connect\",\n    universal:\n      \"https://play.google.com/store/apps/details?id=com.beanbag.wallet\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WACE;;EAEJ,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}