import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/io.armana.portal/index.js
var wallet = {
  id: "io.armana.portal",
  name: "Armana Portal",
  homepage: "https://portal.armana.io",
  image_id: "fe3c264d-b595-437d-e5f9-5e5833dd4300",
  app: {
    browser: "https://arman.io/mint",
    ios: "https://apps.apple.com/us/app/armana-portal/id6448726023",
    android: "https://play.google.com/store/apps/details?id=io.armana.portal",
    mac: null,
    windows: null,
    linux: null,
    chrome: null,
    firefox: null,
    safari: null,
    edge: null,
    opera: null
  },
  rdns: null,
  mobile: {
    native: "armanaportal://",
    universal: "https://portal.armana.io/wc?uri="
  },
  desktop: {
    native: null,
    universal: "https://arman.io/mint"
  }
};
export {
  wallet
};
//# sourceMappingURL=io.armana-CXI2GDZN.js.map
