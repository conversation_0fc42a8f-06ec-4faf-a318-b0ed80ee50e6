import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/io.compasswallet/index.js
var wallet = {
  id: "io.compasswallet",
  name: "Compass Wallet",
  homepage: "https://compasswallet.io/",
  image_id: "1d7dea00-96be-4ce8-ca15-d14bddbb5000",
  app: {
    browser: null,
    ios: "https://apps.apple.com/us/app/compass-wallet-for-sei/id6450257441",
    android: "https://play.google.com/store/apps/details?id=io.leapwallet.compass",
    mac: null,
    windows: null,
    linux: null,
    chrome: "https://chromewebstore.google.com/detail/compass-wallet-for-sei/anokgmphncpekkhclmingpimjmcooifb",
    firefox: null,
    safari: null,
    edge: "https://chromewebstore.google.com/detail/compass-wallet-for-sei/anokgmphncpekkhclmingpimjmcooifb",
    opera: null
  },
  rdns: null,
  mobile: {
    native: "leapcompass://",
    universal: null
  },
  desktop: {
    native: null,
    universal: null
  }
};
export {
  wallet
};
//# sourceMappingURL=io-SLDR7S5K.js.map
