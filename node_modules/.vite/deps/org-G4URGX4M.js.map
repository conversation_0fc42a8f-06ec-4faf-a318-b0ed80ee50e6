{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/org.alephium/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"org.alephium\",\n  name: \"Alephium Wallet\",\n  homepage: \"https://alephium.org/\",\n  image_id: \"3ece76f1-fc71-4fad-2d28-707f5a8d2300\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/alephium-wallet/id6469043072\",\n    android:\n      \"https://play.google.com/store/apps/details?id=org.alephium.wallet\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"alephium://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}