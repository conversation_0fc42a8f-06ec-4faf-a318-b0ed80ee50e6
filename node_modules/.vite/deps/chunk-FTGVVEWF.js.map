{"version": 3, "sources": ["../../thirdweb/src/utils/arweave.ts"], "sourcesContent": ["const DEFAULT_GATEWAY = \"https://arweave.net/{fileId}\";\n\nexport type ResolveArweaveSchemeOptions = {\n  uri: string;\n  gatewayUrl?: string;\n};\n\n/**\n * Resolves the scheme of a given Arweave URI and returns the corresponding URL.\n * @param options - The options object containing the Arweave URI\n * @returns The resolved URL\n * @throws Error if the URI scheme is invalid.\n * @example\n * ```ts\n * import { resolveArweaveScheme } from \"thirdweb/storage\";\n * const url = resolveArweaveScheme({ uri: \"ar://<fileId>\" });\n * ```\n * @storage\n */\nexport function resolveArweaveScheme(options: ResolveArweaveSchemeOptions) {\n  if (options.uri.startsWith(\"ar://\")) {\n    const fileId = options.uri.replace(\"ar://\", \"\");\n    if (options.gatewayUrl) {\n      const separator = options.gatewayUrl.endsWith(\"/\") ? \"\" : \"/\";\n      return `${options.gatewayUrl}${separator}${fileId}`;\n    }\n    return DEFAULT_GATEWAY.replace(\"{fileId}\", fileId);\n  }\n  if (options.uri.startsWith(\"http\")) {\n    return options.uri;\n  }\n  throw new Error(`Invalid URI scheme, expected \"ar://\" or \"http(s)://\"`);\n}\n"], "mappings": ";AAAA,IAAM,kBAAkB;AAmBlB,SAAU,qBAAqB,SAAoC;AACvE,MAAI,QAAQ,IAAI,WAAW,OAAO,GAAG;AACnC,UAAM,SAAS,QAAQ,IAAI,QAAQ,SAAS,EAAE;AAC9C,QAAI,QAAQ,YAAY;AACtB,YAAM,YAAY,QAAQ,WAAW,SAAS,GAAG,IAAI,KAAK;AAC1D,aAAO,GAAG,QAAQ,UAAU,GAAG,SAAS,GAAG,MAAM;IACnD;AACA,WAAO,gBAAgB,QAAQ,YAAY,MAAM;EACnD;AACA,MAAI,QAAQ,IAAI,WAAW,MAAM,GAAG;AAClC,WAAO,QAAQ;EACjB;AACA,QAAM,IAAI,MAAM,sDAAsD;AACxE;", "names": []}