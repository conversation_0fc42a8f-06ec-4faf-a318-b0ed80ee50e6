{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/network.mrhb/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"network.mrhb\",\n  name: \"Sahal Wallet\",\n  homepage: \"https://mrhb.network\",\n  image_id: \"afa1e46a-331a-418f-ef1f-a29f76def100\",\n  app: {\n    browser: \"https://mrhb.network\",\n    ios: \"https://apps.apple.com/gb/app/sahal-wallet/id1602366920\",\n    android:\n      \"https://play.google.com/store/apps/details?id=sahal.wallet.app&gl=GB\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"sahalwallet://\",\n    universal: null,\n  },\n  desktop: {\n    native: \"sahalwallet://\",\n    universal: \"https://sahalwallet.app\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}