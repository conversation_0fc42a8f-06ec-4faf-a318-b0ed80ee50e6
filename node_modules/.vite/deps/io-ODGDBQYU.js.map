{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.harti/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.harti\",\n  name: \"HARTi Wallet\",\n  homepage: \"https://harti.io/\",\n  image_id: \"d0407f26-fe0b-4f3c-43c3-69bc8fef2e00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/jp/app/harti/id1599921940?l=en\",\n    android:\n      \"https://play.google.com/store/apps/details?id=app.harti&hl=ja&gl=US\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"HARTi://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}