{"version": 3, "sources": ["../../thirdweb/src/insight/index.ts", "../../thirdweb/src/insight/get-tokens.ts", "../../thirdweb/src/insight/get-events.ts"], "sourcesContent": ["export {\n  getContractNFTs,\n  getOwnedNFTs,\n  getNFT,\n} from \"./get-nfts.js\";\nexport { getOwnedTokens } from \"./get-tokens.js\";\nexport { getTransactions, type Transaction } from \"./get-transactions.js\";\nexport { getContractEvents, type ContractEvent } from \"./get-events.js\";\n", "import type {\n  GetV1TokensErc20ByOwnerAddressData,\n  GetV1TokensErc20ByOwnerAddressResponse,\n} from \"@thirdweb-dev/insight\";\nimport type { Chain } from \"../chains/types.js\";\nimport type { ThirdwebClient } from \"../client/client.js\";\nimport type { GetWalletBalanceResult } from \"../wallets/utils/getWalletBalance.js\";\n\ntype OwnedToken = GetV1TokensErc20ByOwnerAddressResponse[\"data\"][number];\n\n/**\n * Get ERC20 tokens owned by an address\n * @example\n * ```ts\n * import { Insight } from \"thirdweb\";\n *\n * const tokens = await Insight.getOwnedTokens({\n *   client,\n *   chains: [sepolia],\n *   ownerAddress: \"******************************************\",\n * });\n * ```\n * @insight\n */\nexport async function getOwnedTokens(args: {\n  client: ThirdwebClient;\n  chains: Chain[];\n  ownerAddress: string;\n  queryOptions?: GetV1TokensErc20ByOwnerAddressData[\"query\"];\n}): Promise<GetWalletBalanceResult[]> {\n  const [\n    { getV1TokensErc20ByOwnerAddress },\n    { getThirdwebDomains },\n    { getClientFetch },\n    { assertInsightEnabled },\n    { stringify },\n  ] = await Promise.all([\n    import(\"@thirdweb-dev/insight\"),\n    import(\"../utils/domains.js\"),\n    import(\"../utils/fetch.js\"),\n    import(\"./common.js\"),\n    import(\"../utils/json.js\"),\n  ]);\n\n  const { client, chains, ownerAddress, queryOptions } = args;\n\n  await assertInsightEnabled(chains);\n\n  const defaultQueryOptions: GetV1TokensErc20ByOwnerAddressData[\"query\"] = {\n    chain: chains.map((chain) => chain.id),\n    include_spam: \"false\",\n    metadata: \"true\",\n    limit: 50,\n  };\n\n  const result = await getV1TokensErc20ByOwnerAddress({\n    baseUrl: `https://${getThirdwebDomains().insight}`,\n    fetch: getClientFetch(client),\n    path: {\n      ownerAddress: ownerAddress,\n    },\n    query: {\n      ...defaultQueryOptions,\n      ...queryOptions,\n    },\n  });\n\n  if (result.error) {\n    throw new Error(\n      `${result.response.status} ${result.response.statusText} - ${result.error ? stringify(result.error) : \"Unknown error\"}`,\n    );\n  }\n\n  return transformOwnedToken(result.data?.data ?? []);\n}\n\nasync function transformOwnedToken(\n  token: OwnedToken[],\n): Promise<GetWalletBalanceResult[]> {\n  const { toTokens } = await import(\"../utils/units.js\");\n  return token.map((t) => {\n    const decimals = t.decimals ?? 18;\n    const value = BigInt(t.balance);\n    return {\n      value,\n      displayValue: toTokens(value, decimals),\n      tokenAddress: t.token_address,\n      chainId: t.chain_id,\n      decimals,\n      symbol: t.symbol ?? \"\",\n      name: t.name ?? \"\",\n    };\n  });\n}\n", "import type {\n  GetV1EventsByContractAddressData,\n  GetV1EventsByContractAddressResponse,\n} from \"@thirdweb-dev/insight\";\nimport type { AbiEvent } from \"ox/AbiEvent\";\nimport type { Chain } from \"../chains/types.js\";\nimport type { ThirdwebClient } from \"../client/client.js\";\nimport type { PreparedEvent } from \"../event/prepare-event.js\";\n\nexport type ContractEvent = NonNullable<\n  GetV1EventsByContractAddressResponse[\"data\"]\n>[number];\n\n/**\n * Get contract events\n * @example\n * ```ts\n * import { Insight } from \"thirdweb\";\n *\n * const events = await Insight.getContractEvents({\n *   client,\n *   chains: [sepolia],\n *   contractAddress: \"******************************************\",\n *   event: transferEvent(),\n *   decodeLogs: true,\n * });\n * ```\n * @insight\n */\nexport async function getContractEvents(options: {\n  client: ThirdwebClient;\n  chains: Chain[];\n  contractAddress: string;\n  event?: PreparedEvent<AbiEvent>;\n  decodeLogs?: boolean;\n  queryOptions?: Omit<\n    GetV1EventsByContractAddressData[\"query\"],\n    \"chain\" | \"decode\"\n  >;\n}): Promise<ContractEvent[]> {\n  const [\n    { getV1EventsByContractAddress },\n    { getThirdwebDomains },\n    { getClientFetch },\n    { assertInsightEnabled },\n    { stringify },\n  ] = await Promise.all([\n    import(\"@thirdweb-dev/insight\"),\n    import(\"../utils/domains.js\"),\n    import(\"../utils/fetch.js\"),\n    import(\"./common.js\"),\n    import(\"../utils/json.js\"),\n  ]);\n\n  const { client, chains, contractAddress, event, queryOptions, decodeLogs } =\n    options;\n\n  await assertInsightEnabled(chains);\n\n  const defaultQueryOptions: GetV1EventsByContractAddressData[\"query\"] = {\n    chain: chains.map((chain) => chain.id),\n    limit: 100,\n    decode: decodeLogs,\n  };\n\n  if (event) {\n    defaultQueryOptions.filter_topic_0 = event.topics[0];\n    defaultQueryOptions.filter_topic_1 = event.topics[1];\n    defaultQueryOptions.filter_topic_2 = event.topics[2];\n    defaultQueryOptions.filter_topic_3 = event.topics[3];\n  }\n\n  const result = await getV1EventsByContractAddress({\n    baseUrl: `https://${getThirdwebDomains().insight}`,\n    fetch: getClientFetch(client),\n    path: {\n      contractAddress,\n    },\n    query: {\n      ...defaultQueryOptions,\n      ...queryOptions,\n    },\n  });\n\n  if (result.error) {\n    throw new Error(\n      `${result.response.status} ${result.response.statusText} - ${result.error ? stringify(result.error) : \"Unknown error\"}`,\n    );\n  }\n\n  return result.data?.data ?? [];\n}\n"], "mappings": ";;;;;;;;;;;;;AAAA;;;;;;;;;;;ACwBA,eAAsB,eAAe,MAKpC;AAnBD;AAoBE,QAAM,CACJ,EAAE,+BAA8B,GAChC,EAAE,mBAAkB,GACpB,EAAE,eAAc,GAChB,EAAE,qBAAoB,GACtB,EAAE,UAAS,CAAE,IACX,MAAM,QAAQ,IAAI;IACpB,OAAO,wBAAuB;IAC9B,OAAO,uBAAqB;IAC5B,OAAO,qBAAmB;IAC1B,OAAO,sBAAa;IACpB,OAAO,oBAAkB;GAC1B;AAED,QAAM,EAAE,QAAQ,QAAQ,cAAc,aAAY,IAAK;AAEvD,QAAM,qBAAqB,MAAM;AAEjC,QAAM,sBAAmE;IACvE,OAAO,OAAO,IAAI,CAAC,UAAU,MAAM,EAAE;IACrC,cAAc;IACd,UAAU;IACV,OAAO;;AAGT,QAAM,SAAS,MAAM,+BAA+B;IAClD,SAAS,WAAW,mBAAkB,EAAG,OAAO;IAChD,OAAO,eAAe,MAAM;IAC5B,MAAM;MACJ;;IAEF,OAAO;MACL,GAAG;MACH,GAAG;;GAEN;AAED,MAAI,OAAO,OAAO;AAChB,UAAM,IAAI,MACR,GAAG,OAAO,SAAS,MAAM,IAAI,OAAO,SAAS,UAAU,MAAM,OAAO,QAAQ,UAAU,OAAO,KAAK,IAAI,eAAe,EAAE;EAE3H;AAEA,SAAO,sBAAoB,YAAO,SAAP,mBAAa,SAAQ,CAAA,CAAE;AACpD;AAEA,eAAe,oBACb,OAAmB;AAEnB,QAAM,EAAE,SAAQ,IAAK,MAAM,OAAO,qBAAmB;AACrD,SAAO,MAAM,IAAI,CAAC,MAAK;AACrB,UAAM,WAAW,EAAE,YAAY;AAC/B,UAAM,QAAQ,OAAO,EAAE,OAAO;AAC9B,WAAO;MACL;MACA,cAAc,SAAS,OAAO,QAAQ;MACtC,cAAc,EAAE;MAChB,SAAS,EAAE;MACX;MACA,QAAQ,EAAE,UAAU;MACpB,MAAM,EAAE,QAAQ;;EAEpB,CAAC;AACH;;;AChEA,eAAsB,kBAAkB,SAUvC;AA1BD;AA2BE,QAAM,CACJ,EAAE,6BAA4B,GAC9B,EAAE,mBAAkB,GACpB,EAAE,eAAc,GAChB,EAAE,qBAAoB,GACtB,EAAE,UAAS,CAAE,IACX,MAAM,QAAQ,IAAI;IACpB,OAAO,wBAAuB;IAC9B,OAAO,uBAAqB;IAC5B,OAAO,qBAAmB;IAC1B,OAAO,sBAAa;IACpB,OAAO,oBAAkB;GAC1B;AAED,QAAM,EAAE,QAAQ,QAAQ,iBAAiB,OAAO,cAAc,WAAU,IACtE;AAEF,QAAM,qBAAqB,MAAM;AAEjC,QAAM,sBAAiE;IACrE,OAAO,OAAO,IAAI,CAAC,UAAU,MAAM,EAAE;IACrC,OAAO;IACP,QAAQ;;AAGV,MAAI,OAAO;AACT,wBAAoB,iBAAiB,MAAM,OAAO,CAAC;AACnD,wBAAoB,iBAAiB,MAAM,OAAO,CAAC;AACnD,wBAAoB,iBAAiB,MAAM,OAAO,CAAC;AACnD,wBAAoB,iBAAiB,MAAM,OAAO,CAAC;EACrD;AAEA,QAAM,SAAS,MAAM,6BAA6B;IAChD,SAAS,WAAW,mBAAkB,EAAG,OAAO;IAChD,OAAO,eAAe,MAAM;IAC5B,MAAM;MACJ;;IAEF,OAAO;MACL,GAAG;MACH,GAAG;;GAEN;AAED,MAAI,OAAO,OAAO;AAChB,UAAM,IAAI,MACR,GAAG,OAAO,SAAS,MAAM,IAAI,OAAO,SAAS,UAAU,MAAM,OAAO,QAAQ,UAAU,OAAO,KAAK,IAAI,eAAe,EAAE;EAE3H;AAEA,WAAO,YAAO,SAAP,mBAAa,SAAQ,CAAA;AAC9B;", "names": []}