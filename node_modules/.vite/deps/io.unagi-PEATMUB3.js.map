{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.unagi.unawallet/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.unagi.unawallet\",\n  name: \"una Wallet\",\n  homepage: \"https://unawallet.unagi.io\",\n  image_id: \"78118aa8-8dfc-4742-86f7-b51d34e7cf00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/una-wallet/id6456397239\",\n    android: \"https://play.google.com/store/apps/details?id=app.unawallet.app\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"unaWallet://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}