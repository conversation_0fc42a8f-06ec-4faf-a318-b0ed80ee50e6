import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/react/web/ui/ConnectWallet/locale/ru.js
var connectLocaleRu = {
  id: "ru_RU",
  signIn: "Войти",
  defaultButtonTitle: "Подключиться",
  connecting: "Подключение",
  switchNetwork: "Сменить сеть",
  switchingNetwork: "Смена сети",
  defaultModalTitle: "Войти",
  recommended: "Рекомендуется",
  installed: "Установлено",
  buy: "Купить",
  continueAsGuest: "Продолжить как гость",
  connectAWallet: "Подключить кошелек",
  newToWallets: "Новичок в кошельках?",
  getStarted: "Начать",
  guest: "Гость",
  send: "Отправить",
  receive: "Получить",
  currentNetwork: "Текущая сеть",
  switchAccount: "Сменить аккаунт",
  requestTestnetFunds: "Запросить средства тестовой сети",
  transactions: "Транзакции",
  payTransactions: "Фиатные транзакции",
  walletTransactions: "Транзакции кошелька",
  viewAllTransactions: "Просмотреть все транзакции",
  backupWallet: "Создать резервную копию кошелька",
  guestWalletWarning: "Это временный гостевой кошелек. Создайте резервную копию, если не хотите потерять к нему доступ",
  switchTo: "Переключиться на",
  // Используется в "Switch to <Wallet-Name>>"
  connectedToSmartWallet: "Смарт-аккаунт",
  confirmInWallet: "Подтвердить в кошельке",
  disconnectWallet: "Отсоединить кошелек",
  copyAddress: "Скопировать адрес",
  personalWallet: "Личный кошелек",
  smartWallet: "Смарт-кошелек",
  or: "ИЛИ",
  goBackButton: "Назад",
  passkeys: {
    title: "Ключи доступа",
    linkPasskey: "Привязать ключ доступа"
  },
  welcomeScreen: {
    defaultTitle: "Ваш портал в мир децентрализации",
    defaultSubtitle: "Подключите кошелек, чтобы начать"
  },
  agreement: {
    prefix: "Подключаясь, вы соглашаетесь с",
    termsOfService: "Условиями использования",
    and: "и",
    privacyPolicy: "Политикой конфиденциальности"
  },
  networkSelector: {
    title: "Выбрать сеть",
    mainnets: "Основные сети (мейннетс)",
    testnets: "Тестовые сети (тестнетс)",
    allNetworks: "Все",
    addCustomNetwork: "Добавить кастомную сеть",
    inputPlaceholder: "Поиск сети или Chain ID",
    categoryLabel: {
      recentlyUsed: "Недавно использованные",
      popular: "Популярные",
      others: "Все сети"
    },
    loading: "Загрузка",
    failedToSwitch: "Не удалось сменить сеть"
  },
  receiveFundsScreen: {
    title: "Получить средства",
    instruction: "Скопируйте адрес кошелька, чтобы отправить средства на этот кошелек"
  },
  sendFundsScreen: {
    title: "Отправить средства",
    submitButton: "Отправить",
    token: "Токен",
    sendTo: "Отправить на",
    amount: "Сумма",
    successMessage: "Транзакция успешно выполнена",
    invalidAddress: "Недействительный адрес",
    noTokensFound: "Токены не найдены",
    searchToken: "Найти или вставить адрес токена",
    transactionFailed: "Транзакция не удалась",
    transactionRejected: "Транзакция отклонена",
    insufficientFunds: "Недостаточно средств",
    selectTokenTitle: "Выбрать токен",
    sending: "Отправка"
  },
  signatureScreen: {
    instructionScreen: {
      title: "Войти",
      instruction: "Пожалуйста, подпишите сообщение в кошельке, чтобы продолжить",
      signInButton: "Войти",
      disconnectWallet: "Отсоединить кошелек"
    },
    signingScreen: {
      title: "Вход",
      prompt: "Подпись запроса в вашем кошельке",
      promptForSafe: "Подпишите запрос в вашем кошельке и подтвердите транзакцию в Safe",
      approveTransactionInSafe: "Подтвердить транзакцию в Safe",
      tryAgain: "Попробовать снова",
      failedToSignIn: "Не удалось войти",
      inProgress: "Ожидание подтверждения"
    }
  },
  manageWallet: {
    title: "Управление кошельком",
    linkedProfiles: "Привязанные профили",
    linkProfile: "Привязать профиль",
    connectAnApp: "Подключить приложение",
    exportPrivateKey: "Экспортировать приватный ключ"
  },
  viewFunds: {
    title: "Просмотр средств",
    viewNFTs: "Просмотр NFTs",
    viewTokens: "Просмотр токенов",
    viewAssets: "Просмотр активов"
  }
};
var ru_default = connectLocaleRu;
export {
  ru_default as default
};
//# sourceMappingURL=ru-VGPCQUKU.js.map
