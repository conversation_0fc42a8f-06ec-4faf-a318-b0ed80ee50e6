{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/org.ecoinwallet/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"org.ecoinwallet\",\n  name: \"ECOIN Wallet\",\n  homepage: \"https://ecoinwallet.org\",\n  image_id: \"9639c263-d590-4862-ba9f-d5c7c1878d00\",\n  app: {\n    browser: null,\n    ios: null,\n    android:\n      \"https://play.google.com/store/apps/details?id=org.ecoinwallet&referrer=utm_source%3Dwalletconnect%26utm_medium%3Dreown%26utm_content%3Dlink\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"ecoinwallet://\",\n    universal: \"https://ecoinwallet.org/link\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}