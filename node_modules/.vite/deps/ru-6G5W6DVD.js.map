{"version": 3, "sources": ["../../thirdweb/src/react/web/wallets/shared/locale/ru.ts"], "sourcesContent": ["import type { InAppWalletLocale } from \"./types.js\";\n\nexport default {\n  signInWithGoogle: \"Google\",\n  signInWithFacebook: \"Facebook\",\n  signInWithApple: \"Apple\",\n  signInWithDiscord: \"Discord\",\n  emailPlaceholder: \"Адрес электронной почты\",\n  submitEmail: \"Продолжить\",\n  signIn: \"Войти\",\n  or: \"или\",\n  emailRequired: \"Требуется адрес электронной почты\",\n  invalidEmail: \"Неверный адрес электронной почты\",\n  maxAccountsExceeded:\n    \"Превышено максимальное количество аккаунтов. Пожалуйста, сообщите разработчику приложения.\",\n  socialLoginScreen: {\n    title: \"Войти\",\n    instruction: \"Войдите в свой аккаунт во всплывающем окне\",\n    failed: \"Не удалось войти\",\n    retry: \"Повторить попытку\",\n  },\n  emailLoginScreen: {\n    title: \"Войти\",\n    enterCodeSendTo: \"Введите код подтверждения, отправленный на\",\n    newDeviceDetected: \"Обнаружено новое устройство\",\n    enterRecoveryCode:\n      \"Введите код восстановления, отправленный вам по электронной почте при первой регистрации\",\n    invalidCode: \"Неверный код подтверждения\",\n    invalidCodeOrRecoveryCode:\n      \"Неверный код подтверждения или код восстановления\",\n    verify: \"Подтвердить\",\n    failedToSendCode: \"Не удалось отправить код подтверждения\",\n    sendingCode: \"Отправка кода подтверждения\",\n    resendCode: \"Повторно отправить код подтверждения\",\n  },\n  createPassword: {\n    title: \"Создать пароль\",\n    instruction:\n      \"Установите пароль для вашего аккаунта. Этот пароль понадобится при подключении с нового устройства.\",\n    saveInstruction: \"Обязательно сохраните его\",\n    inputPlaceholder: \"Введите ваш пароль\",\n    confirmation: \"Я сохранил свой пароль\",\n    submitButton: \"Установить пароль\",\n    failedToSetPassword: \"Не удалось установить пароль\",\n  },\n  enterPassword: {\n    title: \"Введите пароль\",\n    instruction: \"Введите пароль для вашего аккаунта\",\n    inputPlaceholder: \"Введите ваш пароль\",\n    submitButton: \"Подтвердить\",\n    wrongPassword: \"Неверный пароль\",\n  },\n  signInWithEmail: \"Войти с помощью электронной почты\",\n  invalidPhone: \"Недействительный номер телефона\",\n  phonePlaceholder: \"Номер телефона\",\n  signInWithPhone: \"Войти с помощью номера телефона\",\n  phoneRequired: \"Требуется номер телефона\",\n  passkey: \"Ключ доступа\",\n  signInWithWallet: \"Войти с помощью кошелька\",\n  linkWallet: \"Привязать кошелек\",\n  loginAsGuest: \"Продолжить как гость\",\n} satisfies InAppWalletLocale;\n"], "mappings": ";;;AAEA,IAAA,aAAe;EACb,kBAAkB;EAClB,oBAAoB;EACpB,iBAAiB;EACjB,mBAAmB;EACnB,kBAAkB;EAClB,aAAa;EACb,QAAQ;EACR,IAAI;EACJ,eAAe;EACf,cAAc;EACd,qBACE;EACF,mBAAmB;IACjB,OAAO;IACP,aAAa;IACb,QAAQ;IACR,OAAO;;EAET,kBAAkB;IAChB,OAAO;IACP,iBAAiB;IACjB,mBAAmB;IACnB,mBACE;IACF,aAAa;IACb,2BACE;IACF,QAAQ;IACR,kBAAkB;IAClB,aAAa;IACb,YAAY;;EAEd,gBAAgB;IACd,OAAO;IACP,aACE;IACF,iBAAiB;IACjB,kBAAkB;IAClB,cAAc;IACd,cAAc;IACd,qBAAqB;;EAEvB,eAAe;IACb,OAAO;IACP,aAAa;IACb,kBAAkB;IAClB,cAAc;IACd,eAAe;;EAEjB,iBAAiB;EACjB,cAAc;EACd,kBAAkB;EAClB,iBAAiB;EACjB,eAAe;EACf,SAAS;EACT,kBAAkB;EAClB,YAAY;EACZ,cAAc;;", "names": []}