import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/org.bitizen/index.js
var wallet = {
  id: "org.bitizen",
  name: "Bitizen",
  homepage: "https://bitizen.org/",
  image_id: "75dd1471-77e9-4811-ce57-ec8fc980ec00",
  app: {
    browser: null,
    ios: "https://apps.apple.com/us/app/bitizen-defi-web3-eth-wallet/id1598283542",
    android: "https://play.google.com/store/apps/details?id=org.bitizen.wallet",
    mac: null,
    windows: null,
    linux: null,
    chrome: "https://bitizen.org/",
    firefox: null,
    safari: null,
    edge: null,
    opera: null
  },
  rdns: null,
  mobile: {
    native: "bitizen://wallet/",
    universal: "https://bitizen.org/wallet/"
  },
  desktop: {
    native: null,
    universal: null
  }
};
export {
  wallet
};
//# sourceMappingURL=org-CMTWDAOG.js.map
