{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.atomicwallet/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.atomicwallet\",\n  name: \"Atomic Wallet\",\n  homepage: \"https://atomicwallet.io\",\n  image_id: \"7eca0311-abf5-4902-43e9-51858403e200\",\n  app: {\n    browser: \"https://atomicwallet.io\",\n    ios: \"https://apps.apple.com/us/app/atomic-wallet/id1478257827\",\n    android: \"https://play.google.com/store/apps/details?id=io.atomicwallet\",\n    mac: \"https://atomicwallet.io/downloads\",\n    windows: \"https://atomicwallet.io/downloads\",\n    linux: \"https://atomicwallet.io/downloads\",\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"atomicwallet://\",\n    universal: null,\n  },\n  desktop: {\n    native: \"atomicwallet://\",\n    universal:\n      \"https://chromewebstore.google.com/detail/atomic-wallet/gjnckgkfmgmibbkoficdidcljeaaaheg\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WACE;;;", "names": []}