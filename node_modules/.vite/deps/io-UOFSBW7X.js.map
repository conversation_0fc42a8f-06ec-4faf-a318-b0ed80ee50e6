{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.wallacy/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.wallacy\",\n  name: \"Wallacy\",\n  homepage: \"https://wallacy.io\",\n  image_id: \"9496c3d8-8b60-495f-bd55-c3af19519d00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/wallacy-crypto-btc-wallet/id6448592576\",\n    android:\n      \"https://play.google.com/store/apps/details?id=io.wallacy.cryptowallet\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"wallacy://\",\n    universal: null,\n  },\n  desktop: {\n    native: \"wallacy://\",\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}