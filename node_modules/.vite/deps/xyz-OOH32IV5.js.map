{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/xyz.sequence/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"xyz.sequence\",\n  name: \"Sequence Wallet\",\n  homepage: \"https://sequence.xyz/\",\n  image_id: \"b2d5c39c-a485-4efa-5736-a782204e4a00\",\n  app: {\n    browser: \"https://sequence.app\",\n    ios: null,\n    android: null,\n    mac: null,\n    windows: null,\n    linux: \"https://sequence.app/\",\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: null,\n    universal: \"https://sequence.app\",\n  },\n  desktop: {\n    native: null,\n    universal: \"https://sequence.app\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}