import {
  createInAppWallet,
  createWalletEmitter,
  trackConnect
} from "./chunk-DD4ZDBMS.js";
import {
  isEcosystemWallet
} from "./chunk-QDEEV5NE.js";
import {
  COINBASE,
  METAMASK,
  getCoinbaseWebProvider
} from "./chunk-4BEPVD3M.js";
import {
  getValidPublicRPCUrl,
  normalizeChainId
} from "./chunk-D7AA5VAR.js";
import {
  isBrowser
} from "./chunk-IA4CMUWX.js";
import {
  parseTypedData,
  trackTransaction
} from "./chunk-OHQKMKT3.js";
import {
  webLocalStorage
} from "./chunk-G4H2UJKK.js";
import {
  getDefaultAccountFactory
} from "./chunk-46GSJ545.js";
import {
  isContractDeployed
} from "./chunk-3S7RRRP4.js";
import {
  isZkSyncChain
} from "./chunk-WLZN2VO2.js";
import {
  getContract
} from "./chunk-UEKVYVRB.js";
import {
  getAddress
} from "./chunk-IHMGPG6V.js";
import {
  numberToHex,
  stringToHex,
  uint8ArrayToHex
} from "./chunk-E4AXWHD7.js";
import {
  getTypesForEIP712Domain,
  serializeTypedData,
  validateTypedData
} from "./chunk-YXD4WFHV.js";
import {
  getCachedChain,
  getCachedChainIfExists,
  getChainMetadata
} from "./chunk-KQKMGIQ6.js";
import {
  detectOS
} from "./chunk-3OXDSLPJ.js";

// node_modules/mipd/dist/esm/utils.js
function requestProviders(listener) {
  if (typeof window === "undefined")
    return;
  const handler = (event) => listener(event.detail);
  window.addEventListener("eip6963:announceProvider", handler);
  window.dispatchEvent(new CustomEvent("eip6963:requestProvider"));
  return () => window.removeEventListener("eip6963:announceProvider", handler);
}

// node_modules/mipd/dist/esm/store.js
function createStore() {
  const listeners = /* @__PURE__ */ new Set();
  let providerDetails = [];
  const request = () => requestProviders((providerDetail) => {
    if (providerDetails.some(({ info }) => info.uuid === providerDetail.info.uuid))
      return;
    providerDetails = [...providerDetails, providerDetail];
    listeners.forEach((listener) => listener(providerDetails, { added: [providerDetail] }));
  });
  let unwatch = request();
  return {
    _listeners() {
      return listeners;
    },
    clear() {
      listeners.forEach((listener) => listener([], { removed: [...providerDetails] }));
      providerDetails = [];
    },
    destroy() {
      this.clear();
      listeners.clear();
      unwatch == null ? void 0 : unwatch();
    },
    findProvider({ rdns }) {
      return providerDetails.find((providerDetail) => providerDetail.info.rdns === rdns);
    },
    getProviders() {
      return providerDetails;
    },
    reset() {
      this.clear();
      unwatch == null ? void 0 : unwatch();
      unwatch = request();
    },
    subscribe(listener, { emitImmediately } = {}) {
      listeners.add(listener);
      if (emitImmediately)
        listener(providerDetails, { added: providerDetails });
      return () => listeners.delete(listener);
    }
  };
}

// node_modules/thirdweb/dist/esm/utils/web/isMobile.js
function isAndroid() {
  if (typeof navigator === "undefined") {
    return false;
  }
  const os = detectOS(navigator.userAgent);
  return os ? os.toLowerCase().includes("android") : false;
}
function isIOS() {
  if (typeof navigator === "undefined") {
    return false;
  }
  const os = detectOS(navigator.userAgent);
  return os ? os.toLowerCase().includes("ios") || os.toLowerCase().includes("mac") && navigator.maxTouchPoints > 1 : false;
}
function isMobile() {
  return isAndroid() || isIOS();
}

// node_modules/thirdweb/dist/esm/utils/web/openWindow.js
function openWindow(uri) {
  const isInsideIframe = window !== window.top;
  if (isInsideIframe) {
    window.open(uri);
  } else {
    if (uri.startsWith("http")) {
      const link = document.createElement("a");
      link.href = uri;
      link.target = "_blank";
      link.rel = "noreferrer noopener";
      link.click();
    } else {
      window.location.href = uri;
    }
  }
}

// node_modules/thirdweb/dist/esm/wallets/coinbase/coinbase-wallet.js
function coinbaseWalletSDK(args) {
  const { createOptions } = args;
  const emitter = createWalletEmitter();
  let account = void 0;
  let chain = void 0;
  function reset() {
    account = void 0;
    chain = void 0;
  }
  let handleDisconnect = async () => {
  };
  let handleSwitchChain = async (newChain) => {
    chain = newChain;
  };
  const unsubscribeChainChanged = emitter.subscribe("chainChanged", (newChain) => {
    chain = newChain;
  });
  const unsubscribeDisconnect = emitter.subscribe("disconnect", () => {
    reset();
    unsubscribeChainChanged();
    unsubscribeDisconnect();
  });
  emitter.subscribe("accountChanged", (_account) => {
    account = _account;
  });
  return {
    id: COINBASE,
    subscribe: emitter.subscribe,
    getChain() {
      if (!chain) {
        return void 0;
      }
      chain = getCachedChainIfExists(chain.id) || chain;
      return chain;
    },
    getConfig: () => createOptions,
    getAccount: () => account,
    autoConnect: async (options) => {
      const { autoConnectCoinbaseWalletSDK } = await import("./coinbase-web-4S3RBCKE.js");
      const provider = await args.providerFactory();
      const [connectedAccount, connectedChain, doDisconnect, doSwitchChain] = await autoConnectCoinbaseWalletSDK(options, emitter, provider);
      account = connectedAccount;
      chain = connectedChain;
      handleDisconnect = doDisconnect;
      handleSwitchChain = doSwitchChain;
      trackConnect({
        client: options.client,
        walletType: COINBASE,
        walletAddress: account.address,
        chainId: chain.id
      });
      return account;
    },
    connect: async (options) => {
      const { connectCoinbaseWalletSDK } = await import("./coinbase-web-4S3RBCKE.js");
      const provider = await args.providerFactory();
      const [connectedAccount, connectedChain, doDisconnect, doSwitchChain] = await connectCoinbaseWalletSDK(options, emitter, provider);
      account = connectedAccount;
      chain = connectedChain;
      handleDisconnect = doDisconnect;
      handleSwitchChain = doSwitchChain;
      trackConnect({
        client: options.client,
        walletType: COINBASE,
        walletAddress: account.address,
        chainId: chain.id
      });
      return account;
    },
    disconnect: async () => {
      reset();
      await handleDisconnect();
    },
    switchChain: async (newChain) => {
      await handleSwitchChain(newChain);
    },
    onConnectRequested: async () => {
      var _a;
      if (args.onConnectRequested) {
        const provider = await args.providerFactory();
        return (_a = args.onConnectRequested) == null ? void 0 : _a.call(args, provider);
      }
    }
  };
}

// node_modules/thirdweb/dist/esm/wallets/in-app/web/ecosystem.js
function ecosystemWallet(...args) {
  const [ecosystemId, createOptions] = args;
  const ecosystem = {
    id: ecosystemId,
    partnerId: createOptions == null ? void 0 : createOptions.partnerId
  };
  return createInAppWallet({
    ecosystem,
    createOptions: {
      auth: {
        ...createOptions == null ? void 0 : createOptions.auth,
        options: []
        // controlled by ecosystem
      },
      partnerId: ecosystem.partnerId
    },
    connectorFactory: async (client) => {
      const { InAppWebConnector } = await import("./web-connector-E7NYPPBG.js");
      return new InAppWebConnector({
        client,
        ecosystem,
        storage: createOptions == null ? void 0 : createOptions.storage
      });
    }
  });
}

// node_modules/thirdweb/dist/esm/wallets/in-app/web/in-app.js
function inAppWallet(createOptions) {
  return createInAppWallet({
    createOptions,
    connectorFactory: async (client) => {
      var _a;
      const { InAppWebConnector } = await import("./web-connector-E7NYPPBG.js");
      return new InAppWebConnector({
        client,
        passkeyDomain: (_a = createOptions == null ? void 0 : createOptions.auth) == null ? void 0 : _a.passkeyDomain,
        storage: createOptions == null ? void 0 : createOptions.storage
      });
    }
  });
}

// node_modules/thirdweb/dist/esm/wallets/injected/index.js
function getInjectedProvider(walletId) {
  const provider = injectedProvider(walletId);
  if (!provider) {
    throw new Error(`No injected provider found for wallet: "${walletId}"`);
  }
  return provider;
}
async function connectEip1193Wallet({ id, provider, emitter, client, chain }) {
  var _a, _b;
  let addresses;
  const retries = 3;
  let attempts = 0;
  while (!(addresses == null ? void 0 : addresses[0]) && attempts < retries) {
    try {
      addresses = await provider.request({
        method: "eth_requestAccounts"
      });
    } catch (e) {
      console.error(e);
      if ((_b = (_a = extractErrorMessage(e)) == null ? void 0 : _a.toLowerCase()) == null ? void 0 : _b.includes("rejected")) {
        throw e;
      }
      await new Promise((resolve) => setTimeout(resolve, 500));
    }
    attempts++;
  }
  const addr = addresses == null ? void 0 : addresses[0];
  if (!addr) {
    throw new Error("Failed to connect to wallet, no accounts available");
  }
  const address = getAddress(addr);
  const chainId = await provider.request({ method: "eth_chainId" }).then(normalizeChainId).catch((e) => {
    throw new Error("Error reading chainId from provider", e);
  });
  let connectedChain = chain && chain.id === chainId ? chain : getCachedChain(chainId);
  try {
    if (chain && typeof chain.id !== "undefined" && chain.id !== chainId) {
      await switchChain(provider, chain);
      connectedChain = chain;
    }
  } catch {
    console.warn(`Error switching to chain ${chain == null ? void 0 : chain.id} - defaulting to wallet chain (${chainId})`);
  }
  return onConnect({
    provider,
    address,
    chain: connectedChain,
    emitter,
    client,
    id
  });
}
async function autoConnectEip1193Wallet({ id, provider, emitter, client, chain }) {
  const addresses = await provider.request({
    method: "eth_accounts"
  });
  const addr = addresses[0];
  if (!addr) {
    throw new Error("Failed to connect to wallet, no accounts available");
  }
  const address = getAddress(addr);
  const chainId = await provider.request({ method: "eth_chainId" }).then(normalizeChainId);
  const connectedChain = chain && chain.id === chainId ? chain : getCachedChain(chainId);
  return onConnect({
    provider,
    address,
    chain: connectedChain,
    emitter,
    client,
    id
  });
}
function createAccount({ provider, address, client, id }) {
  const account = {
    address: getAddress(address),
    async sendTransaction(tx) {
      const gasFees = tx.gasPrice ? {
        gasPrice: tx.gasPrice ? numberToHex(tx.gasPrice) : void 0
      } : {
        maxFeePerGas: tx.maxFeePerGas ? numberToHex(tx.maxFeePerGas) : void 0,
        maxPriorityFeePerGas: tx.maxPriorityFeePerGas ? numberToHex(tx.maxPriorityFeePerGas) : void 0
      };
      const params = [
        {
          ...gasFees,
          nonce: tx.nonce ? numberToHex(tx.nonce) : void 0,
          accessList: tx.accessList,
          value: tx.value ? numberToHex(tx.value) : void 0,
          gas: tx.gas ? numberToHex(tx.gas) : void 0,
          from: this.address,
          to: tx.to ? getAddress(tx.to) : void 0,
          data: tx.data,
          ...tx.eip712
        }
      ];
      const transactionHash = await provider.request({
        method: "eth_sendTransaction",
        // @ts-expect-error - overriding types here
        params
      });
      trackTransaction({
        client,
        chainId: tx.chainId,
        walletAddress: getAddress(address),
        walletType: id,
        transactionHash,
        contractAddress: tx.to ?? void 0,
        gasPrice: tx.gasPrice
      });
      return {
        transactionHash
      };
    },
    async signMessage({ message }) {
      if (!account.address) {
        throw new Error("Provider not setup");
      }
      const messageToSign = (() => {
        if (typeof message === "string") {
          return stringToHex(message);
        }
        if (message.raw instanceof Uint8Array) {
          return uint8ArrayToHex(message.raw);
        }
        return message.raw;
      })();
      return await provider.request({
        method: "personal_sign",
        params: [messageToSign, getAddress(account.address)]
      });
    },
    async signTypedData(typedData) {
      if (!provider || !account.address) {
        throw new Error("Provider not setup");
      }
      const parsedTypedData = parseTypedData(typedData);
      const { domain, message, primaryType } = parsedTypedData;
      const types = {
        EIP712Domain: getTypesForEIP712Domain({ domain }),
        ...parsedTypedData.types
      };
      validateTypedData({ domain, message, primaryType, types });
      const stringifiedData = serializeTypedData({
        domain: domain ?? {},
        message,
        primaryType,
        types
      });
      return await provider.request({
        method: "eth_signTypedData_v4",
        params: [getAddress(account.address), stringifiedData]
      });
    },
    async watchAsset(asset) {
      const result = await provider.request({
        method: "wallet_watchAsset",
        params: asset
      }, { retryCount: 0 });
      return result;
    }
  };
  return account;
}
async function onConnect({ provider, address, chain, emitter, client, id }) {
  const account = createAccount({ provider, address, client, id });
  async function disconnect() {
    provider.removeListener("accountsChanged", onAccountsChanged);
    provider.removeListener("chainChanged", onChainChanged);
    provider.removeListener("disconnect", onDisconnect);
  }
  async function onDisconnect() {
    disconnect();
    emitter.emit("disconnect", void 0);
  }
  function onAccountsChanged(accounts) {
    if (accounts[0]) {
      const newAccount = createAccount({
        provider,
        address: getAddress(accounts[0]),
        client,
        id
      });
      emitter.emit("accountChanged", newAccount);
      emitter.emit("accountsChanged", accounts);
    } else {
      onDisconnect();
    }
  }
  function onChainChanged(newChainId) {
    const newChain = getCachedChain(normalizeChainId(newChainId));
    emitter.emit("chainChanged", newChain);
  }
  if (provider.on) {
    provider.on("accountsChanged", onAccountsChanged);
    provider.on("chainChanged", onChainChanged);
    provider.on("disconnect", onDisconnect);
  }
  return [
    account,
    chain,
    onDisconnect,
    (newChain) => switchChain(provider, newChain)
  ];
}
async function switchChain(provider, chain) {
  var _a;
  const hexChainId = numberToHex(chain.id);
  try {
    await provider.request({
      method: "wallet_switchEthereumChain",
      params: [{ chainId: hexChainId }]
    });
  } catch {
    const apiChain = await getChainMetadata(chain);
    await provider.request({
      method: "wallet_addEthereumChain",
      params: [
        {
          chainId: hexChainId,
          chainName: apiChain.name,
          nativeCurrency: apiChain.nativeCurrency,
          rpcUrls: getValidPublicRPCUrl(apiChain),
          // no client id on purpose here
          blockExplorerUrls: (_a = apiChain.explorers) == null ? void 0 : _a.map((x) => x.url)
        }
      ]
    });
  }
}
function extractErrorMessage(e) {
  if (e instanceof Error) {
    return e.message;
  }
  if (typeof e === "string") {
    return e;
  }
  if (typeof e === "object" && e !== null) {
    return JSON.stringify(e);
  }
  return String(e);
}

// node_modules/thirdweb/dist/esm/wallets/smart/smart-wallet.js
function smartWallet(createOptions) {
  const emitter = createWalletEmitter();
  let account = void 0;
  let adminAccount = void 0;
  let chain = void 0;
  let lastConnectOptions;
  return {
    id: "smart",
    subscribe: emitter.subscribe,
    getChain() {
      if (!chain) {
        return void 0;
      }
      chain = getCachedChainIfExists(chain.id) || chain;
      return chain;
    },
    getConfig: () => createOptions,
    getAccount: () => account,
    getAdminAccount: () => adminAccount,
    autoConnect: async (options) => {
      const { connectSmartAccount: connectSmartWallet } = await import("./smart-J3ZZR2U4.js");
      const [connectedAccount, connectedChain] = await connectSmartWallet(options, createOptions);
      lastConnectOptions = options;
      account = connectedAccount;
      chain = connectedChain;
      trackConnect({
        client: options.client,
        walletType: "smart",
        walletAddress: account.address,
        chainId: chain.id
      });
      return account;
    },
    connect: async (options) => {
      const { connectSmartAccount } = await import("./smart-J3ZZR2U4.js");
      const [connectedAccount, connectedChain] = await connectSmartAccount(options, createOptions);
      adminAccount = options.personalAccount;
      lastConnectOptions = options;
      account = connectedAccount;
      chain = connectedChain;
      trackConnect({
        client: options.client,
        walletType: "smart",
        walletAddress: account.address,
        chainId: chain.id
      });
      emitter.emit("accountChanged", account);
      return account;
    },
    disconnect: async () => {
      if (account) {
        const { disconnectSmartAccount } = await import("./smart-J3ZZR2U4.js");
        await disconnectSmartAccount(account);
      }
      account = void 0;
      adminAccount = void 0;
      chain = void 0;
      emitter.emit("disconnect", void 0);
    },
    switchChain: async (newChain) => {
      var _a;
      if (!lastConnectOptions) {
        throw new Error("Cannot switch chain without a previous connection");
      }
      const isZksyncChain = await isZkSyncChain(newChain);
      if (!isZksyncChain) {
        const factory = getContract({
          address: createOptions.factoryAddress || getDefaultAccountFactory((_a = createOptions.overrides) == null ? void 0 : _a.entrypointAddress),
          chain: newChain,
          client: lastConnectOptions.client
        });
        const isDeployed = await isContractDeployed(factory);
        if (!isDeployed) {
          throw new Error(`Factory contract not deployed on chain: ${newChain.id}`);
        }
      }
      const { connectSmartAccount } = await import("./smart-J3ZZR2U4.js");
      const [connectedAccount, connectedChain] = await connectSmartAccount({ ...lastConnectOptions, chain: newChain }, createOptions);
      account = connectedAccount;
      chain = connectedChain;
      emitter.emit("accountChanged", connectedAccount);
      emitter.emit("chainChanged", connectedChain);
    }
  };
}

// node_modules/thirdweb/dist/esm/wallets/create-wallet.js
function createWallet(...args) {
  const [id, creationOptions] = args;
  switch (true) {
    case id === "smart": {
      return smartWallet(creationOptions);
    }
    case (id === "embedded" || id === "inApp"): {
      return inAppWallet(creationOptions);
    }
    case isEcosystemWallet(id):
      return ecosystemWallet(...args);
    case id === COINBASE: {
      const options = creationOptions;
      return coinbaseWalletSDK({
        createOptions: options,
        providerFactory: () => getCoinbaseWebProvider(options),
        onConnectRequested: async (provider) => {
          const { showCoinbasePopup } = await import("./utils-TPJADCMG.js");
          return showCoinbasePopup(provider);
        }
      });
    }
    default: {
      let reset = function() {
        account = void 0;
        chain = void 0;
      };
      const emitter = createWalletEmitter();
      let account = void 0;
      let chain = void 0;
      let unsubscribeChain = void 0;
      let handleDisconnect = async () => {
      };
      const unsubscribeDisconnect = emitter.subscribe("disconnect", () => {
        reset();
        unsubscribeChain == null ? void 0 : unsubscribeChain();
        unsubscribeDisconnect();
      });
      emitter.subscribe("accountChanged", (_account) => {
        account = _account;
      });
      let handleSwitchChain = async () => {
        throw new Error("Not implemented yet");
      };
      const sessionHandler = isMobile() ? (uri) => openWindow(uri) : void 0;
      const wallet = {
        id,
        subscribe: emitter.subscribe,
        getConfig: () => args[1],
        getChain() {
          if (!chain) {
            return void 0;
          }
          chain = getCachedChainIfExists(chain.id) || chain;
          return chain;
        },
        getAccount: () => account,
        autoConnect: async (options) => {
          const { injectedProvider: injectedProvider2 } = await import("./mipdStore-QNYZOLQN.js");
          if (id !== "walletConnect" && injectedProvider2(id)) {
            const { autoConnectEip1193Wallet: autoConnectEip1193Wallet2 } = await import("./injected-OIK2L34R.js");
            const [connectedAccount, connectedChain, doDisconnect, doSwitchChain] = await autoConnectEip1193Wallet2({
              id,
              provider: getInjectedProvider(id),
              emitter,
              chain: options.chain,
              client: options.client
            });
            account = connectedAccount;
            chain = connectedChain;
            handleDisconnect = doDisconnect;
            handleSwitchChain = doSwitchChain;
            unsubscribeChain = emitter.subscribe("chainChanged", (newChain) => {
              chain = newChain;
            });
            trackConnect({
              client: options.client,
              walletType: id,
              walletAddress: account.address,
              chainId: chain.id
            });
            return account;
          }
          if (options && "client" in options) {
            const { autoConnectWC } = await import("./controller-DA2DE4UB.js");
            const [connectedAccount, connectedChain, doDisconnect, doSwitchChain] = await autoConnectWC(options, emitter, wallet.id, webLocalStorage, sessionHandler);
            account = connectedAccount;
            chain = connectedChain;
            handleDisconnect = doDisconnect;
            handleSwitchChain = doSwitchChain;
            trackConnect({
              client: options.client,
              walletType: id,
              walletAddress: account.address,
              chainId: chain.id
            });
            return account;
          }
          throw new Error("Failed to auto connect");
        },
        connect: async (options) => {
          async function wcConnect(wcOptions) {
            const { connectWC } = await import("./controller-DA2DE4UB.js");
            const [connectedAccount, connectedChain, doDisconnect, doSwitchChain] = await connectWC(wcOptions, emitter, wallet.id, webLocalStorage, sessionHandler);
            account = connectedAccount;
            chain = connectedChain;
            handleDisconnect = doDisconnect;
            handleSwitchChain = doSwitchChain;
            trackConnect({
              client: wcOptions.client,
              walletType: id,
              walletAddress: account.address,
              chainId: chain.id
            });
            return account;
          }
          if (id === "walletConnect") {
            const { client, chain: _chain, ...walletConnectOptions } = options;
            return wcConnect({
              client,
              chain: _chain,
              walletConnect: {
                ...walletConnectOptions
              }
            });
          }
          const forceWalletConnectOption = options && "walletConnect" in options;
          const { injectedProvider: injectedProvider2 } = await import("./mipdStore-QNYZOLQN.js");
          if (injectedProvider2(id) && !forceWalletConnectOption) {
            const { connectEip1193Wallet: connectEip1193Wallet2 } = await import("./injected-OIK2L34R.js");
            const [connectedAccount, connectedChain, doDisconnect, doSwitchChain] = await connectEip1193Wallet2({
              id,
              provider: getInjectedProvider(id),
              client: options.client,
              chain: options.chain,
              emitter
            });
            account = connectedAccount;
            chain = connectedChain;
            handleDisconnect = doDisconnect;
            handleSwitchChain = doSwitchChain;
            unsubscribeChain = emitter.subscribe("chainChanged", (newChain) => {
              chain = newChain;
            });
            trackConnect({
              client: options.client,
              walletType: id,
              walletAddress: account.address,
              chainId: chain.id
            });
            return account;
          }
          if (options && "client" in options) {
            return wcConnect(options);
          }
          throw new Error("Failed to connect");
        },
        // these get overridden in connect and autoConnect
        disconnect: async () => {
          reset();
          await handleDisconnect();
        },
        switchChain: (c) => handleSwitchChain(c)
      };
      return wallet;
    }
  }
}
function walletConnect() {
  return createWallet("walletConnect");
}

// node_modules/thirdweb/dist/esm/wallets/injected/mipdStore.js
var mipdStore = (() => isBrowser() ? createStore() : void 0)();
function injectedProvider(walletId) {
  const injectedProviderDetail = getInstalledWalletProviders().find((p) => p.info.rdns === walletId);
  return injectedProviderDetail == null ? void 0 : injectedProviderDetail.provider;
}
function getInstalledWallets() {
  const providers = getInstalledWalletProviders();
  const walletIds = providers.map((provider) => provider.info.rdns);
  return walletIds.map((w) => createWallet(w));
}
function getMIPDStore() {
  if (!mipdStore) {
    return void 0;
  }
  return mipdStore;
}
function getInstalledWalletProviders() {
  var _a;
  const providers = ((_a = getMIPDStore()) == null ? void 0 : _a.getProviders()) || [];
  for (const provider of providers) {
    if (provider.info.rdns === "io.metamask.mobile") {
      provider.info.rdns = METAMASK;
      break;
    }
  }
  return providers;
}

export {
  isAndroid,
  isIOS,
  isMobile,
  openWindow,
  ecosystemWallet,
  inAppWallet,
  injectedProvider,
  getInstalledWallets,
  getInstalledWalletProviders,
  getInjectedProvider,
  connectEip1193Wallet,
  autoConnectEip1193Wallet,
  smartWallet,
  createWallet,
  walletConnect
};
//# sourceMappingURL=chunk-QIHVSQYQ.js.map
