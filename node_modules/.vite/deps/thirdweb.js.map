{"version": 3, "sources": ["../../thirdweb/src/utils/hashing/sha256.ts", "../../thirdweb/src/utils/client-id.ts", "../../thirdweb/src/client/client.ts", "../../thirdweb/src/rpc/actions/eth_getBlockByHash.ts", "../../thirdweb/src/rpc/actions/eth_getTransactionByHash.ts", "../../thirdweb/src/engine/index.ts", "../../@thirdweb-dev/engine/src/client/client.gen.ts", "../../@thirdweb-dev/engine/src/client/sdk.gen.ts", "../../thirdweb/src/engine/get-status.ts", "../../thirdweb/src/engine/server-wallet.ts", "../../thirdweb/src/transaction/resolve-method.ts", "../../thirdweb/src/auth/verify-typed-data.ts", "../../thirdweb/src/transaction/actions/eip7702/authorization.ts"], "sourcesContent": ["import { sha256 as noble_sha256 } from \"@noble/hashes/sha256\";\nimport {\n  type Hex,\n  hexToUint8Array,\n  isHex,\n  uint8ArrayToHex,\n} from \"../encoding/hex.js\";\n\ntype To = \"hex\" | \"bytes\";\n\ntype Sha256Hash<TTo extends To> =\n  | (TTo extends \"bytes\" ? Uint8Array : never)\n  | (TTo extends \"hex\" ? Hex : never);\n\n/**\n * Calculates the SHA256 hash of the given value.\n * @param value - The value to hash. It can be either a hexadecimal string or a Uint8Array.\n * @param to - (Optional) The desired output format of the hash. Defaults to 'hex'.\n * @returns The SHA256 hash of the value in the specified format.\n * @example\n * ```ts\n * import { sha256 } from \"thirdweb/utils\";\n * const hash = sha256(\"0x1234\");\n * ```\n * @utils\n */\nexport function sha256<TTo extends To = \"hex\">(\n  value: Hex | Uint8Array,\n  to?: TTo,\n): Sha256Hash<TTo> {\n  const bytes = noble_sha256(\n    isHex(value, { strict: false }) ? hexToUint8Array(value) : value,\n  );\n  if (to === \"bytes\") {\n    return bytes as Sha256Hash<TTo>;\n  }\n  return uint8ArrayToHex(bytes) as Sha256Hash<TTo>;\n}\n", "import { LruMap } from \"./caching/lru.js\";\nimport { stringToBytes } from \"./encoding/to-bytes.js\";\nimport { sha256 } from \"./hashing/sha256.js\";\n\nconst cache = new LruMap<string>(4096);\n\n/**\n * @param secretKey - the secret key to compute the client id from\n * @returns the 32 char hex client id\n * @internal\n */\nexport function computeClientIdFromSecretKey(secretKey: string): string {\n  if (cache.has(secretKey)) {\n    return cache.get(secretKey) as string;\n  }\n  // we slice off the leading `0x` and then take the first 32 chars\n  const cId = sha256(stringToBytes(secretKey)).slice(2, 34);\n  cache.set(secretKey, cId);\n  return cId;\n}\n", "import { computeClientIdFromSecretKey } from \"../utils/client-id.js\";\nimport { isJWT } from \"../utils/jwt/is-jwt.js\";\nimport type { Prettify } from \"../utils/type-utils.js\";\n\ntype FetchConfig = {\n  requestTimeoutMs?: number;\n  keepalive?: boolean;\n  headers?: HeadersInit;\n};\n\ntype ClientOptions = Prettify<{\n  /**\n   * The configuration options for the client.\n   */\n  config?: {\n    /**\n     * The configuration options for the RPC client.\n     */\n    rpc?: {\n      /**\n       * The configuration options for the fetch function.\n       * @default {}\n       */\n      fetch?: FetchConfig;\n      /**\n       * The maximum number of requests to batch together.\n       * @default 100\n       */\n      maxBatchSize?: number;\n      /**\n       * The maximum time to wait before sending a batch of requests.\n       * @default 0 (no timeout)\n       */\n      batchTimeoutMs?: number;\n    };\n    /**\n     * The configuration options for the storage client.\n     */\n    storage?: {\n      /**\n       * The configuration options for the fetch function.\n       * @default {}\n       */\n      fetch?: FetchConfig;\n      /**\n       * The IPFS gateway URL.\n       * @default \"https://<your_client_id>.ipfscdn.io/ipfs/<cid>\"\n       */\n      gatewayUrl?: string;\n    };\n  };\n\n  /**\n   * The team ID for thirdweb dashboard usage.\n   * @hidden\n   */\n  teamId?: string;\n}>;\n\nexport type CreateThirdwebClientOptions = Prettify<\n  (\n    | {\n        clientId: string;\n        secretKey?: string;\n      }\n    | {\n        clientId?: string;\n        secretKey: string;\n      }\n  ) &\n    ClientOptions\n>;\n\nexport type ThirdwebClient = {\n  readonly clientId: string;\n  readonly secretKey: string | undefined;\n} & Readonly<ClientOptions>;\n\n/**\n * Creates a Thirdweb client using the provided client ID (client-side) or secret key (server-side).\n *\n * Get your client ID and secret key from the Thirdweb dashboard [here](https://thirdweb.com/create-api-key).\n * **Never share your secret key with anyone.\n *\n * A client is necessary for most functions in the thirdweb SDK. It provides access to thirdweb APIs including built-in RPC, storage, and more.\n *\n * @param options - The options for creating the client.\n * @param [options.clientId] - The client ID to use for thirdweb services.\n * @param [options.secretKey] - The secret key to use for thirdweb services.\n * @returns The created Thirdweb client.\n * @throws An error if neither `clientId` nor `secretKey` is provided.\n *\n * @example\n * Create a client on the client side (client ID):\n * ```ts\n * import { createThirdwebClient } from \"thirdweb\";\n *\n * const client = createThirdwebClient({ clientId: \"...\" });\n * ```\n *\n * Create a client on the server (secret key):\n * ```ts\n * import { createThirdwebClient } from \"thirdweb\";\n *\n * const client = createThirdwebClient({ secretKey: \"...\" });\n * ```\n * @client\n */\nexport function createThirdwebClient(\n  options: CreateThirdwebClientOptions,\n): ThirdwebClient {\n  const { clientId, secretKey, ...rest } = options;\n\n  let realClientId: string | undefined = clientId;\n\n  if (secretKey) {\n    if (isJWT(secretKey)) {\n      // when passing a JWT as secret key we HAVE to also have a clientId\n      if (!clientId) {\n        throw new Error(\"clientId must be provided when using a JWT secretKey\");\n      }\n    } else {\n      // always PREFER the clientId if provided, only compute it from the secretKey if we don't have a clientId passed explicitly\n      realClientId = clientId ?? computeClientIdFromSecretKey(secretKey);\n    }\n  }\n\n  // only path we get here is if we have no secretKey and no clientId\n  if (!realClientId) {\n    throw new Error(\"clientId or secretKey must be provided\");\n  }\n\n  return {\n    ...rest,\n    clientId: realClientId,\n    secretKey,\n  } as const;\n}\n", "import {\n  type EIP1193RequestFn,\n  type EIP1474Methods,\n  type GetBlockReturnType,\n  type Hash,\n  formatBlock,\n} from \"viem\";\n\ntype GetBlockByHashParams<TIncludeTransactions extends boolean = false> = {\n  /** Whether or not to include transaction data in the response. */\n  includeTransactions?: TIncludeTransactions;\n} & {\n  /** Hash of the block. */\n  blockHash: Hash;\n};\n\n/**\n * Retrieves a block by its hash.\n * @param request - The EIP1193 request function.\n * @param params - The parameters for the block retrieval.\n * @returns A promise that resolves to the retrieved block.\n * @throws An error if the block is not found.\n * @rpc\n * @example\n * ```ts\n * import { getRpcClient, eth_getBlockByHash } from \"thirdweb/rpc\";\n * const rpcRequest = getRpcClient({ client, chain });\n * const block = await eth_getBlockByHash(rpcRequest, {\n * blockHash: \"0x...\",\n * includeTransactions: true,\n * });\n * ```\n */\nexport async function eth_getBlockByHash<\n  TIncludeTransactions extends boolean = false,\n>(\n  request: EIP1193RequestFn<EIP1474Methods>,\n  params: GetBlockByHashParams<TIncludeTransactions>,\n): Promise<GetBlockReturnType<undefined, TIncludeTransactions>> {\n  const includeTransactions = params.includeTransactions ?? false;\n\n  const block = await request({\n    method: \"eth_getBlockByHash\",\n    params: [params.blockHash, includeTransactions],\n  });\n  if (!block) {\n    throw new Error(\"Block not found\");\n  }\n  return formatBlock(block) as GetBlockReturnType<\n    undefined,\n    TIncludeTransactions\n  >;\n}\n", "import {\n  type EIP1193RequestFn,\n  type EIP1474Methods,\n  type Hash,\n  type Transaction,\n  formatTransaction,\n} from \"viem\";\n\ntype GetTransactionByHashParameters = {\n  hash: Hash;\n};\n\n/**\n * Retrieves a transaction by its hash.\n * @param request - The EIP1193 request function.\n * @param params - The parameters for retrieving the transaction.\n * @returns A promise that resolves to the transaction.\n * @throws An error if the transaction is not found.\n * @rpc\n * @example\n * ```ts\n * import { getRpcClient, eth_getTransactionByHash } from \"thirdweb/rpc\";\n * const rpcRequest = getRpcClient({ client, chain });\n *  const transaction = await eth_getTransactionByHash(rpcRequest, {\n *  hash: \"0x...\",\n * });\n * ```\n */\nexport async function eth_getTransactionByHash(\n  request: EIP1193RequestFn<EIP1474Methods>,\n  params: GetTransactionByHashParameters,\n): Promise<Transaction> {\n  const receipt = await request({\n    method: \"eth_getTransactionByHash\",\n    params: [params.hash],\n  });\n\n  if (!receipt) {\n    throw new Error(\"Transaction not found.\");\n  }\n\n  return formatTransaction(receipt);\n}\n", "export {\n  serverWallet,\n  type ServerWalletOptions,\n  type ServerWallet,\n} from \"./server-wallet.js\";\nexport {\n  getTransactionStatus,\n  waitForTransactionHash,\n  type ExecutionResult,\n  type RevertData,\n} from \"./get-status.js\";\n", "// This file is auto-generated by @hey-api/openapi-ts\n\nimport {\n  type Config,\n  type ClientOptions as DefaultClientOptions,\n  createClient,\n  createConfig,\n} from \"@hey-api/client-fetch\";\nimport type { ClientOptions } from \"./types.gen.js\";\n\n/**\n * The `createClientConfig()` function will be called on client initialization\n * and the returned object will become the client's initial configuration.\n *\n * You may want to initialize your client this way instead of calling\n * `setConfig()`. This is useful for example if you're using Next.js\n * to ensure your client always has the correct values.\n */\nexport type CreateClientConfig<T extends DefaultClientOptions = ClientOptions> =\n  (\n    override?: Config<DefaultClientOptions & T>,\n  ) => Config<Required<DefaultClientOptions> & T>;\n\nexport const client = createClient(\n  createConfig<ClientOptions>({\n    baseUrl: \"https://engine.thirdweb.com\",\n  }),\n);\n", "// This file is auto-generated by @hey-api/openapi-ts\n\nimport type {\n  Client,\n  Options as ClientOptions,\n  TDataShape,\n} from \"@hey-api/client-fetch\";\nimport { client as _heyApiClient } from \"./client.gen.js\";\nimport type {\n  EncodeFunctionDataData,\n  EncodeFunctionDataResponse,\n  GetNativeBalanceData,\n  GetNativeBalanceResponse,\n  GetTransactionAnalyticsData,\n  GetTransactionAnalyticsResponse,\n  GetTransactionAnalyticsSummaryData,\n  GetTransactionAnalyticsSummaryResponse,\n  ReadContractData,\n  ReadContractResponse,\n  SearchTransactionsData,\n  SearchTransactionsResponse,\n  SendTransactionData,\n  SendTransactionResponse,\n  SignMessageData,\n  SignMessageResponse,\n  SignTransactionData,\n  SignTransactionResponse,\n  SignTypedDataData,\n  SignTypedDataResponse,\n  WriteContractData,\n  WriteContractResponse,\n} from \"./types.gen.js\";\n\nexport type Options<\n  TData extends TDataShape = TDataShape,\n  ThrowOnError extends boolean = boolean,\n> = ClientOptions<TData, ThrowOnError> & {\n  /**\n   * You can provide a client instance returned by `createClient()` instead of\n   * individual options. This might be also useful if you want to implement a\n   * custom client.\n   */\n  client?: Client;\n  /**\n   * You can pass arbitrary values through the `meta` object. This can be\n   * used to access values that aren't defined as part of the SDK function.\n   */\n  meta?: Record<string, unknown>;\n};\n\n/**\n * Write Contract\n * Call a write function on a contract.\n */\nexport const writeContract = <ThrowOnError extends boolean = false>(\n  options?: Options<WriteContractData, ThrowOnError>,\n) => {\n  return (options?.client ?? _heyApiClient).post<\n    WriteContractResponse,\n    unknown,\n    ThrowOnError\n  >({\n    security: [\n      {\n        name: \"x-secret-key\",\n        type: \"apiKey\",\n      },\n    ],\n    url: \"/v1/write/contract\",\n    ...options,\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...options?.headers,\n    },\n  });\n};\n\n/**\n * Send Transaction\n * Send an encoded transaction or a batch of transactions\n */\nexport const sendTransaction = <ThrowOnError extends boolean = false>(\n  options?: Options<SendTransactionData, ThrowOnError>,\n) => {\n  return (options?.client ?? _heyApiClient).post<\n    SendTransactionResponse,\n    unknown,\n    ThrowOnError\n  >({\n    security: [\n      {\n        name: \"x-secret-key\",\n        type: \"apiKey\",\n      },\n    ],\n    url: \"/v1/write/transaction\",\n    ...options,\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...options?.headers,\n    },\n  });\n};\n\n/**\n * Sign Transaction\n * Sign transactions without sending them.\n */\nexport const signTransaction = <ThrowOnError extends boolean = false>(\n  options?: Options<SignTransactionData, ThrowOnError>,\n) => {\n  return (options?.client ?? _heyApiClient).post<\n    SignTransactionResponse,\n    unknown,\n    ThrowOnError\n  >({\n    security: [\n      {\n        name: \"x-secret-key\",\n        type: \"apiKey\",\n      },\n    ],\n    url: \"/v1/sign/transaction\",\n    ...options,\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...options?.headers,\n    },\n  });\n};\n\n/**\n * Sign Message\n * Sign arbitrary messages.\n */\nexport const signMessage = <ThrowOnError extends boolean = false>(\n  options?: Options<SignMessageData, ThrowOnError>,\n) => {\n  return (options?.client ?? _heyApiClient).post<\n    SignMessageResponse,\n    unknown,\n    ThrowOnError\n  >({\n    security: [\n      {\n        name: \"x-secret-key\",\n        type: \"apiKey\",\n      },\n    ],\n    url: \"/v1/sign/message\",\n    ...options,\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...options?.headers,\n    },\n  });\n};\n\n/**\n * Sign Typed Data\n * Sign EIP-712 typed data.\n */\nexport const signTypedData = <ThrowOnError extends boolean = false>(\n  options?: Options<SignTypedDataData, ThrowOnError>,\n) => {\n  return (options?.client ?? _heyApiClient).post<\n    SignTypedDataResponse,\n    unknown,\n    ThrowOnError\n  >({\n    security: [\n      {\n        name: \"x-secret-key\",\n        type: \"apiKey\",\n      },\n    ],\n    url: \"/v1/sign/typed-data\",\n    ...options,\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...options?.headers,\n    },\n  });\n};\n\n/**\n * Read Contract\n * Call read-only contract functions or batch read using multicall.\n */\nexport const readContract = <ThrowOnError extends boolean = false>(\n  options?: Options<ReadContractData, ThrowOnError>,\n) => {\n  return (options?.client ?? _heyApiClient).post<\n    ReadContractResponse,\n    unknown,\n    ThrowOnError\n  >({\n    security: [\n      {\n        name: \"x-secret-key\",\n        type: \"apiKey\",\n      },\n    ],\n    url: \"/v1/read/contract\",\n    ...options,\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...options?.headers,\n    },\n  });\n};\n\n/**\n * Read Native Balance\n * Fetches the native cryptocurrency balance (e.g., ETH, MATIC) for a given address on a specific chain.\n */\nexport const getNativeBalance = <ThrowOnError extends boolean = false>(\n  options?: Options<GetNativeBalanceData, ThrowOnError>,\n) => {\n  return (options?.client ?? _heyApiClient).post<\n    GetNativeBalanceResponse,\n    unknown,\n    ThrowOnError\n  >({\n    security: [\n      {\n        name: \"x-secret-key\",\n        type: \"apiKey\",\n      },\n    ],\n    url: \"/v1/read/balance\",\n    ...options,\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...options?.headers,\n    },\n  });\n};\n\n/**\n * Encode Function Data\n * Encode a contract call into transaction parameters (to, data, value).\n */\nexport const encodeFunctionData = <ThrowOnError extends boolean = false>(\n  options?: Options<EncodeFunctionDataData, ThrowOnError>,\n) => {\n  return (options?.client ?? _heyApiClient).post<\n    EncodeFunctionDataResponse,\n    unknown,\n    ThrowOnError\n  >({\n    security: [\n      {\n        name: \"x-secret-key\",\n        type: \"apiKey\",\n      },\n    ],\n    url: \"/v1/encode/contract\",\n    ...options,\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...options?.headers,\n    },\n  });\n};\n\n/**\n * Search Transactions\n * Advanced search for transactions with complex nested filters\n */\nexport const searchTransactions = <ThrowOnError extends boolean = false>(\n  options?: Options<SearchTransactionsData, ThrowOnError>,\n) => {\n  return (options?.client ?? _heyApiClient).post<\n    SearchTransactionsResponse,\n    unknown,\n    ThrowOnError\n  >({\n    security: [\n      {\n        name: \"x-secret-key\",\n        type: \"apiKey\",\n      },\n    ],\n    url: \"/v1/transactions/search\",\n    ...options,\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...options?.headers,\n    },\n  });\n};\n\n/**\n * Transaction Analytics\n * Get transaction count analytics over time with filtering\n */\nexport const getTransactionAnalytics = <ThrowOnError extends boolean = false>(\n  options?: Options<GetTransactionAnalyticsData, ThrowOnError>,\n) => {\n  return (options?.client ?? _heyApiClient).post<\n    GetTransactionAnalyticsResponse,\n    unknown,\n    ThrowOnError\n  >({\n    security: [\n      {\n        name: \"x-secret-key\",\n        type: \"apiKey\",\n      },\n    ],\n    url: \"/v1/transactions/analytics\",\n    ...options,\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...options?.headers,\n    },\n  });\n};\n\n/**\n * Transaction Analytics Summary\n * Get a summary (total count and total gas calculation) for transactions within a time range, supporting complex nested filters.\n */\nexport const getTransactionAnalyticsSummary = <\n  ThrowOnError extends boolean = false,\n>(\n  options?: Options<GetTransactionAnalyticsSummaryData, ThrowOnError>,\n) => {\n  return (options?.client ?? _heyApiClient).post<\n    GetTransactionAnalyticsSummaryResponse,\n    unknown,\n    ThrowOnError\n  >({\n    security: [\n      {\n        name: \"x-secret-key\",\n        type: \"apiKey\",\n      },\n    ],\n    url: \"/v1/transactions/analytics-summary\",\n    ...options,\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...options?.headers,\n    },\n  });\n};\n", "import { searchTransactions } from \"@thirdweb-dev/engine\";\nimport type { Chain } from \"../chains/types.js\";\nimport { getCached<PERSON>hain } from \"../chains/utils.js\";\nimport type { ThirdwebClient } from \"../client/client.js\";\nimport type { WaitForReceiptOptions } from \"../transaction/actions/wait-for-tx-receipt.js\";\nimport { getThirdwebBaseUrl } from \"../utils/domains.js\";\nimport type { Hex } from \"../utils/encoding/hex.js\";\nimport { getClientFetch } from \"../utils/fetch.js\";\nimport { stringify } from \"../utils/json.js\";\nimport type { Prettify } from \"../utils/type-utils.js\";\n\nexport type RevertData = {\n  errorName: string;\n  errorArgs: Record<string, unknown>;\n};\n\ntype ExecutionResult4337Serialized =\n  | {\n      status: \"QUEUED\";\n    }\n  | {\n      status: \"FAILED\";\n      error: string;\n    }\n  | {\n      status: \"SUBMITTED\";\n      monitoringStatus: \"WILL_MONITOR\" | \"CANNOT_MONITOR\";\n      userOpHash: string;\n    }\n  | ({\n      status: \"CONFIRMED\";\n      userOpHash: Hex;\n      transactionHash: Hex;\n      actualGasCost: string;\n      actualGasUsed: string;\n      nonce: string;\n    } & (\n      | {\n          onchainStatus: \"SUCCESS\";\n        }\n      | {\n          onchainStatus: \"REVERTED\";\n          revertData?: RevertData;\n        }\n    ));\n\nexport type ExecutionResult = Prettify<\n  ExecutionResult4337Serialized & {\n    chain: Chain;\n    from: string | undefined;\n    id: string;\n    createdAt: string;\n    confirmedAt: string | null;\n    cancelledAt: string | null;\n  }\n>;\n\n/**\n * Get the execution status of a transaction.\n * @param args - The arguments for the transaction.\n * @param args.client - The thirdweb client to use.\n * @param args.transactionId - The id of the transaction to get the status of.\n * @engine\n * @example\n * ```ts\n * import { Engine } from \"thirdweb\";\n *\n * const executionResult = await Engine.getTransactionStatus({\n *   client,\n *   transactionId,\n * });\n * console.log(executionResult.status);\n * ```\n */\nexport async function getTransactionStatus(args: {\n  client: ThirdwebClient;\n  transactionId: string;\n}): Promise<ExecutionResult> {\n  const { client, transactionId } = args;\n  const searchResult = await searchTransactions({\n    baseUrl: getThirdwebBaseUrl(\"engineCloud\"),\n    fetch: getClientFetch(client),\n    body: {\n      filters: [\n        {\n          field: \"id\",\n          values: [transactionId],\n          operation: \"OR\",\n        },\n      ],\n    },\n  });\n\n  if (searchResult.error) {\n    throw new Error(\n      `Error searching for transaction ${transactionId}: ${stringify(\n        searchResult.error,\n      )}`,\n    );\n  }\n\n  const data = searchResult.data?.result?.transactions?.[0];\n\n  if (!data) {\n    throw new Error(`Transaction ${transactionId} not found`);\n  }\n\n  const executionResult = data.executionResult as ExecutionResult4337Serialized;\n  return {\n    ...executionResult,\n    createdAt: data.createdAt,\n    confirmedAt: data.confirmedAt,\n    cancelledAt: data.cancelledAt,\n    chain: getCachedChain(Number(data.chainId)),\n    from: data.from ?? undefined,\n    id: data.id,\n  };\n}\n\n/**\n * Wait for a transaction to be submitted onchain and return the transaction hash.\n * @param args - The arguments for the transaction.\n * @param args.client - The thirdweb client to use.\n * @param args.transactionId - The id of the transaction to wait for.\n * @param args.timeoutInSeconds - The timeout in seconds.\n * @engine\n * @example\n * ```ts\n * import { Engine } from \"thirdweb\";\n *\n * const { transactionHash } = await Engine.waitForTransactionHash({\n *   client,\n *   transactionId, // the transaction id returned from enqueueTransaction\n * });\n * ```\n */\nexport async function waitForTransactionHash(args: {\n  client: ThirdwebClient;\n  transactionId: string;\n  timeoutInSeconds?: number;\n}): Promise<WaitForReceiptOptions> {\n  const startTime = Date.now();\n  const TIMEOUT_IN_MS = args.timeoutInSeconds\n    ? args.timeoutInSeconds * 1000\n    : 5 * 60 * 1000; // 5 minutes in milliseconds\n\n  while (Date.now() - startTime < TIMEOUT_IN_MS) {\n    const executionResult = await getTransactionStatus(args);\n    const status = executionResult.status;\n\n    switch (status) {\n      case \"FAILED\": {\n        throw new Error(\n          `Transaction failed: ${executionResult.error || \"Unknown error\"}`,\n        );\n      }\n      case \"CONFIRMED\": {\n        const onchainStatus =\n          executionResult && \"onchainStatus\" in executionResult\n            ? executionResult.onchainStatus\n            : null;\n        if (onchainStatus === \"REVERTED\") {\n          const revertData =\n            \"revertData\" in executionResult\n              ? executionResult.revertData\n              : undefined;\n          throw new Error(\n            `Transaction reverted: ${revertData?.errorName || \"\"} ${revertData?.errorArgs ? stringify(revertData.errorArgs) : \"\"}`,\n          );\n        }\n        return {\n          transactionHash: executionResult.transactionHash as Hex,\n          client: args.client,\n          chain: executionResult.chain,\n        };\n      }\n      default: {\n        // wait for the transaction to be confirmed\n        await new Promise((resolve) => setTimeout(resolve, 1000));\n      }\n    }\n  }\n  throw new Error(\n    `Transaction timed out after ${TIMEOUT_IN_MS / 1000} seconds`,\n  );\n}\n", "import {\n  type AaExecutionOptions,\n  type AaZksyncExecutionOptions,\n  sendTransaction,\n  signMessage,\n  signTypedData,\n} from \"@thirdweb-dev/engine\";\nimport type { Chain } from \"../chains/types.js\";\nimport type { ThirdwebClient } from \"../client/client.js\";\nimport { encode } from \"../transaction/actions/encode.js\";\nimport { toSerializableTransaction } from \"../transaction/actions/to-serializable-transaction.js\";\nimport type { PreparedTransaction } from \"../transaction/prepare-transaction.js\";\nimport { getThirdwebBaseUrl } from \"../utils/domains.js\";\nimport { type Hex, toHex } from \"../utils/encoding/hex.js\";\nimport { getClientFetch } from \"../utils/fetch.js\";\nimport { stringify } from \"../utils/json.js\";\nimport { resolvePromisedValue } from \"../utils/promise/resolve-promised-value.js\";\nimport type {\n  Account,\n  SendTransactionOption,\n} from \"../wallets/interfaces/wallet.js\";\nimport { waitForTransactionHash } from \"./get-status.js\";\n\n/**\n * Options for creating an server wallet.\n */\nexport type ServerWalletOptions = {\n  /**\n   * The thirdweb client to use for authentication to thirdweb services.\n   */\n  client: ThirdwebClient;\n  /**\n   * The vault access token to use your server wallet.\n   */\n  vaultAccessToken: string;\n  /**\n   * The server wallet address to use for sending transactions inside engine.\n   */\n  address: string;\n  /**\n   * The chain to use for signing messages and typed data (smart server wallet only).\n   */\n  chain?: Chain;\n  /**\n   * Optional custom execution options to use for sending transactions and signing data.\n   */\n  executionOptions?:\n    | Omit<AaExecutionOptions, \"chainId\">\n    | Omit<AaZksyncExecutionOptions, \"chainId\">;\n};\n\nexport type ServerWallet = Account & {\n  enqueueTransaction: (args: {\n    transaction: PreparedTransaction;\n    simulate?: boolean;\n  }) => Promise<{ transactionId: string }>;\n};\n\n/**\n * Create a server wallet for sending transactions and signing messages via engine (v3+).\n * @param options - The server wallet options.\n * @returns An account object that can be used to send transactions and sign messages.\n * @engine\n * @example\n * ### Creating a server wallet\n * ```ts\n * import { Engine } from \"thirdweb\";\n *\n * const client = createThirdwebClient({\n *   secretKey: \"<your-project-secret-key>\",\n * });\n *\n * const myServerWallet = Engine.serverWallet({\n *   client,\n *   address: \"<your-server-wallet-address>\",\n *   vaultAccessToken: \"<your-vault-access-token>\",\n * });\n * ```\n *\n * ### Sending a transaction\n * ```ts\n * // prepare the transaction\n * const transaction = claimTo({\n *   contract,\n *   to: \"0x...\",\n *   quantity: 1n,\n * });\n *\n * // enqueue the transaction\n * const { transactionId } = await myServerWallet.enqueueTransaction({\n *   transaction,\n * });\n * ```\n *\n * ### Polling for the transaction to be submitted onchain\n * ```ts\n * // optionally poll for the transaction to be submitted onchain\n * const { transactionHash } = await Engine.waitForTransactionHash({\n *   client,\n *   transactionId,\n * });\n * console.log(\"Transaction sent:\", transactionHash);\n * ```\n *\n * ### Getting the execution status of a transaction\n * ```ts\n * const executionResult = await Engine.getTransactionStatus({\n *   client,\n *   transactionId,\n * });\n * console.log(\"Transaction status:\", executionResult.status);\n * ```\n */\nexport function serverWallet(options: ServerWalletOptions): ServerWallet {\n  const { client, vaultAccessToken, address, chain, executionOptions } =\n    options;\n  const headers: HeadersInit = {\n    \"x-vault-access-token\": vaultAccessToken,\n  };\n\n  const getExecutionOptions = (chainId: number) => {\n    return executionOptions\n      ? {\n          ...executionOptions,\n          chainId: chainId.toString(),\n        }\n      : {\n          from: address,\n          chainId: chainId.toString(),\n        };\n  };\n\n  const enqueueTx = async (transaction: SendTransactionOption) => {\n    const body = {\n      executionOptions: getExecutionOptions(transaction.chainId),\n      params: [\n        {\n          to: transaction.to ?? undefined,\n          data: transaction.data,\n          value: transaction.value?.toString(),\n        },\n      ],\n    };\n\n    const result = await sendTransaction({\n      baseUrl: getThirdwebBaseUrl(\"engineCloud\"),\n      fetch: getClientFetch(client),\n      headers,\n      body,\n    });\n\n    if (result.error) {\n      throw new Error(`Error sending transaction: ${result.error}`);\n    }\n\n    const data = result.data?.result;\n    if (!data) {\n      throw new Error(\"No data returned from engine\");\n    }\n    const transactionId = data.transactions?.[0]?.id;\n    if (!transactionId) {\n      throw new Error(\"No transactionId returned from engine\");\n    }\n    return transactionId;\n  };\n\n  return {\n    address,\n    enqueueTransaction: async (args: {\n      transaction: PreparedTransaction;\n      simulate?: boolean;\n    }) => {\n      let serializedTransaction: SendTransactionOption;\n      if (args.simulate) {\n        serializedTransaction = await toSerializableTransaction({\n          transaction: args.transaction,\n        });\n      } else {\n        const [to, data, value] = await Promise.all([\n          args.transaction.to\n            ? resolvePromisedValue(args.transaction.to)\n            : null,\n          encode(args.transaction),\n          args.transaction.value\n            ? resolvePromisedValue(args.transaction.value)\n            : null,\n        ]);\n        serializedTransaction = {\n          chainId: args.transaction.chain.id,\n          data,\n          to: to ?? undefined,\n          value: value ?? undefined,\n        };\n      }\n      const transactionId = await enqueueTx(serializedTransaction);\n      return { transactionId };\n    },\n    sendTransaction: async (transaction: SendTransactionOption) => {\n      const transactionId = await enqueueTx(transaction);\n      return waitForTransactionHash({\n        client,\n        transactionId,\n      });\n    },\n    signMessage: async (data) => {\n      const { message, chainId } = data;\n      let engineMessage: string | Hex;\n      let isBytes = false;\n      if (typeof message === \"string\") {\n        engineMessage = message;\n      } else {\n        engineMessage = toHex(message.raw);\n        isBytes = true;\n      }\n\n      const signingChainId = chainId || chain?.id;\n      if (!signingChainId) {\n        throw new Error(\"Chain ID is required for signing messages\");\n      }\n\n      const signResult = await signMessage({\n        baseUrl: getThirdwebBaseUrl(\"engineCloud\"),\n        fetch: getClientFetch(client),\n        headers,\n        body: {\n          executionOptions: getExecutionOptions(signingChainId),\n          params: [\n            {\n              message: engineMessage,\n              messageFormat: isBytes ? \"hex\" : \"text\",\n            },\n          ],\n        },\n      });\n\n      if (signResult.error) {\n        throw new Error(\n          `Error signing message: ${stringify(signResult.error)}`,\n        );\n      }\n\n      const signatureResult = signResult.data?.result.results[0];\n      if (signatureResult?.success) {\n        return signatureResult.result.signature as Hex;\n      }\n\n      throw new Error(\n        `Failed to sign message: ${signatureResult?.error?.message || \"Unknown error\"}`,\n      );\n    },\n    signTypedData: async (typedData) => {\n      const signingChainId = chain?.id;\n      if (!signingChainId) {\n        throw new Error(\"Chain ID is required for signing messages\");\n      }\n\n      const signResult = await signTypedData({\n        baseUrl: getThirdwebBaseUrl(\"engineCloud\"),\n        fetch: getClientFetch(client),\n        headers,\n        body: {\n          executionOptions: getExecutionOptions(signingChainId),\n          // biome-ignore lint/suspicious/noExplicitAny: TODO: fix ts / hey-api type clash\n          params: [typedData as any],\n        },\n      });\n\n      if (signResult.error) {\n        throw new Error(\n          `Error signing message: ${stringify(signResult.error)}`,\n        );\n      }\n\n      const signatureResult = signResult.data?.result.results[0];\n      if (signatureResult?.success) {\n        return signatureResult.result.signature as Hex;\n      }\n\n      throw new Error(\n        `Failed to sign message: ${signatureResult?.error?.message || \"Unknown error\"}`,\n      );\n    },\n  };\n}\n", "import type { <PERSON><PERSON>, AbiFunction } from \"abitype\";\nimport { parseAbiItem } from \"abitype\";\nimport { resolveContractAbi } from \"../contract/actions/resolve-abi.js\";\nimport type { ThirdwebContract } from \"../contract/contract.js\";\n\n/**\n * Resolves and returns the ABI function with the specified method name.\n * Throws an error if the function is not found in the ABI.\n * @template abiFn - The type of the ABI function.\n * @param method - The name of the method to resolve.\n * @returns The resolved ABI function.\n * @throws Error if the function is not found in the ABI.\n * @example\n * ```ts\n * import { resolveMethod, prepareContractCall } from \"thirdweb\";\n * const tx = prepareContractCall({\n *  contract,\n *  // automatically resolves the necessary abi to encode the transaction\n *  method: resolveMethod(\"transfer\"),\n *  // however there is no type completion for params in this case (as the resolution is async and happens at runtime)\n *  params: [to, value],\n * });\n * ```\n * @contract\n */\nexport function resolveMethod<\n  abiFn extends AbiFunction,\n  T<PERSON><PERSON> extends Abi = Abi,\n>(method: string) {\n  return async (contract: ThirdwebContract<TAbi>) => {\n    if (typeof method === \"string\" && method.startsWith(\"function \")) {\n      // we know it will be an abi function so we can cast it\n      return parseAbiItem(method) as AbiFunction;\n    }\n\n    const resolvedAbi = contract.abi?.length\n      ? contract.abi\n      : await resolveContractAbi<Abi>(contract);\n    // we try to find the abiFunction in the abi\n    const abiFunction = resolvedAbi.find((item) => {\n      // if the item is not a function we can ignore it\n      if (item.type !== \"function\") {\n        return false;\n      }\n      // if the item is a function we can compare the name\n      return item.name === method;\n    }) as abiFn | undefined;\n\n    if (!abiFunction) {\n      throw new Error(`could not find function with name \"${method}\" in abi`);\n    }\n    return abiFunction;\n  };\n}\n", "import type * as ox__Signature from \"ox/Signature\";\nimport * as ox__TypedData from \"ox/TypedData\";\nimport type { Chain } from \"../chains/types.js\";\nimport type { ThirdwebClient } from \"../client/client.js\";\nimport type { Hex } from \"../utils/encoding/hex.js\";\nimport type { HashTypedDataParams } from \"../utils/hashing/hashTypedData.js\";\nimport { type VerifyHashParams, verifyHash } from \"./verify-hash.js\";\n\nexport type VerifyTypedDataParams<\n  typedData extends\n    | ox__TypedData.TypedData\n    | Record<string, unknown> = ox__TypedData.TypedData,\n  primaryType extends keyof typedData | \"EIP712Domain\" = keyof typedData,\n> = Omit<VerifyHashParams, \"hash\"> &\n  ox__TypedData.Definition<typedData, primaryType> & {\n    address: string;\n    signature: string | Uint8Array | ox__Signature.Signature;\n    client: ThirdwebClient;\n    chain: Chain;\n    accountFactory?: {\n      address: string;\n      verificationCalldata: Hex;\n    };\n  };\n\n/**\n * Verify am [EIP-712](https://eips.ethereum.org/EIPS/eip-712) typed data signature. This function is interoperable with all wallet types (smart accounts or EOAs).\n *\n * @param {string} options.address The address that signed the typed data\n * @param {string | Uint8Array | Signature} options.signature The signature that was signed\n * @param {ThirdwebClient} options.client The Thirdweb client\n * @param {Chain} options.chain The chain that the address is on. For an EOA, this can be any chain.\n * @param {string} [options.accountFactory.address] The address of the account factory that created the account if using a smart account with a custom account factory\n * @param {Hex} [options.accountFactory.verificationCalldata] The calldata that was used to create the account if using a smart account with a custom account factory\n * @param {typeof VerifyTypedDataParams.message} options.message The EIP-712 message that was signed.\n * @param {typeof VerifyTypedDataParams.domain} options.domain The EIP-712 domain that was signed.\n * @param {typeof VerifyTypedDataParams.primaryType} options.primaryType The EIP-712 primary type that was signed.\n * @param {typeof VerifyTypedDataParams.types} options.types The EIP-712 types that were signed.\n *\n * @returns {Promise<boolean>} A promise that resolves to `true` if the signature is valid, or `false` otherwise.\n *\n * @example\n * ```ts\n * import { verifyTypedData } from \"thirdweb/utils\";\n * const isValid = await verifyTypedData({\n *   address: \"0x...\",\n *   signature: \"0x...\",\n *   client,\n *   chain,\n *   domain: {\n      name: \"Ether Mail\",\n      version: \"1\",\n      chainId: 1,\n      verifyingContract: \"******************************************\",\n    },\n *   primaryType: \"Mail\",\n *   types: {\n      Person: [\n        { name: \"name\", type: \"string\" },\n        { name: \"wallet\", type: \"address\" },\n      ],\n      Mail: [\n        { name: \"from\", type: \"Person\" },\n        { name: \"to\", type: \"Person\" },\n        { name: \"contents\", type: \"string\" },\n      ],\n    },\n    message: {\n      from: {\n        name: \"Cow\",\n        wallet: \"******************************************\",\n      },\n      to: {\n        name: \"Bob\",\n        wallet: \"******************************************\",\n      },\n      contents: \"Hello, Bob!\",\n    },\n * });\n * ```\n *\n * @auth\n */\nexport async function verifyTypedData<\n  typedData extends ox__TypedData.TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | \"EIP712Domain\",\n>({\n  address,\n  signature,\n  client,\n  chain,\n  accountFactory,\n  message,\n  domain,\n  primaryType,\n  types,\n}: VerifyTypedDataParams<typedData, primaryType>): Promise<boolean> {\n  const messageHash = ox__TypedData.getSignPayload({\n    message,\n    domain,\n    primaryType,\n    types,\n  } as HashTypedDataParams);\n  return verifyHash({\n    hash: messageHash,\n    signature,\n    address,\n    chain,\n    client,\n    accountFactory,\n  });\n}\n", "import type * as ox__Authorization from \"ox/Authorization\";\nimport type { Address } from \"../../../utils/address.js\";\nimport type { Account } from \"../../../wallets/interfaces/wallet.js\";\n\n/**\n * An EIP-7702 authorization object fully prepared and ready for signing.\n *\n * @beta\n * @transaction\n */\nexport type AuthorizationRequest = {\n  address: Address;\n  chainId: number;\n  nonce: bigint;\n};\n\n/**\n * Represents a signed EIP-7702 authorization object.\n *\n * @beta\n * @transaction\n */\nexport type SignedAuthorization = ox__Authorization.ListSigned[number];\n\n/**\n * Sign the given EIP-7702 authorization object.\n * @param options - The options for `signAuthorization`\n * Refer to the type [`SignAuthorizationOptions`](https://portal.thirdweb.com/references/typescript/v5/SignAuthorizationOptions)\n * @returns The signed authorization object\n *\n * ```ts\n * import { signAuthorization } from \"thirdweb\";\n *\n * const authorization = await signAuthorization({\n *     request: {\n *         address: \"0x...\",\n *         chainId: 911867,\n *         nonce: 100n,\n *     },\n *     account: myAccount,\n * });\n * ```\n *\n * @beta\n * @transaction\n */\nexport async function signAuthorization(options: {\n  account: Account;\n  request: AuthorizationRequest;\n}): Promise<SignedAuthorization> {\n  const { account, request } = options;\n  if (typeof account.signAuthorization === \"undefined\") {\n    throw new Error(\n      \"This account type does not yet support signing EIP-7702 authorizations\",\n    );\n  }\n  return account.signAuthorization(request);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BM,SAAUA,QACd,OACA,IAAQ;AAER,QAAM,QAAQ,OACZ,MAAM,OAAO,EAAE,QAAQ,MAAK,CAAE,IAAI,gBAAgB,KAAK,IAAI,KAAK;AAElE,MAAI,OAAO,SAAS;AAClB,WAAO;EACT;AACA,SAAO,gBAAgB,KAAK;AAC9B;;;ACjCA,IAAM,QAAQ,IAAI,OAAe,IAAI;AAO/B,SAAU,6BAA6B,WAAiB;AAC5D,MAAI,MAAM,IAAI,SAAS,GAAG;AACxB,WAAO,MAAM,IAAI,SAAS;EAC5B;AAEA,QAAM,MAAMC,QAAO,cAAc,SAAS,CAAC,EAAE,MAAM,GAAG,EAAE;AACxD,QAAM,IAAI,WAAW,GAAG;AACxB,SAAO;AACT;;;ACyFM,SAAU,qBACd,SAAoC;AAEpC,QAAM,EAAE,UAAU,WAAW,GAAG,KAAI,IAAK;AAEzC,MAAI,eAAmC;AAEvC,MAAI,WAAW;AACb,QAAI,MAAM,SAAS,GAAG;AAEpB,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,sDAAsD;MACxE;IACF,OAAO;AAEL,qBAAe,YAAY,6BAA6B,SAAS;IACnE;EACF;AAGA,MAAI,CAAC,cAAc;AACjB,UAAM,IAAI,MAAM,wCAAwC;EAC1D;AAEA,SAAO;IACL,GAAG;IACH,UAAU;IACV;;AAEJ;;;ACxGA,eAAsB,mBAGpB,SACA,QAAkD;AAElD,QAAM,sBAAsB,OAAO,uBAAuB;AAE1D,QAAM,QAAQ,MAAM,QAAQ;IAC1B,QAAQ;IACR,QAAQ,CAAC,OAAO,WAAW,mBAAmB;GAC/C;AACD,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM,iBAAiB;EACnC;AACA,SAAO,YAAY,KAAK;AAI1B;;;ACxBA,eAAsB,yBACpB,SACA,QAAsC;AAEtC,QAAM,UAAU,MAAM,QAAQ;IAC5B,QAAQ;IACR,QAAQ,CAAC,OAAO,IAAI;GACrB;AAED,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,wBAAwB;EAC1C;AAEA,SAAO,kBAAkB,OAAO;AAClC;;;AC1CA;;;;;;;;ACuBO,IAAM,SAAS,EACpB,EAA4B;EAC1B,SAAS;CACV,CAAC;;;ACuDG,IAAMC,mBAAkB,CAC7B,YACE;AACF,WAAQ,mCAAS,WAAU,QAAe,KAIxC;IACA,UAAU;MACR;QACE,MAAM;QACN,MAAM;;;IAGV,KAAK;IACL,GAAG;IACH,SAAS;MACP,gBAAgB;MAChB,GAAG,mCAAS;;GAEf;AACH;AAiCO,IAAM,cAAc,CACzB,YACE;AACF,WAAQ,mCAAS,WAAU,QAAe,KAIxC;IACA,UAAU;MACR;QACE,MAAM;QACN,MAAM;;;IAGV,KAAK;IACL,GAAG;IACH,SAAS;MACP,gBAAgB;MAChB,GAAG,mCAAS;;GAEf;AACH;AAMO,IAAM,gBAAgB,CAC3B,YACE;AACF,WAAQ,mCAAS,WAAU,QAAe,KAIxC;IACA,UAAU;MACR;QACE,MAAM;QACN,MAAM;;;IAGV,KAAK;IACL,GAAG;IACH,SAAS;MACP,gBAAgB;MAChB,GAAG,mCAAS;;GAEf;AACH;AAuFO,IAAM,qBAAqB,CAChC,YACE;AACF,WAAQ,mCAAS,WAAU,QAAe,KAIxC;IACA,UAAU;MACR;QACE,MAAM;QACN,MAAM;;;IAGV,KAAK;IACL,GAAG;IACH,SAAS;MACP,gBAAgB;MAChB,GAAG,mCAAS;;GAEf;AACH;;;ACzNA,eAAsB,qBAAqB,MAG1C;AA7ED;AA8EE,QAAM,EAAE,QAAAC,SAAQ,cAAa,IAAK;AAClC,QAAM,eAAe,MAAM,mBAAmB;IAC5C,SAAS,mBAAmB,aAAa;IACzC,OAAO,eAAeA,OAAM;IAC5B,MAAM;MACJ,SAAS;QACP;UACE,OAAO;UACP,QAAQ,CAAC,aAAa;UACtB,WAAW;;;;GAIlB;AAED,MAAI,aAAa,OAAO;AACtB,UAAM,IAAI,MACR,mCAAmC,aAAa,KAAK,UACnD,aAAa,KAAK,CACnB,EAAE;EAEP;AAEA,QAAM,QAAO,8BAAa,SAAb,mBAAmB,WAAnB,mBAA2B,iBAA3B,mBAA0C;AAEvD,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,eAAe,aAAa,YAAY;EAC1D;AAEA,QAAM,kBAAkB,KAAK;AAC7B,SAAO;IACL,GAAG;IACH,WAAW,KAAK;IAChB,aAAa,KAAK;IAClB,aAAa,KAAK;IAClB,OAAO,eAAe,OAAO,KAAK,OAAO,CAAC;IAC1C,MAAM,KAAK,QAAQ;IACnB,IAAI,KAAK;;AAEb;AAmBA,eAAsB,uBAAuB,MAI5C;AACC,QAAM,YAAY,KAAK,IAAG;AAC1B,QAAM,gBAAgB,KAAK,mBACvB,KAAK,mBAAmB,MACxB,IAAI,KAAK;AAEb,SAAO,KAAK,IAAG,IAAK,YAAY,eAAe;AAC7C,UAAM,kBAAkB,MAAM,qBAAqB,IAAI;AACvD,UAAM,SAAS,gBAAgB;AAE/B,YAAQ,QAAQ;MACd,KAAK,UAAU;AACb,cAAM,IAAI,MACR,uBAAuB,gBAAgB,SAAS,eAAe,EAAE;MAErE;MACA,KAAK,aAAa;AAChB,cAAM,gBACJ,mBAAmB,mBAAmB,kBAClC,gBAAgB,gBAChB;AACN,YAAI,kBAAkB,YAAY;AAChC,gBAAM,aACJ,gBAAgB,kBACZ,gBAAgB,aAChB;AACN,gBAAM,IAAI,MACR,0BAAyB,yCAAY,cAAa,EAAE,KAAI,yCAAY,aAAY,UAAU,WAAW,SAAS,IAAI,EAAE,EAAE;QAE1H;AACA,eAAO;UACL,iBAAiB,gBAAgB;UACjC,QAAQ,KAAK;UACb,OAAO,gBAAgB;;MAE3B;MACA,SAAS;AAEP,cAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,GAAI,CAAC;MAC1D;IACF;EACF;AACA,QAAM,IAAI,MACR,+BAA+B,gBAAgB,GAAI,UAAU;AAEjE;;;ACxEM,SAAU,aAAa,SAA4B;AACvD,QAAM,EAAE,QAAAC,SAAQ,kBAAkB,SAAS,OAAO,iBAAgB,IAChE;AACF,QAAM,UAAuB;IAC3B,wBAAwB;;AAG1B,QAAM,sBAAsB,CAAC,YAAmB;AAC9C,WAAO,mBACH;MACE,GAAG;MACH,SAAS,QAAQ,SAAQ;QAE3B;MACE,MAAM;MACN,SAAS,QAAQ,SAAQ;;EAEjC;AAEA,QAAM,YAAY,OAAO,gBAAsC;AApIjE;AAqII,UAAM,OAAO;MACX,kBAAkB,oBAAoB,YAAY,OAAO;MACzD,QAAQ;QACN;UACE,IAAI,YAAY,MAAM;UACtB,MAAM,YAAY;UAClB,QAAO,iBAAY,UAAZ,mBAAmB;;;;AAKhC,UAAM,SAAS,MAAMC,iBAAgB;MACnC,SAAS,mBAAmB,aAAa;MACzC,OAAO,eAAeD,OAAM;MAC5B;MACA;KACD;AAED,QAAI,OAAO,OAAO;AAChB,YAAM,IAAI,MAAM,8BAA8B,OAAO,KAAK,EAAE;IAC9D;AAEA,UAAM,QAAO,YAAO,SAAP,mBAAa;AAC1B,QAAI,CAAC,MAAM;AACT,YAAM,IAAI,MAAM,8BAA8B;IAChD;AACA,UAAM,iBAAgB,gBAAK,iBAAL,mBAAoB,OAApB,mBAAwB;AAC9C,QAAI,CAAC,eAAe;AAClB,YAAM,IAAI,MAAM,uCAAuC;IACzD;AACA,WAAO;EACT;AAEA,SAAO;IACL;IACA,oBAAoB,OAAO,SAGtB;AACH,UAAI;AACJ,UAAI,KAAK,UAAU;AACjB,gCAAwB,MAAM,0BAA0B;UACtD,aAAa,KAAK;SACnB;MACH,OAAO;AACL,cAAM,CAAC,IAAI,MAAM,KAAK,IAAI,MAAM,QAAQ,IAAI;UAC1C,KAAK,YAAY,KACb,qBAAqB,KAAK,YAAY,EAAE,IACxC;UACJ,OAAO,KAAK,WAAW;UACvB,KAAK,YAAY,QACb,qBAAqB,KAAK,YAAY,KAAK,IAC3C;SACL;AACD,gCAAwB;UACtB,SAAS,KAAK,YAAY,MAAM;UAChC;UACA,IAAI,MAAM;UACV,OAAO,SAAS;;MAEpB;AACA,YAAM,gBAAgB,MAAM,UAAU,qBAAqB;AAC3D,aAAO,EAAE,cAAa;IACxB;IACA,iBAAiB,OAAO,gBAAsC;AAC5D,YAAM,gBAAgB,MAAM,UAAU,WAAW;AACjD,aAAO,uBAAuB;QAC5B,QAAAA;QACA;OACD;IACH;IACA,aAAa,OAAO,SAAQ;AA5MhC;AA6MM,YAAM,EAAE,SAAS,QAAO,IAAK;AAC7B,UAAI;AACJ,UAAI,UAAU;AACd,UAAI,OAAO,YAAY,UAAU;AAC/B,wBAAgB;MAClB,OAAO;AACL,wBAAgB,MAAM,QAAQ,GAAG;AACjC,kBAAU;MACZ;AAEA,YAAM,iBAAiB,YAAW,+BAAO;AACzC,UAAI,CAAC,gBAAgB;AACnB,cAAM,IAAI,MAAM,2CAA2C;MAC7D;AAEA,YAAM,aAAa,MAAM,YAAY;QACnC,SAAS,mBAAmB,aAAa;QACzC,OAAO,eAAeA,OAAM;QAC5B;QACA,MAAM;UACJ,kBAAkB,oBAAoB,cAAc;UACpD,QAAQ;YACN;cACE,SAAS;cACT,eAAe,UAAU,QAAQ;;;;OAIxC;AAED,UAAI,WAAW,OAAO;AACpB,cAAM,IAAI,MACR,0BAA0B,UAAU,WAAW,KAAK,CAAC,EAAE;MAE3D;AAEA,YAAM,mBAAkB,gBAAW,SAAX,mBAAiB,OAAO,QAAQ;AACxD,UAAI,mDAAiB,SAAS;AAC5B,eAAO,gBAAgB,OAAO;MAChC;AAEA,YAAM,IAAI,MACR,6BAA2B,wDAAiB,UAAjB,mBAAwB,YAAW,eAAe,EAAE;IAEnF;IACA,eAAe,OAAO,cAAa;AA1PvC;AA2PM,YAAM,iBAAiB,+BAAO;AAC9B,UAAI,CAAC,gBAAgB;AACnB,cAAM,IAAI,MAAM,2CAA2C;MAC7D;AAEA,YAAM,aAAa,MAAM,cAAc;QACrC,SAAS,mBAAmB,aAAa;QACzC,OAAO,eAAeA,OAAM;QAC5B;QACA,MAAM;UACJ,kBAAkB,oBAAoB,cAAc;;UAEpD,QAAQ,CAAC,SAAgB;;OAE5B;AAED,UAAI,WAAW,OAAO;AACpB,cAAM,IAAI,MACR,0BAA0B,UAAU,WAAW,KAAK,CAAC,EAAE;MAE3D;AAEA,YAAM,mBAAkB,gBAAW,SAAX,mBAAiB,OAAO,QAAQ;AACxD,UAAI,mDAAiB,SAAS;AAC5B,eAAO,gBAAgB,OAAO;MAChC;AAEA,YAAM,IAAI,MACR,6BAA2B,wDAAiB,UAAjB,mBAAwB,YAAW,eAAe,EAAE;IAEnF;;AAEJ;;;AClQM,SAAU,cAGd,QAAc;AACd,SAAO,OAAO,aAAoC;AA5BpD;AA6BI,QAAI,OAAO,WAAW,YAAY,OAAO,WAAW,WAAW,GAAG;AAEhE,aAAO,aAAa,MAAM;IAC5B;AAEA,UAAM,gBAAc,cAAS,QAAT,mBAAc,UAC9B,SAAS,MACT,MAAM,mBAAwB,QAAQ;AAE1C,UAAM,cAAc,YAAY,KAAK,CAAC,SAAQ;AAE5C,UAAI,KAAK,SAAS,YAAY;AAC5B,eAAO;MACT;AAEA,aAAO,KAAK,SAAS;IACvB,CAAC;AAED,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,sCAAsC,MAAM,UAAU;IACxE;AACA,WAAO;EACT;AACF;;;AC8BA,eAAsB,gBAGpB,EACA,SACA,WACA,QAAAE,SACA,OACA,gBACA,SACA,QACA,aACA,MAAK,GACyC;AAC9C,QAAM,cAA4B,eAAe;IAC/C;IACA;IACA;IACA;GACsB;AACxB,SAAO,WAAW;IAChB,MAAM;IACN;IACA;IACA;IACA,QAAAA;IACA;GACD;AACH;;;ACjEA,eAAsB,kBAAkB,SAGvC;AACC,QAAM,EAAE,SAAS,QAAO,IAAK;AAC7B,MAAI,OAAO,QAAQ,sBAAsB,aAAa;AACpD,UAAM,IAAI,MACR,wEAAwE;EAE5E;AACA,SAAO,QAAQ,kBAAkB,OAAO;AAC1C;", "names": ["sha256", "sha256", "sendTransaction", "client", "client", "sendTransaction", "client"]}