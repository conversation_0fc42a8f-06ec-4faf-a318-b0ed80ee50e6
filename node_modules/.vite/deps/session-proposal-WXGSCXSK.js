import {
  disconnectWalletConnectSession,
  getSessions,
  saveSession
} from "./chunk-RHFE2MF3.js";
import "./chunk-Q5L7DEYD.js";
import "./chunk-WUZHUB45.js";
import "./chunk-7N62H5IB.js";
import "./chunk-OJRBY574.js";
import "./chunk-TJPCO3UF.js";
import "./chunk-FUPOJN5U.js";
import "./chunk-67YIWUOQ.js";
import "./chunk-FUW7UPWG.js";
import "./chunk-IA4CMUWX.js";
import "./chunk-JK36SS76.js";
import "./chunk-HFJPNBPY.js";
import "./chunk-HGMV3JDR.js";
import "./chunk-QGXAPRFG.js";
import "./chunk-E4AMV5H3.js";
import "./chunk-WLZN2VO2.js";
import "./chunk-HKVYRBWW.js";
import "./chunk-HAADYJEF.js";
import "./chunk-LVUFVX5C.js";
import "./chunk-UEKVYVRB.js";
import "./chunk-IHMGPG6V.js";
import "./chunk-MZGV4VDW.js";
import "./chunk-RPGMYTER.js";
import "./chunk-64KT6ODC.js";
import "./chunk-EP3M7YWL.js";
import "./chunk-NTKAF5LO.js";
import "./chunk-2CIJO3V3.js";
import "./chunk-E4AXWHD7.js";
import "./chunk-H7Z32RTP.js";
import "./chunk-YXD4WFHV.js";
import "./chunk-26FWGFQH.js";
import "./chunk-DESKQC7P.js";
import "./chunk-BJ63FHMG.js";
import "./chunk-4LB33PYO.js";
import "./chunk-6CMZOK3K.js";
import "./chunk-HXWRQBIO.js";
import "./chunk-5UQS4U7E.js";
import "./chunk-ZLUBC7IW.js";
import "./chunk-5Q2S2L2S.js";
import "./chunk-KQKMGIQ6.js";
import "./chunk-MTFDOOBS.js";
import "./chunk-RJUQUX6Y.js";
import "./chunk-PPP72TBL.js";
import "./chunk-3OXDSLPJ.js";
import "./chunk-OSFP2VB7.js";
import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/wallet-connect/receiver/session-proposal.js
async function onSessionProposal(options) {
  var _a, _b;
  const { wallet, walletConnectClient, event, chains, onConnect } = options;
  const account = wallet.getAccount();
  if (!account) {
    throw new Error("No account connected to provided wallet");
  }
  const origin = (_b = (_a = event.verifyContext) == null ? void 0 : _a.verified) == null ? void 0 : _b.origin;
  if (origin) {
    await disconnectExistingSessions({ origin, walletConnectClient });
  }
  const session = await acceptSessionProposal({
    account,
    walletConnectClient,
    sessionProposal: event,
    chains
  });
  await saveSession(session);
  wallet.subscribe("disconnect", () => {
    disconnectWalletConnectSession({ session, walletConnectClient });
  });
  onConnect == null ? void 0 : onConnect(session);
}
async function disconnectExistingSessions({ walletConnectClient, origin }) {
  const sessions = await getSessions();
  for (const session of sessions) {
    if (session.origin === origin) {
      await disconnectWalletConnectSession({ session, walletConnectClient });
    }
  }
}
async function acceptSessionProposal({ account, walletConnectClient, sessionProposal, chains }) {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p, _q, _r;
  if (!((_a = sessionProposal.params.requiredNamespaces) == null ? void 0 : _a.eip155) && !((_b = sessionProposal.params.optionalNamespaces) == null ? void 0 : _b.eip155)) {
    throw new Error("No EIP155 namespace found in Wallet Connect session proposal");
  }
  const namespaces = {
    chains: [
      ...Array.from(/* @__PURE__ */ new Set([
        ...((_e = (_d = (_c = sessionProposal.params.requiredNamespaces) == null ? void 0 : _c.eip155) == null ? void 0 : _d.chains) == null ? void 0 : _e.map((chain) => `${chain}:${account.address}`)) ?? [],
        ...((_h = (_g = (_f = sessionProposal.params.optionalNamespaces) == null ? void 0 : _f.eip155) == null ? void 0 : _g.chains) == null ? void 0 : _h.map((chain) => `${chain}:${account.address}`)) ?? [],
        ...(chains == null ? void 0 : chains.map((chain) => `eip155:${chain.id}:${account.address}`)) ?? []
      ]))
    ],
    methods: [
      ...((_j = (_i = sessionProposal.params.requiredNamespaces) == null ? void 0 : _i.eip155) == null ? void 0 : _j.methods) ?? [],
      ...((_l = (_k = sessionProposal.params.optionalNamespaces) == null ? void 0 : _k.eip155) == null ? void 0 : _l.methods) ?? []
    ],
    events: [
      ...((_n = (_m = sessionProposal.params.requiredNamespaces) == null ? void 0 : _m.eip155) == null ? void 0 : _n.events) ?? [],
      ...((_p = (_o = sessionProposal.params.optionalNamespaces) == null ? void 0 : _o.eip155) == null ? void 0 : _p.events) ?? []
    ]
  };
  const approval = await walletConnectClient.approve({
    id: sessionProposal.id,
    namespaces: {
      eip155: {
        accounts: namespaces.chains,
        methods: namespaces.methods,
        events: namespaces.events
      }
    }
  });
  const session = await approval.acknowledged();
  return {
    topic: session.topic,
    origin: ((_r = (_q = sessionProposal.verifyContext) == null ? void 0 : _q.verified) == null ? void 0 : _r.origin) || "Unknown origin"
  };
}
export {
  acceptSessionProposal,
  disconnectExistingSessions,
  onSessionProposal
};
//# sourceMappingURL=session-proposal-WXGSCXSK.js.map
