{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/xyz.frontier.wallet/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"xyz.frontier.wallet\",\n  name: \"Frontier\",\n  homepage: \"https://www.frontier.xyz\",\n  image_id: \"a78c4d48-32c1-4a9d-52f2-ec7ee08ce200\",\n  app: {\n    browser: \"https://www.frontier.xyz/download\",\n    ios: \"https://apps.apple.com/us/app/frontier-defi-wallet/id1482380988\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.frontierwallet&hl=en_IN&gl=US\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome:\n      \"https://chrome.google.com/webstore/detail/frontier-wallet/kppfdiipphfccemcignhifpjkapfbihd\",\n    firefox: null,\n    safari: null,\n    edge: \"https://chrome.google.com/webstore/detail/frontier-wallet/kppfdiipphfccemcignhifpjkapfbihd\",\n    opera: null,\n  },\n  rdns: \"xyz.frontier.wallet\",\n  mobile: {\n    native: \"frontier://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: \"https://www.frontier.xyz/download\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QACE;IACF,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}