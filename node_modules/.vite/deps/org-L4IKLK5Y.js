import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/org.talkapp/index.js
var wallet = {
  id: "org.talkapp",
  name: "T+ Wallet ",
  homepage: "https://www.talkapp.org/",
  image_id: "c08ff28f-5a52-4bf2-e63a-205905fd5800",
  app: {
    browser: null,
    ios: "https://apps.apple.com/hk/app/talk-%E5%8A%A0%E5%AF%86%E8%B2%A8%E5%B9%A3%E4%BA%A4%E6%98%93%E5%8F%8Aai%E8%81%8A%E5%A4%A9%E9%80%9A%E8%A8%8A%E8%BB%9F%E4%BB%B6/id1547227377",
    android: "https://play.google.com/store/apps/details?id=org.talkapp&hl=en&gl=US",
    mac: null,
    windows: null,
    linux: null,
    chrome: null,
    firefox: null,
    safari: null,
    edge: null,
    opera: null
  },
  rdns: null,
  mobile: {
    native: "talkapp://",
    universal: null
  },
  desktop: {
    native: null,
    universal: null
  }
};
export {
  wallet
};
//# sourceMappingURL=org-L4IKLK5Y.js.map
