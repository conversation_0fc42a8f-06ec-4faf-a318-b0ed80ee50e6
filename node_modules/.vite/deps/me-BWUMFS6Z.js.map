{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/me.easy/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"me.easy\",\n  name: \"EASY\",\n  homepage: \"https://easy.me\",\n  image_id: \"62feb41a-be1f-4b1c-e089-27f97c0e8d00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/easy-web3-social-wallet/id1641192503\",\n    android: \"https://play.google.com/store/apps/details?id=co.theeasy.app\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"co.theeasy.app://\",\n    universal: \"https://link.easy.me\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}