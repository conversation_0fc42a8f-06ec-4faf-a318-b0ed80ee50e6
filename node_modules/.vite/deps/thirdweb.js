import {
  eth_getStorageAt
} from "./chunk-34K3P2ZE.js";
import {
  eth_getTransactionCount
} from "./chunk-6J7GBL42.js";
import {
  J,
  w
} from "./chunk-X2AGAVBN.js";
import {
  eth_estimateGas
} from "./chunk-57ZH6LFY.js";
import {
  eth_getLogs,
  getBuyWithCryptoHistory,
  getContractEvents,
  isBaseTransactionOptions,
  simulateTransaction,
  watchContractEvents
} from "./chunk-HQXSQO5B.js";
import {
  bridge_exports,
  getBuyWithCryptoQuote,
  getBuyWithCryptoStatus,
  getBuyWithCryptoTransfer
} from "./chunk-DI4JGB77.js";
import {
  sendAndConfirmTransaction,
  sendBatchTransaction
} from "./chunk-KIS5WZSA.js";
import {
  eth_blockNumber,
  eth_getTransactionReceipt,
  waitForReceipt,
  watchBlockNumber
} from "./chunk-J7DXO4GY.js";
import "./chunk-QWTK625L.js";
import "./chunk-W4AHB6JF.js";
import "./chunk-HK6ZOTJE.js";
import {
  insight_exports
} from "./chunk-FQZ567R7.js";
import "./chunk-3YX5YEAD.js";
import "./chunk-WWY7S4YD.js";
import {
  getUser,
  signTransaction
} from "./chunk-6ZZYYFZR.js";
import {
  bytesToBigInt,
  bytesToBool,
  bytesToNumber,
  bytesToString,
  deploySmartAccount,
  fromBytes,
  verifyHash
} from "./chunk-KZFMJ5EJ.js";
import {
  serializeTransaction
} from "./chunk-KKYSBV5J.js";
import "./chunk-S37TJN6V.js";
import {
  eth_getBalance
} from "./chunk-ZYOAGPYD.js";
import {
  parseEventLogs,
  prepareEvent
} from "./chunk-W5WNM5J7.js";
import {
  sendTransaction
} from "./chunk-JK36SS76.js";
import "./chunk-HFJPNBPY.js";
import "./chunk-RDXOJSO7.js";
import "./chunk-QAKDTVBR.js";
import {
  prepareContractCall
} from "./chunk-DH7M67RK.js";
import {
  getSignPayload
} from "./chunk-LH7DZZVX.js";
import "./chunk-46GSJ545.js";
import "./chunk-3S7RRRP4.js";
import "./chunk-HGMV3JDR.js";
import {
  prepareTransaction
} from "./chunk-QGXAPRFG.js";
import {
  NATIVE_TOKEN_ADDRESS,
  ZERO_ADDRESS
} from "./chunk-YCZ3YGMG.js";
import {
  eth_getCode
} from "./chunk-S6FQMGF4.js";
import "./chunk-54TJVF2D.js";
import {
  eth_sendRawTransaction
} from "./chunk-CMXLKATA.js";
import {
  concatHex
} from "./chunk-YABCK54Q.js";
import "./chunk-T3QVMRUA.js";
import "./chunk-S2QIGEBI.js";
import "./chunk-INXYAOQP.js";
import "./chunk-DUYIIKDP.js";
import {
  eth_call,
  readContract
} from "./chunk-A4QD5K62.js";
import {
  estimateGasCost
} from "./chunk-P4DFI2FE.js";
import {
  toSerializableTransaction
} from "./chunk-E4AMV5H3.js";
import "./chunk-WLZN2VO2.js";
import {
  estimateGas,
  eth_gasPrice,
  eth_getBlockByNumber,
  eth_maxPriorityFeePerGas,
  getGasPrice,
  resolveContractAbi,
  resolvePromisedValue
} from "./chunk-HKVYRBWW.js";
import {
  fromGwei,
  toEther,
  toTokens,
  toUnits,
  toWei
} from "./chunk-HAADYJEF.js";
import {
  encode
} from "./chunk-LVUFVX5C.js";
import {
  getContract
} from "./chunk-UEKVYVRB.js";
import {
  getAddress,
  isAddress,
  keccak256
} from "./chunk-IHMGPG6V.js";
import {
  boolToBytes,
  hexToBytes,
  numberToBytes,
  stringToBytes,
  toBytes
} from "./chunk-MZGV4VDW.js";
import "./chunk-RPGMYTER.js";
import "./chunk-64KT6ODC.js";
import "./chunk-EP3M7YWL.js";
import {
  getRpcClient
} from "./chunk-NTKAF5LO.js";
import {
  stringify
} from "./chunk-2CIJO3V3.js";
import {
  boolToHex,
  fromHex,
  hexToBigInt,
  hexToBool,
  hexToNumber,
  hexToString,
  hexToUint8Array,
  isHex,
  numberToHex,
  padHex,
  stringToHex,
  toHex,
  uint8ArrayToHex
} from "./chunk-E4AXWHD7.js";
import "./chunk-H7Z32RTP.js";
import {
  formatBlock,
  formatTransaction
} from "./chunk-YXD4WFHV.js";
import "./chunk-26FWGFQH.js";
import "./chunk-DESKQC7P.js";
import {
  sha256
} from "./chunk-BJ63FHMG.js";
import "./chunk-4LB33PYO.js";
import "./chunk-6CMZOK3K.js";
import {
  parseAbiItem
} from "./chunk-HXWRQBIO.js";
import "./chunk-5UQS4U7E.js";
import "./chunk-ZLUBC7IW.js";
import "./chunk-5Q2S2L2S.js";
import "./chunk-VAV3ZUCP.js";
import {
  defineChain,
  getCachedChain
} from "./chunk-KQKMGIQ6.js";
import "./chunk-MTFDOOBS.js";
import {
  getClientFetch,
  isJWT
} from "./chunk-RJUQUX6Y.js";
import {
  LruMap
} from "./chunk-PPP72TBL.js";
import "./chunk-3OXDSLPJ.js";
import {
  getThirdwebBaseUrl
} from "./chunk-OSFP2VB7.js";
import {
  __export
} from "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/utils/hashing/sha256.js
function sha2562(value, to) {
  const bytes = sha256(isHex(value, { strict: false }) ? hexToUint8Array(value) : value);
  if (to === "bytes") {
    return bytes;
  }
  return uint8ArrayToHex(bytes);
}

// node_modules/thirdweb/dist/esm/utils/client-id.js
var cache = new LruMap(4096);
function computeClientIdFromSecretKey(secretKey) {
  if (cache.has(secretKey)) {
    return cache.get(secretKey);
  }
  const cId = sha2562(stringToBytes(secretKey)).slice(2, 34);
  cache.set(secretKey, cId);
  return cId;
}

// node_modules/thirdweb/dist/esm/client/client.js
function createThirdwebClient(options) {
  const { clientId, secretKey, ...rest } = options;
  let realClientId = clientId;
  if (secretKey) {
    if (isJWT(secretKey)) {
      if (!clientId) {
        throw new Error("clientId must be provided when using a JWT secretKey");
      }
    } else {
      realClientId = clientId ?? computeClientIdFromSecretKey(secretKey);
    }
  }
  if (!realClientId) {
    throw new Error("clientId or secretKey must be provided");
  }
  return {
    ...rest,
    clientId: realClientId,
    secretKey
  };
}

// node_modules/thirdweb/dist/esm/rpc/actions/eth_getBlockByHash.js
async function eth_getBlockByHash(request, params) {
  const includeTransactions = params.includeTransactions ?? false;
  const block = await request({
    method: "eth_getBlockByHash",
    params: [params.blockHash, includeTransactions]
  });
  if (!block) {
    throw new Error("Block not found");
  }
  return formatBlock(block);
}

// node_modules/thirdweb/dist/esm/rpc/actions/eth_getTransactionByHash.js
async function eth_getTransactionByHash(request, params) {
  const receipt = await request({
    method: "eth_getTransactionByHash",
    params: [params.hash]
  });
  if (!receipt) {
    throw new Error("Transaction not found.");
  }
  return formatTransaction(receipt);
}

// node_modules/thirdweb/dist/esm/engine/index.js
var engine_exports = {};
__export(engine_exports, {
  getTransactionStatus: () => getTransactionStatus,
  serverWallet: () => serverWallet,
  waitForTransactionHash: () => waitForTransactionHash
});

// node_modules/@thirdweb-dev/engine/dist/esm/client/client.gen.js
var client = J(w({
  baseUrl: "https://engine.thirdweb.com"
}));

// node_modules/@thirdweb-dev/engine/dist/esm/client/sdk.gen.js
var sendTransaction2 = (options) => {
  return ((options == null ? void 0 : options.client) ?? client).post({
    security: [
      {
        name: "x-secret-key",
        type: "apiKey"
      }
    ],
    url: "/v1/write/transaction",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options == null ? void 0 : options.headers
    }
  });
};
var signMessage = (options) => {
  return ((options == null ? void 0 : options.client) ?? client).post({
    security: [
      {
        name: "x-secret-key",
        type: "apiKey"
      }
    ],
    url: "/v1/sign/message",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options == null ? void 0 : options.headers
    }
  });
};
var signTypedData = (options) => {
  return ((options == null ? void 0 : options.client) ?? client).post({
    security: [
      {
        name: "x-secret-key",
        type: "apiKey"
      }
    ],
    url: "/v1/sign/typed-data",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options == null ? void 0 : options.headers
    }
  });
};
var searchTransactions = (options) => {
  return ((options == null ? void 0 : options.client) ?? client).post({
    security: [
      {
        name: "x-secret-key",
        type: "apiKey"
      }
    ],
    url: "/v1/transactions/search",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options == null ? void 0 : options.headers
    }
  });
};

// node_modules/thirdweb/dist/esm/engine/get-status.js
async function getTransactionStatus(args) {
  var _a, _b, _c;
  const { client: client2, transactionId } = args;
  const searchResult = await searchTransactions({
    baseUrl: getThirdwebBaseUrl("engineCloud"),
    fetch: getClientFetch(client2),
    body: {
      filters: [
        {
          field: "id",
          values: [transactionId],
          operation: "OR"
        }
      ]
    }
  });
  if (searchResult.error) {
    throw new Error(`Error searching for transaction ${transactionId}: ${stringify(searchResult.error)}`);
  }
  const data = (_c = (_b = (_a = searchResult.data) == null ? void 0 : _a.result) == null ? void 0 : _b.transactions) == null ? void 0 : _c[0];
  if (!data) {
    throw new Error(`Transaction ${transactionId} not found`);
  }
  const executionResult = data.executionResult;
  return {
    ...executionResult,
    createdAt: data.createdAt,
    confirmedAt: data.confirmedAt,
    cancelledAt: data.cancelledAt,
    chain: getCachedChain(Number(data.chainId)),
    from: data.from ?? void 0,
    id: data.id
  };
}
async function waitForTransactionHash(args) {
  const startTime = Date.now();
  const TIMEOUT_IN_MS = args.timeoutInSeconds ? args.timeoutInSeconds * 1e3 : 5 * 60 * 1e3;
  while (Date.now() - startTime < TIMEOUT_IN_MS) {
    const executionResult = await getTransactionStatus(args);
    const status = executionResult.status;
    switch (status) {
      case "FAILED": {
        throw new Error(`Transaction failed: ${executionResult.error || "Unknown error"}`);
      }
      case "CONFIRMED": {
        const onchainStatus = executionResult && "onchainStatus" in executionResult ? executionResult.onchainStatus : null;
        if (onchainStatus === "REVERTED") {
          const revertData = "revertData" in executionResult ? executionResult.revertData : void 0;
          throw new Error(`Transaction reverted: ${(revertData == null ? void 0 : revertData.errorName) || ""} ${(revertData == null ? void 0 : revertData.errorArgs) ? stringify(revertData.errorArgs) : ""}`);
        }
        return {
          transactionHash: executionResult.transactionHash,
          client: args.client,
          chain: executionResult.chain
        };
      }
      default: {
        await new Promise((resolve) => setTimeout(resolve, 1e3));
      }
    }
  }
  throw new Error(`Transaction timed out after ${TIMEOUT_IN_MS / 1e3} seconds`);
}

// node_modules/thirdweb/dist/esm/engine/server-wallet.js
function serverWallet(options) {
  const { client: client2, vaultAccessToken, address, chain, executionOptions } = options;
  const headers = {
    "x-vault-access-token": vaultAccessToken
  };
  const getExecutionOptions = (chainId) => {
    return executionOptions ? {
      ...executionOptions,
      chainId: chainId.toString()
    } : {
      from: address,
      chainId: chainId.toString()
    };
  };
  const enqueueTx = async (transaction) => {
    var _a, _b, _c, _d;
    const body = {
      executionOptions: getExecutionOptions(transaction.chainId),
      params: [
        {
          to: transaction.to ?? void 0,
          data: transaction.data,
          value: (_a = transaction.value) == null ? void 0 : _a.toString()
        }
      ]
    };
    const result = await sendTransaction2({
      baseUrl: getThirdwebBaseUrl("engineCloud"),
      fetch: getClientFetch(client2),
      headers,
      body
    });
    if (result.error) {
      throw new Error(`Error sending transaction: ${result.error}`);
    }
    const data = (_b = result.data) == null ? void 0 : _b.result;
    if (!data) {
      throw new Error("No data returned from engine");
    }
    const transactionId = (_d = (_c = data.transactions) == null ? void 0 : _c[0]) == null ? void 0 : _d.id;
    if (!transactionId) {
      throw new Error("No transactionId returned from engine");
    }
    return transactionId;
  };
  return {
    address,
    enqueueTransaction: async (args) => {
      let serializedTransaction;
      if (args.simulate) {
        serializedTransaction = await toSerializableTransaction({
          transaction: args.transaction
        });
      } else {
        const [to, data, value] = await Promise.all([
          args.transaction.to ? resolvePromisedValue(args.transaction.to) : null,
          encode(args.transaction),
          args.transaction.value ? resolvePromisedValue(args.transaction.value) : null
        ]);
        serializedTransaction = {
          chainId: args.transaction.chain.id,
          data,
          to: to ?? void 0,
          value: value ?? void 0
        };
      }
      const transactionId = await enqueueTx(serializedTransaction);
      return { transactionId };
    },
    sendTransaction: async (transaction) => {
      const transactionId = await enqueueTx(transaction);
      return waitForTransactionHash({
        client: client2,
        transactionId
      });
    },
    signMessage: async (data) => {
      var _a, _b;
      const { message, chainId } = data;
      let engineMessage;
      let isBytes = false;
      if (typeof message === "string") {
        engineMessage = message;
      } else {
        engineMessage = toHex(message.raw);
        isBytes = true;
      }
      const signingChainId = chainId || (chain == null ? void 0 : chain.id);
      if (!signingChainId) {
        throw new Error("Chain ID is required for signing messages");
      }
      const signResult = await signMessage({
        baseUrl: getThirdwebBaseUrl("engineCloud"),
        fetch: getClientFetch(client2),
        headers,
        body: {
          executionOptions: getExecutionOptions(signingChainId),
          params: [
            {
              message: engineMessage,
              messageFormat: isBytes ? "hex" : "text"
            }
          ]
        }
      });
      if (signResult.error) {
        throw new Error(`Error signing message: ${stringify(signResult.error)}`);
      }
      const signatureResult = (_a = signResult.data) == null ? void 0 : _a.result.results[0];
      if (signatureResult == null ? void 0 : signatureResult.success) {
        return signatureResult.result.signature;
      }
      throw new Error(`Failed to sign message: ${((_b = signatureResult == null ? void 0 : signatureResult.error) == null ? void 0 : _b.message) || "Unknown error"}`);
    },
    signTypedData: async (typedData) => {
      var _a, _b;
      const signingChainId = chain == null ? void 0 : chain.id;
      if (!signingChainId) {
        throw new Error("Chain ID is required for signing messages");
      }
      const signResult = await signTypedData({
        baseUrl: getThirdwebBaseUrl("engineCloud"),
        fetch: getClientFetch(client2),
        headers,
        body: {
          executionOptions: getExecutionOptions(signingChainId),
          // biome-ignore lint/suspicious/noExplicitAny: TODO: fix ts / hey-api type clash
          params: [typedData]
        }
      });
      if (signResult.error) {
        throw new Error(`Error signing message: ${stringify(signResult.error)}`);
      }
      const signatureResult = (_a = signResult.data) == null ? void 0 : _a.result.results[0];
      if (signatureResult == null ? void 0 : signatureResult.success) {
        return signatureResult.result.signature;
      }
      throw new Error(`Failed to sign message: ${((_b = signatureResult == null ? void 0 : signatureResult.error) == null ? void 0 : _b.message) || "Unknown error"}`);
    }
  };
}

// node_modules/thirdweb/dist/esm/transaction/resolve-method.js
function resolveMethod(method) {
  return async (contract) => {
    var _a;
    if (typeof method === "string" && method.startsWith("function ")) {
      return parseAbiItem(method);
    }
    const resolvedAbi = ((_a = contract.abi) == null ? void 0 : _a.length) ? contract.abi : await resolveContractAbi(contract);
    const abiFunction = resolvedAbi.find((item) => {
      if (item.type !== "function") {
        return false;
      }
      return item.name === method;
    });
    if (!abiFunction) {
      throw new Error(`could not find function with name "${method}" in abi`);
    }
    return abiFunction;
  };
}

// node_modules/thirdweb/dist/esm/auth/verify-typed-data.js
async function verifyTypedData({ address, signature, client: client2, chain, accountFactory, message, domain, primaryType, types }) {
  const messageHash = getSignPayload({
    message,
    domain,
    primaryType,
    types
  });
  return verifyHash({
    hash: messageHash,
    signature,
    address,
    chain,
    client: client2,
    accountFactory
  });
}

// node_modules/thirdweb/dist/esm/transaction/actions/eip7702/authorization.js
async function signAuthorization(options) {
  const { account, request } = options;
  if (typeof account.signAuthorization === "undefined") {
    throw new Error("This account type does not yet support signing EIP-7702 authorizations");
  }
  return account.signAuthorization(request);
}
export {
  ZERO_ADDRESS as ADDRESS_ZERO,
  bridge_exports as Bridge,
  engine_exports as Engine,
  insight_exports as Insight,
  NATIVE_TOKEN_ADDRESS,
  ZERO_ADDRESS,
  boolToBytes,
  boolToHex,
  bytesToBigInt,
  bytesToBool,
  bytesToNumber,
  bytesToString,
  concatHex,
  createThirdwebClient,
  defineChain,
  deploySmartAccount,
  encode,
  estimateGas,
  estimateGasCost,
  eth_blockNumber,
  eth_call,
  eth_estimateGas,
  eth_gasPrice,
  eth_getBalance,
  eth_getBlockByHash,
  eth_getBlockByNumber,
  eth_getCode,
  eth_getLogs,
  eth_getStorageAt,
  eth_getTransactionByHash,
  eth_getTransactionCount,
  eth_getTransactionReceipt,
  eth_maxPriorityFeePerGas,
  eth_sendRawTransaction,
  fromBytes,
  fromGwei,
  fromHex,
  getAddress,
  getBuyWithCryptoHistory,
  getBuyWithCryptoQuote,
  getBuyWithCryptoStatus,
  getBuyWithCryptoTransfer,
  getContract,
  getContractEvents,
  getGasPrice,
  getRpcClient,
  getUser,
  hexToBigInt,
  hexToBool,
  hexToBytes,
  hexToNumber,
  hexToString,
  hexToUint8Array,
  isAddress,
  isBaseTransactionOptions,
  isHex,
  keccak256,
  numberToBytes,
  numberToHex,
  padHex,
  parseEventLogs,
  prepareContractCall,
  prepareEvent,
  prepareTransaction,
  readContract,
  resolveMethod,
  sendAndConfirmTransaction,
  sendBatchTransaction,
  sendTransaction,
  serializeTransaction,
  sha2562 as sha256,
  signAuthorization,
  signTransaction,
  simulateTransaction,
  stringToBytes,
  stringToHex,
  toBytes,
  toEther,
  toHex,
  toSerializableTransaction,
  toTokens,
  toUnits,
  toWei,
  uint8ArrayToHex,
  verifyTypedData,
  waitForReceipt,
  watchBlockNumber,
  watchContractEvents
};
//# sourceMappingURL=thirdweb.js.map
