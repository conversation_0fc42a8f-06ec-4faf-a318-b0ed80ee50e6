{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.zerion.wallet/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.zerion.wallet\",\n  name: \"Zerion\",\n  homepage: \"https://zerion.io/\",\n  image_id: \"77c1d3dd-0213-400a-f9cc-bfd524c47f00\",\n  app: {\n    browser: \"https://app.zerion.io\",\n    ios: \"https://apps.apple.com/app/id1456732565\",\n    android:\n      \"https://play.google.com/store/apps/details?id=io.zerion.android&hl=en&gl=US\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome:\n      \"https://chrome.google.com/webstore/detail/zerion-wallet-for-web3-nf/klghhnkeealcohjjanjjdaeeggmfmlpl\",\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: \"io.zerion.wallet\",\n  mobile: {\n    native: \"zerion://\",\n    universal: \"https://wallet.zerion.io/wc\",\n  },\n  desktop: {\n    native: \"zerion://\",\n    universal: \"https://wallet.zerion.io\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QACE;IACF,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}