{"version": 3, "sources": ["../../thirdweb/src/storage/upload/helpers.ts", "../../thirdweb/src/storage/upload.ts"], "sourcesContent": ["import { isObjectWithKeys } from \"../../utils/type-guards.js\";\nimport { areUint8ArraysEqual, isUint8Array } from \"../../utils/uint8-array.js\";\nimport type {\n  BufferOrStringWithName,\n  BuildFormDataOptions,\n  FileOrBuffer,\n  FileOrBufferOrString,\n} from \"./types.js\";\n\n/**\n * @internal\n */\nfunction isFileInstance(data: unknown): data is File {\n  return globalThis.File && data instanceof File;\n}\n\n/**\n * @internal\n */\nfunction isBufferOrStringWithName(\n  data: unknown,\n): data is BufferOrStringWithName {\n  if (!data) {\n    return false;\n  }\n  if (!isObjectWithKeys(data, [\"data\", \"name\"])) {\n    return false;\n  }\n  return !!(\n    typeof data.name === \"string\" &&\n    (typeof data.data === \"string\" || isUint8Array(data.data))\n  );\n}\n\nexport function isFileBufferOrStringEqual(\n  input1: unknown,\n  input2: unknown,\n): boolean {\n  if (isFileInstance(input1) && isFileInstance(input2)) {\n    // if both are File types, compare the name, size, and last modified date (best guess that these are the same files)\n    if (\n      input1.name === input2.name &&\n      input1.lastModified === input2.lastModified &&\n      input1.size === input2.size\n    ) {\n      return true;\n    }\n  } else if (isUint8Array(input1) && isUint8Array(input2)) {\n    // buffer gives us an easy way to compare the contents!\n\n    return areUint8ArraysEqual(input1, input2);\n  } else if (\n    isBufferOrStringWithName(input1) &&\n    isBufferOrStringWithName(input2)\n  ) {\n    // first check the names\n    if (input1.name === input2.name) {\n      // if the data for both is a string, compare the strings\n      if (typeof input1.data === \"string\" && typeof input2.data === \"string\") {\n        return input1.data === input2.data;\n      }\n      if (isUint8Array(input1.data) && isUint8Array(input2.data)) {\n        // otherwise we know it's buffers, so compare the buffers\n        return areUint8ArraysEqual(input1.data, input2.data);\n      }\n    }\n  }\n  // otherwise if we have not found a match, return false\n  return false;\n}\n\nexport function buildFormData(\n  form: FormData,\n  files: FileOrBufferOrString[],\n  options?: BuildFormDataOptions,\n) {\n  const fileNameToFileMap = new Map<string, FileOrBufferOrString>();\n  const fileNames: string[] = [];\n  for (let i = 0; i < files.length; i++) {\n    // biome-ignore lint/style/noNonNullAssertion: we know that files[i] is not null or undefined because we are iterating over the array\n    const file = files[i]!;\n    let fileName = \"\";\n    let fileData = file;\n\n    if (isFileInstance(file)) {\n      if (options?.rewriteFileNames) {\n        let extensions = \"\";\n        if (file.name) {\n          const extensionStartIndex = file.name.lastIndexOf(\".\");\n          if (extensionStartIndex > -1) {\n            extensions = file.name.substring(extensionStartIndex);\n          }\n        }\n        fileName = `${\n          i + options.rewriteFileNames.fileStartNumber\n        }${extensions}`;\n      } else {\n        fileName = `${file.name}`;\n      }\n    } else if (isBufferOrStringWithName(file)) {\n      fileData = file.data;\n      if (options?.rewriteFileNames) {\n        fileName = `${i + options.rewriteFileNames.fileStartNumber}`;\n      } else {\n        fileName = `${file.name}`;\n      }\n    } else {\n      if (options?.rewriteFileNames) {\n        fileName = `${i + options.rewriteFileNames.fileStartNumber}`;\n      } else {\n        fileName = `${i}`;\n      }\n    }\n\n    // If we don't want to wrap with directory, adjust the filepath\n    const filepath = options?.uploadWithoutDirectory\n      ? \"files\"\n      : `files/${fileName}`;\n\n    if (fileNameToFileMap.has(fileName)) {\n      // if the file in the map is the same as the file we are already looking at then just skip and continue\n      if (isFileBufferOrStringEqual(fileNameToFileMap.get(fileName), file)) {\n        // we add it to the filenames array so that we can return the correct number of urls,\n        fileNames.push(fileName);\n        // but then we skip because we don't need to upload it multiple times\n        continue;\n      }\n      // otherwise if file names are the same but they are not the same file then we should throw an error (trying to upload to different files but with the same names)\n      throw new Error(\n        `[DUPLICATE_FILE_NAME_ERROR] File name ${fileName} was passed for more than one different file.`,\n      );\n    }\n\n    // add it to the map so that we can check for duplicates\n    fileNameToFileMap.set(fileName, file);\n    // add it to the filenames array so that we can return the correct number of urls\n    fileNames.push(fileName);\n    form.append(\"file\", new Blob([fileData as BlobPart]), filepath);\n  }\n\n  const metadata = {\n    name: \"Storage SDK\",\n    keyvalues: { ...options?.metadata },\n  };\n  form.append(\"pinataMetadata\", JSON.stringify(metadata));\n\n  if (options?.uploadWithoutDirectory) {\n    form.append(\n      \"pinataOptions\",\n      JSON.stringify({\n        wrapWithDirectory: false,\n      }),\n    );\n  }\n\n  return {\n    form,\n    // encode the file names on the way out (which is what the upload backend expects)\n    fileNames: fileNames.map((fName) => encodeURIComponent(fName)),\n  };\n}\n\nexport function isFileOrUint8Array(\n  data: unknown,\n): data is File | Uint8Array | BufferOrStringWithName {\n  return (\n    isFileInstance(data) || isUint8Array(data) || isBufferOrStringWithName(data)\n  );\n}\n\n/**\n * @internal\n */\nexport function extractObjectFiles(\n  data: unknown,\n  files: FileOrBuffer[] = [],\n): FileOrBuffer[] {\n  // If item is a FileOrBuffer add it to our list of files\n  if (isFileOrUint8Array(data)) {\n    files.push(data);\n    return files;\n  }\n\n  if (typeof data === \"object\") {\n    if (!data) {\n      return files;\n    }\n\n    if (Array.isArray(data)) {\n      for (const entry of data) {\n        extractObjectFiles(entry, files);\n      }\n    } else {\n      Object.keys(data).map((key) =>\n        extractObjectFiles(data[key as keyof typeof data], files),\n      );\n    }\n  }\n\n  return files;\n}\n\n/**\n * @internal\n */\nexport function replaceObjectFilesWithUris(\n  data: unknown,\n  uris: string[],\n): unknown {\n  if (isFileOrUint8Array(data)) {\n    if (uris.length) {\n      return uris.shift() as string;\n    }\n    console.warn(\"Not enough URIs to replace all files in object.\");\n  }\n\n  if (typeof data === \"object\") {\n    if (!data) {\n      return data;\n    }\n\n    if (Array.isArray(data)) {\n      return data.map((entry) => replaceObjectFilesWithUris(entry, uris));\n    }\n    return Object.fromEntries(\n      Object.entries(data).map(([key, value]) => [\n        key,\n        replaceObjectFilesWithUris(value, uris),\n      ]),\n    );\n  }\n\n  return data;\n}\n\n/**\n * @internal\n */\nexport function replaceGatewayUrlWithScheme(url: string): string {\n  if (url.includes(\"/ipfs/\")) {\n    const hash = url.split(\"/ipfs/\")[1];\n    return `ipfs://${hash}`;\n  }\n  return url;\n}\n\n/**\n * @internal\n */\nexport function replaceObjectGatewayUrlsWithSchemes<TData>(data: TData): TData {\n  if (typeof data === \"string\") {\n    return replaceGatewayUrlWithScheme(data) as TData;\n  }\n  if (typeof data === \"object\") {\n    if (!data) {\n      return data;\n    }\n\n    if (isFileOrUint8Array(data)) {\n      return data;\n    }\n\n    if (Array.isArray(data)) {\n      return data.map((entry) =>\n        replaceObjectGatewayUrlsWithSchemes(entry),\n      ) as TData;\n    }\n\n    return Object.fromEntries(\n      Object.entries(data).map(([key, value]) => [\n        key,\n        replaceObjectGatewayUrlsWithSchemes(value),\n      ]),\n    ) as TData;\n  }\n\n  return data;\n}\n", "import type { ThirdwebClient } from \"../client/client.js\";\nimport { detectPlatform } from \"../utils/detect-platform.js\";\nimport { stringify } from \"../utils/json.js\";\nimport {\n  buildFormData,\n  extractObjectFiles,\n  isFileOrUint8Array,\n  replaceObjectFilesWithUris,\n  replaceObjectGatewayUrlsWithSchemes,\n} from \"./upload/helpers.js\";\nimport type {\n  FileOrBufferOrString,\n  UploadOptions as InternalUploadOptions,\n  UploadableFile,\n} from \"./upload/types.js\";\n\nexport type UploadOptions<TFiles extends UploadableFile[]> =\n  InternalUploadOptions<TFiles> & {\n    client: ThirdwebClient;\n  };\n\ntype UploadReturnType<TFiles extends UploadableFile[]> = TFiles extends {\n  length: 0;\n}\n  ? null\n  : TFiles extends { length: 1 }\n    ? string\n    : string[];\n\n/**\n * Uploads files based on the provided options.\n * @param options - The upload options.\n * @returns A promise that resolves to the uploaded file URI or URIs (when passing multiple files).\n * @throws An error if the upload fails.\n * @example\n *\n * ### Uploading JSON objects\n *\n * ```ts\n * import { upload } from \"thirdweb/storage\";\n * const uri = await upload({\n *  client,\n *  files: [\n *    {\n *      name: \"something\",\n *      data: {\n *        hello: \"world\",\n *      },\n *    },\n *  ],\n * });\n * ```\n *\n * ### Uploading files\n *\n * ```ts\n * import { upload } from \"thirdweb/storage\";\n * const uri = await upload({\n *  client,\n *  files: [\n *    new File([\"hello world\"], \"hello.txt\"),\n *  ],\n * });\n * ```\n * @storage\n */\nexport async function upload<const TFiles extends UploadableFile[]>(\n  options: UploadOptions<TFiles>,\n): Promise<UploadReturnType<TFiles>> {\n  // deal with the differnt file types\n\n  // if there are no files, return an empty array immediately\n  if (options.files.length === 0) {\n    return null as UploadReturnType<TFiles>;\n  }\n  // handle file arrays\n  const isFileArray = options.files\n    .map((item) => isFileOrUint8Array(item) || typeof item === \"string\")\n    .every((item) => !!item);\n\n  let uris: FileOrBufferOrString[];\n\n  if (isFileArray) {\n    // if we already have an array of files, we can just pass it through\n    uris = options.files as FileOrBufferOrString[];\n  } else {\n    // otherwise we have to process them first\n    let cleaned = options.files as unknown[];\n\n    // Replace any gateway URLs with their hashes\n    cleaned = replaceObjectGatewayUrlsWithSchemes(cleaned);\n\n    // Recurse through data and extract files to upload\n    const files = extractObjectFiles(cleaned);\n    if (files.length) {\n      // Upload all files that came from the object\n      const uris_ = await upload({ ...options, files });\n\n      // Recurse through data and replace files with hashes\n      cleaned = replaceObjectFilesWithUris(\n        cleaned,\n        // always pass an array even if the underlying upload returns a single uri\n        Array.isArray(uris_) ? uris_ : [uris_],\n      ) as unknown[];\n    }\n\n    uris = cleaned.map((item) => {\n      if (typeof item === \"string\") {\n        return item;\n      }\n      return stringify(item);\n    });\n  }\n\n  // end deal with the differnt file types\n  const form_ = new FormData();\n\n  const { fileNames, form } = buildFormData(form_, uris, options);\n\n  const platform = detectPlatform();\n  if (platform === \"browser\" || platform === \"node\") {\n    const { uploadBatch } = await import(\"./upload/web-node.js\");\n    const uris = await uploadBatch(options.client, form, fileNames, options);\n    // if we only passed a single file, return its URI directly\n    if (options.files.length === 1) {\n      return uris[0] as UploadReturnType<TFiles>;\n    }\n    return uris as UploadReturnType<TFiles>;\n  }\n  throw new Error(\n    \"Please, use the uploadMobile function in mobile environments.\",\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAYA,SAAS,eAAe,MAAa;AACnC,SAAO,WAAW,QAAQ,gBAAgB;AAC5C;AAKA,SAAS,yBACP,MAAa;AAEb,MAAI,CAAC,MAAM;AACT,WAAO;EACT;AACA,MAAI,CAAC,iBAAiB,MAAM,CAAC,QAAQ,MAAM,CAAC,GAAG;AAC7C,WAAO;EACT;AACA,SAAO,CAAC,EACN,OAAO,KAAK,SAAS,aACpB,OAAO,KAAK,SAAS,YAAY,aAAa,KAAK,IAAI;AAE5D;AAEM,SAAU,0BACd,QACA,QAAe;AAEf,MAAI,eAAe,MAAM,KAAK,eAAe,MAAM,GAAG;AAEpD,QACE,OAAO,SAAS,OAAO,QACvB,OAAO,iBAAiB,OAAO,gBAC/B,OAAO,SAAS,OAAO,MACvB;AACA,aAAO;IACT;EACF,WAAW,aAAa,MAAM,KAAK,aAAa,MAAM,GAAG;AAGvD,WAAO,oBAAoB,QAAQ,MAAM;EAC3C,WACE,yBAAyB,MAAM,KAC/B,yBAAyB,MAAM,GAC/B;AAEA,QAAI,OAAO,SAAS,OAAO,MAAM;AAE/B,UAAI,OAAO,OAAO,SAAS,YAAY,OAAO,OAAO,SAAS,UAAU;AACtE,eAAO,OAAO,SAAS,OAAO;MAChC;AACA,UAAI,aAAa,OAAO,IAAI,KAAK,aAAa,OAAO,IAAI,GAAG;AAE1D,eAAO,oBAAoB,OAAO,MAAM,OAAO,IAAI;MACrD;IACF;EACF;AAEA,SAAO;AACT;AAEM,SAAU,cACd,MACA,OACA,SAA8B;AAE9B,QAAM,oBAAoB,oBAAI,IAAG;AACjC,QAAM,YAAsB,CAAA;AAC5B,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAErC,UAAM,OAAO,MAAM,CAAC;AACpB,QAAI,WAAW;AACf,QAAI,WAAW;AAEf,QAAI,eAAe,IAAI,GAAG;AACxB,UAAI,mCAAS,kBAAkB;AAC7B,YAAI,aAAa;AACjB,YAAI,KAAK,MAAM;AACb,gBAAM,sBAAsB,KAAK,KAAK,YAAY,GAAG;AACrD,cAAI,sBAAsB,IAAI;AAC5B,yBAAa,KAAK,KAAK,UAAU,mBAAmB;UACtD;QACF;AACA,mBAAW,GACT,IAAI,QAAQ,iBAAiB,eAC/B,GAAG,UAAU;MACf,OAAO;AACL,mBAAW,GAAG,KAAK,IAAI;MACzB;IACF,WAAW,yBAAyB,IAAI,GAAG;AACzC,iBAAW,KAAK;AAChB,UAAI,mCAAS,kBAAkB;AAC7B,mBAAW,GAAG,IAAI,QAAQ,iBAAiB,eAAe;MAC5D,OAAO;AACL,mBAAW,GAAG,KAAK,IAAI;MACzB;IACF,OAAO;AACL,UAAI,mCAAS,kBAAkB;AAC7B,mBAAW,GAAG,IAAI,QAAQ,iBAAiB,eAAe;MAC5D,OAAO;AACL,mBAAW,GAAG,CAAC;MACjB;IACF;AAGA,UAAM,YAAW,mCAAS,0BACtB,UACA,SAAS,QAAQ;AAErB,QAAI,kBAAkB,IAAI,QAAQ,GAAG;AAEnC,UAAI,0BAA0B,kBAAkB,IAAI,QAAQ,GAAG,IAAI,GAAG;AAEpE,kBAAU,KAAK,QAAQ;AAEvB;MACF;AAEA,YAAM,IAAI,MACR,yCAAyC,QAAQ,+CAA+C;IAEpG;AAGA,sBAAkB,IAAI,UAAU,IAAI;AAEpC,cAAU,KAAK,QAAQ;AACvB,SAAK,OAAO,QAAQ,IAAI,KAAK,CAAC,QAAoB,CAAC,GAAG,QAAQ;EAChE;AAEA,QAAM,WAAW;IACf,MAAM;IACN,WAAW,EAAE,GAAG,mCAAS,SAAQ;;AAEnC,OAAK,OAAO,kBAAkB,KAAK,UAAU,QAAQ,CAAC;AAEtD,MAAI,mCAAS,wBAAwB;AACnC,SAAK,OACH,iBACA,KAAK,UAAU;MACb,mBAAmB;KACpB,CAAC;EAEN;AAEA,SAAO;IACL;;IAEA,WAAW,UAAU,IAAI,CAAC,UAAU,mBAAmB,KAAK,CAAC;;AAEjE;AAEM,SAAU,mBACd,MAAa;AAEb,SACE,eAAe,IAAI,KAAK,aAAa,IAAI,KAAK,yBAAyB,IAAI;AAE/E;AAKM,SAAU,mBACd,MACA,QAAwB,CAAA,GAAE;AAG1B,MAAI,mBAAmB,IAAI,GAAG;AAC5B,UAAM,KAAK,IAAI;AACf,WAAO;EACT;AAEA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,MAAM;AACT,aAAO;IACT;AAEA,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,iBAAW,SAAS,MAAM;AACxB,2BAAmB,OAAO,KAAK;MACjC;IACF,OAAO;AACL,aAAO,KAAK,IAAI,EAAE,IAAI,CAAC,QACrB,mBAAmB,KAAK,GAAwB,GAAG,KAAK,CAAC;IAE7D;EACF;AAEA,SAAO;AACT;AAKM,SAAU,2BACd,MACA,MAAc;AAEd,MAAI,mBAAmB,IAAI,GAAG;AAC5B,QAAI,KAAK,QAAQ;AACf,aAAO,KAAK,MAAK;IACnB;AACA,YAAQ,KAAK,iDAAiD;EAChE;AAEA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,MAAM;AACT,aAAO;IACT;AAEA,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,aAAO,KAAK,IAAI,CAAC,UAAU,2BAA2B,OAAO,IAAI,CAAC;IACpE;AACA,WAAO,OAAO,YACZ,OAAO,QAAQ,IAAI,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;MACzC;MACA,2BAA2B,OAAO,IAAI;KACvC,CAAC;EAEN;AAEA,SAAO;AACT;AAKM,SAAU,4BAA4B,KAAW;AACrD,MAAI,IAAI,SAAS,QAAQ,GAAG;AAC1B,UAAM,OAAO,IAAI,MAAM,QAAQ,EAAE,CAAC;AAClC,WAAO,UAAU,IAAI;EACvB;AACA,SAAO;AACT;AAKM,SAAU,oCAA2C,MAAW;AACpE,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO,4BAA4B,IAAI;EACzC;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,MAAM;AACT,aAAO;IACT;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,aAAO;IACT;AAEA,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,aAAO,KAAK,IAAI,CAAC,UACf,oCAAoC,KAAK,CAAC;IAE9C;AAEA,WAAO,OAAO,YACZ,OAAO,QAAQ,IAAI,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;MACzC;MACA,oCAAoC,KAAK;KAC1C,CAAC;EAEN;AAEA,SAAO;AACT;;;ACnNA,eAAsB,OACpB,SAA8B;AAK9B,MAAI,QAAQ,MAAM,WAAW,GAAG;AAC9B,WAAO;EACT;AAEA,QAAM,cAAc,QAAQ,MACzB,IAAI,CAAC,SAAS,mBAAmB,IAAI,KAAK,OAAO,SAAS,QAAQ,EAClE,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI;AAEzB,MAAI;AAEJ,MAAI,aAAa;AAEf,WAAO,QAAQ;EACjB,OAAO;AAEL,QAAI,UAAU,QAAQ;AAGtB,cAAU,oCAAoC,OAAO;AAGrD,UAAM,QAAQ,mBAAmB,OAAO;AACxC,QAAI,MAAM,QAAQ;AAEhB,YAAM,QAAQ,MAAM,OAAO,EAAE,GAAG,SAAS,MAAK,CAAE;AAGhD,gBAAU;QACR;;QAEA,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;MAAC;IAE1C;AAEA,WAAO,QAAQ,IAAI,CAAC,SAAQ;AAC1B,UAAI,OAAO,SAAS,UAAU;AAC5B,eAAO;MACT;AACA,aAAO,UAAU,IAAI;IACvB,CAAC;EACH;AAGA,QAAM,QAAQ,IAAI,SAAQ;AAE1B,QAAM,EAAE,WAAW,KAAI,IAAK,cAAc,OAAO,MAAM,OAAO;AAE9D,QAAM,WAAW,eAAc;AAC/B,MAAI,aAAa,aAAa,aAAa,QAAQ;AACjD,UAAM,EAAE,YAAW,IAAK,MAAM,OAAO,wBAAsB;AAC3D,UAAMA,QAAO,MAAM,YAAY,QAAQ,QAAQ,MAAM,WAAW,OAAO;AAEvE,QAAI,QAAQ,MAAM,WAAW,GAAG;AAC9B,aAAOA,MAAK,CAAC;IACf;AACA,WAAOA;EACT;AACA,QAAM,IAAI,MACR,+DAA+D;AAEnE;", "names": ["uris"]}