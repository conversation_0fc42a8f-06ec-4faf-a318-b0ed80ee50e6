{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/us.binance/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"us.binance\",\n  name: \"Binance.US\",\n  homepage: \"https://binance.us\",\n  image_id: \"48aa1a7d-c5fe-4ad6-c2f2-e5684b296900\",\n  app: {\n    browser: null,\n    ios: \"https://itunes.apple.com/app/id1492670702\",\n    android: \"https://play.google.com/store/apps/details?id=com.binance.us\",\n    mac: \"\",\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"bncus://binance.us\",\n    universal: \"https://binance.us/universal_JHHGDSKDJ\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}