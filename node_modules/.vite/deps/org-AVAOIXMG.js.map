{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/org.dota168/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"org.dota168\",\n  name: \"MetaWallet\",\n  homepage: \"http://www.dota168.org/\",\n  image_id: \"a18337ad-433f-47c0-ea57-8a6199835e00\",\n  app: {\n    browser: null,\n    ios: null,\n    android: \"http://www.dota168.org/\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: \"http://www.dota168.org/\",\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"metawallet://com.metawallet.client\",\n    universal: \"http://www.dota168.org/\",\n  },\n  desktop: {\n    native: \"metawallet://\",\n    universal: \"http://www.dota168.org/\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}