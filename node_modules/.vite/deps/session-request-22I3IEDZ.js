import {
  handleSendRawTransactionRequest,
  handleSendTransactionRequest,
  handleSignRequest,
  handleSignTransactionRequest,
  handleSignTypedDataRequest,
  parseEip155ChainId
} from "./chunk-WUZHUB45.js";
import "./chunk-JK36SS76.js";
import "./chunk-HFJPNBPY.js";
import "./chunk-HGMV3JDR.js";
import "./chunk-QGXAPRFG.js";
import "./chunk-E4AMV5H3.js";
import "./chunk-WLZN2VO2.js";
import "./chunk-HKVYRBWW.js";
import "./chunk-HAADYJEF.js";
import "./chunk-LVUFVX5C.js";
import "./chunk-UEKVYVRB.js";
import "./chunk-IHMGPG6V.js";
import "./chunk-MZGV4VDW.js";
import "./chunk-RPGMYTER.js";
import "./chunk-64KT6ODC.js";
import "./chunk-EP3M7YWL.js";
import "./chunk-NTKAF5LO.js";
import "./chunk-2CIJO3V3.js";
import "./chunk-E4AXWHD7.js";
import "./chunk-H7Z32RTP.js";
import "./chunk-YXD4WFHV.js";
import "./chunk-26FWGFQH.js";
import "./chunk-DESKQC7P.js";
import "./chunk-BJ63FHMG.js";
import "./chunk-4LB33PYO.js";
import "./chunk-6CMZOK3K.js";
import "./chunk-HXWRQBIO.js";
import "./chunk-5UQS4U7E.js";
import "./chunk-ZLUBC7IW.js";
import "./chunk-5Q2S2L2S.js";
import "./chunk-KQKMGIQ6.js";
import "./chunk-MTFDOOBS.js";
import "./chunk-RJUQUX6Y.js";
import "./chunk-PPP72TBL.js";
import "./chunk-3OXDSLPJ.js";
import "./chunk-OSFP2VB7.js";
import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/wallet-connect/receiver/session-request.js
async function fulfillRequest(options) {
  const { wallet, walletConnectClient, thirdwebClient, event: { topic, id, params: { chainId: rawChainId, request } }, handlers } = options;
  const account = wallet.getAccount();
  if (!account) {
    throw new Error("No account connected to provided wallet");
  }
  let result;
  try {
    switch (request.method) {
      case "personal_sign": {
        if (handlers == null ? void 0 : handlers.personal_sign) {
          result = await handlers.personal_sign({
            account,
            params: request.params
          });
        } else {
          result = await handleSignRequest({
            account,
            params: request.params
          });
        }
        break;
      }
      case "eth_sign": {
        if (handlers == null ? void 0 : handlers.eth_sign) {
          result = await handlers.eth_sign({
            account,
            params: request.params
          });
        } else {
          result = await handleSignRequest({
            account,
            params: request.params
          });
        }
        break;
      }
      case "eth_signTypedData": {
        if (handlers == null ? void 0 : handlers.eth_signTypedData) {
          result = await handlers.eth_signTypedData({
            account,
            params: request.params
          });
        } else {
          result = await handleSignTypedDataRequest({
            account,
            params: request.params
          });
        }
        break;
      }
      case "eth_signTypedData_v4": {
        if (handlers == null ? void 0 : handlers.eth_signTypedData_v4) {
          result = await handlers.eth_signTypedData_v4({
            account,
            params: request.params
          });
        } else {
          result = await handleSignTypedDataRequest({
            account,
            params: request.params
          });
        }
        break;
      }
      case "eth_signTransaction": {
        if (handlers == null ? void 0 : handlers.eth_signTransaction) {
          result = await handlers.eth_signTransaction({
            account,
            params: request.params
          });
        } else {
          result = await handleSignTransactionRequest({
            account,
            params: request.params
          });
        }
        break;
      }
      case "eth_sendTransaction": {
        const chainId = parseEip155ChainId(rawChainId);
        if (handlers == null ? void 0 : handlers.eth_sendTransaction) {
          result = await handlers.eth_sendTransaction({
            account,
            chainId,
            params: request.params
          });
        } else {
          result = await handleSendTransactionRequest({
            account,
            chainId,
            thirdwebClient,
            params: request.params
          });
        }
        break;
      }
      case "eth_sendRawTransaction": {
        const chainId = parseEip155ChainId(rawChainId);
        if (handlers == null ? void 0 : handlers.eth_sendRawTransaction) {
          result = await handlers.eth_sendRawTransaction({
            account,
            chainId,
            params: request.params
          });
        } else {
          result = await handleSendRawTransactionRequest({
            account,
            chainId,
            params: request.params
          });
        }
        break;
      }
      case "wallet_addEthereumChain": {
        if (handlers == null ? void 0 : handlers.wallet_addEthereumChain) {
          result = await handlers.wallet_addEthereumChain({
            wallet,
            params: request.params
          });
        } else {
          throw new Error("Unsupported request method: wallet_addEthereumChain");
        }
        break;
      }
      case "wallet_switchEthereumChain": {
        if (handlers == null ? void 0 : handlers.wallet_switchEthereumChain) {
          result = await handlers.wallet_switchEthereumChain({
            wallet,
            params: request.params
          });
        } else {
          const { handleSwitchChain } = await import("./switch-chain-WF5G5YI3.js");
          result = await handleSwitchChain({
            wallet,
            params: request.params
          });
        }
        break;
      }
      default: {
        const potentialHandler = handlers == null ? void 0 : handlers[request.method];
        if (potentialHandler) {
          result = await potentialHandler({
            account,
            chainId: parseEip155ChainId(rawChainId),
            params: request.params
          });
        } else {
          throw new Error(`Unsupported request method: ${request.method}`);
        }
      }
    }
  } catch (error) {
    result = {
      code: typeof error === "object" && error !== null && "code" in error ? error.code : 500,
      message: typeof error === "object" && error !== null && "message" in error ? error.message : "Unknown error"
    };
  }
  walletConnectClient.respond({
    topic,
    response: {
      id,
      jsonrpc: "2.0",
      result
    }
  });
}
export {
  fulfillRequest
};
//# sourceMappingURL=session-request-22I3IEDZ.js.map
