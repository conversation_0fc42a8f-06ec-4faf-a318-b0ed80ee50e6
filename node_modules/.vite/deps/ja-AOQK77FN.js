import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/react/web/wallets/shared/locale/ja.js
var ja_default = {
  signInWithGoogle: "Google",
  signInWithFacebook: "Facebook",
  signInWithApple: "Apple",
  signInWithDiscord: "Discord",
  emailPlaceholder: "メールアドレス",
  submitEmail: "続行",
  signIn: "サインイン",
  or: "または",
  emailRequired: "メールアドレスは必須です",
  invalidEmail: "無効なメールアドレス",
  maxAccountsExceeded: "アカウントの最大数を超えました。アプリ開発者に通知してください。",
  socialLoginScreen: {
    title: "サインイン",
    instruction: "ポップアップでアカウントにサインインしてください",
    failed: "サインインに失敗しました",
    retry: "再試行"
  },
  emailLoginScreen: {
    title: "サインイン",
    enterCodeSendTo: "送信された確認コードを入力してください",
    newDeviceDetected: "新しいデバイスが検出されました",
    enterRecoveryCode: "初回サインアップ時に送信されたリカバリーコードを入力してください",
    invalidCode: "無効な確認コード",
    invalidCodeOrRecoveryCode: "無効な確認コードまたはリカバリーコード",
    verify: "確認",
    failedToSendCode: "確認コードの送信に失敗しました",
    sendingCode: "確認コードを送信中",
    resendCode: "確認コードを再送"
  },
  createPassword: {
    title: "パスワードを作成",
    instruction: "アカウントのパスワードを設定してください。新しいデバイスから接続する際にこのパスワードが必要です。",
    saveInstruction: "必ず保存してください",
    inputPlaceholder: "パスワードを入力してください",
    confirmation: "パスワードを保存しました",
    submitButton: "パスワードを設定",
    failedToSetPassword: "パスワードの設定に失敗しました"
  },
  enterPassword: {
    title: "パスワードを入力",
    instruction: "アカウントのパスワードを入力してください",
    inputPlaceholder: "パスワードを入力してください",
    submitButton: "確認",
    wrongPassword: "パスワードが間違っています"
  },
  signInWithEmail: "メールでサインイン",
  invalidPhone: "無効な電話番号",
  phonePlaceholder: "電話番号",
  signInWithPhone: "電話番号でサインイン",
  phoneRequired: "電話番号は必須です",
  passkey: "パスキー",
  linkWallet: "ウォレットをリンクする",
  loginAsGuest: "ゲストとしてログイン",
  signInWithWallet: "ウォレットでログイン"
};
export {
  ja_default as default
};
//# sourceMappingURL=ja-AOQK77FN.js.map
