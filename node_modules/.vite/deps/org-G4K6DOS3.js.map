{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/org.kelp/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"org.kelp\",\n  name: \"<PERSON><PERSON><PERSON>\",\n  homepage: \"https://kelp.org\",\n  image_id: \"1854e47d-3804-4e92-e455-06829b64b100\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/kelp/id1632857274\",\n    android: \"https://play.google.com/store/apps/details?id=com.app.kelpa\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"link.kelp.finance://walletconnect\",\n    universal: \"https://link.kelp.finance/walletconnect\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}