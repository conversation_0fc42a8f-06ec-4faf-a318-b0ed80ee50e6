{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/pro.tokenpocket/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"pro.tokenpocket\",\n  name: \"TokenPocket\",\n  homepage: \"https://tokenpocket.pro/\",\n  image_id: \"d8e930b6-ccde-471e-ecbe-6967b1c0c400\",\n  app: {\n    browser:\n      \"https://chrome.google.com/webstore/detail/tokenpocket/mfgccjchihfkkindfppnaooecgfneiii\",\n    ios: \"https://apps.apple.com/us/app/tp-wallet/id6444625622?l=en\",\n    android: \"https://play.google.com/store/apps/details?id=vip.mytokenpocket\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome:\n      \"https://chrome.google.com/webstore/detail/tokenpocket/mfgccjchihfkkindfppnaooecgfneiii\",\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: \"pro.tokenpocket\",\n  mobile: {\n    native: \"tpoutside://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal:\n      \"https://chrome.google.com/webstore/detail/tokenpocket/mfgccjchihfkkindfppnaooecgfneiii\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SACE;IACF,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QACE;IACF,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WACE;;;", "names": []}