{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/network.haqq/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"network.haqq\",\n  name: \"HAQQ Wallet\",\n  homepage: \"https://haqq.network/wallet\",\n  image_id: \"99fe539d-6a2a-4f52-2211-42fd04a9f300\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/haqq-wallet-by-bored-gen/id6443843352\",\n    android: \"https://play.google.com/store/apps/details?id=com.haqq.wallet\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"haqq://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}