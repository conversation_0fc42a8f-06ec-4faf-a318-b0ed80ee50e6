import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/react/web/wallets/shared/locale/kr.js
var kr_default = {
  signInWithGoogle: "Google",
  signInWithFacebook: "Facebook",
  signInWithApple: "Apple",
  signInWithDiscord: "Discord",
  emailPlaceholder: "이메일 주소",
  submitEmail: "계속하기",
  signIn: "로그인",
  or: "Or",
  emailRequired: "이메일 주소가 필요합니다",
  invalidEmail: "잘못된 이메일 주소",
  maxAccountsExceeded: "계정 최대 수를 초과했습니다. 앱 개발자에게 알려주세요.",
  socialLoginScreen: {
    title: "로그인",
    instruction: "팝업 창에서 계정에 로그인하세요",
    failed: "로그인에 실패했습니다",
    retry: "다시 시도"
  },
  emailLoginScreen: {
    title: "로그인",
    enterCodeSendTo: "전송된 인증 코드를 입력하세요",
    newDeviceDetected: "새 기기가 감지되었습니다",
    enterRecoveryCode: "처음 가입할 때 이메일로 전송된 복구 코드를 입력하세요",
    invalidCode: "잘못된 인증 코드",
    invalidCodeOrRecoveryCode: "잘못된 인증 코드 또는 복구 코드",
    verify: "확인",
    failedToSendCode: "인증 코드 전송에 실패했습니다",
    sendingCode: "인증 코드 전송 중",
    resendCode: "인증 코드 다시 보내기"
  },
  createPassword: {
    title: "비밀번호 생성",
    instruction: "계정의 비밀번호를 설정하세요. 새 기기에서 연결할 때 이 비밀번호가 필요합니다.",
    saveInstruction: "반드시 저장하세요",
    inputPlaceholder: "비밀번호를 입력하세요",
    confirmation: "비밀번호를 저장했습니다",
    submitButton: "비밀번호 설정",
    failedToSetPassword: "비밀번호 설정 실패"
  },
  enterPassword: {
    title: "비밀번호를 입력하세요",
    instruction: "계정의 비밀번호를 입력하세요",
    inputPlaceholder: "비밀번호를 입력하세요",
    submitButton: "확인",
    wrongPassword: "잘못된 비밀번호"
  },
  signInWithEmail: "이메일로 로그인",
  invalidPhone: "잘못된 전화번호",
  phonePlaceholder: "전화번호",
  signInWithPhone: "전화번호로 로그인",
  phoneRequired: "전화번호가 필요합니다",
  passkey: "비밀번호",
  linkWallet: "지갑 연결",
  loginAsGuest: "게스트로 로그인",
  signInWithWallet: "지갑으로 로그인"
};
export {
  kr_default as default
};
//# sourceMappingURL=kr-J4BS33JV.js.map
