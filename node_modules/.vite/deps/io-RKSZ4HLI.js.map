{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.neopin/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.neopin\",\n  name: \"<PERSON>OP<PERSON>\",\n  homepage: \"https://neopin.io/\",\n  image_id: \"424c54b5-b786-4c14-871f-61d5c5ded800\",\n  app: {\n    browser: null,\n    ios: \"https://itunes.apple.com/app/id1600381072\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.blockchain.crypto.wallet.neopin\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"nptwc://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}