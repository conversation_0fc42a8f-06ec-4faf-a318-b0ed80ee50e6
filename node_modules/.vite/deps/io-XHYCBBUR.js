import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/io.ethermail/index.js
var wallet = {
  id: "io.ethermail",
  name: "EtherMail",
  homepage: "https://ethermail.io",
  image_id: "7f3205c6-6051-4cdb-8ef8-84334a7c7f00",
  app: {
    browser: null,
    ios: "https://apps.apple.com/us/app/ethermail/id6451305966",
    android: "https://play.google.com/store/apps/details?id=com.ethermail.ethermail_android",
    mac: null,
    windows: null,
    linux: null,
    chrome: null,
    firefox: null,
    safari: null,
    edge: null,
    opera: null
  },
  rdns: null,
  mobile: {
    native: "ethermail://",
    universal: null
  },
  desktop: {
    native: null,
    universal: null
  }
};
export {
  wallet
};
//# sourceMappingURL=io-XHYCBBUR.js.map
