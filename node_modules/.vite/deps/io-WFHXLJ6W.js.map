{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.certhis/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.certhis\",\n  name: \"<PERSON><PERSON><PERSON>\",\n  homepage: \"https://certhis.io\",\n  image_id: \"fbd441cc-e861-46dc-48ae-a04228ddb500\",\n  app: {\n    browser: \"https://explorer.certhis.io\",\n    ios: null,\n    android: null,\n    mac: \"\",\n    windows: null,\n    linux: \"https://certhis.io/\",\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: null,\n    universal: \"https://certhis.io/\",\n  },\n  desktop: {\n    native: null,\n    universal: \"https://certhis.io\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}