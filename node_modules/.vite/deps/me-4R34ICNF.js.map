{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/me.haha/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"me.haha\",\n  name: \"<PERSON><PERSON><PERSON>\",\n  homepage: \"https://www.haha.me\",\n  image_id: \"79285c9f-2630-451e-0680-c71b42fb7400\",\n  app: {\n    browser: \"https://www.haha.me\",\n    ios: \"https://apps.apple.com/us/app/haha-crypto-portfolio-tracker/id1591158244\",\n    android: \"https://play.google.com/store/apps/details?id=com.permutize.haha\",\n    mac: \"https://apps.apple.com/us/app/haha-crypto-portfolio-tracker/id1591158244\",\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"haha://\",\n    universal: \"https://haha.me\",\n  },\n  desktop: {\n    native: null,\n    universal: \"https://www.haha.me\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}