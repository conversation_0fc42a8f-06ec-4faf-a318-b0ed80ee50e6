{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/me.rainbow/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"me.rainbow\",\n  name: \"<PERSON>\",\n  homepage: \"https://rainbow.me/\",\n  image_id: \"a9978739-0685-43d5-bb3c-c11545d91300\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/app/apple-store/id1457119021?pt=119997837&ct=wc&mt=8\",\n    android:\n      \"https://play.google.com/store/apps/details?id=me.rainbow&referrer=utm_source%3Dwc%26utm_medium%3Dconnector%26utm_campaign%3Dwc\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome:\n      \"https://chrome.google.com/webstore/detail/rainbow/opfgelmcmbiajamepnmloijbpoleiama?utm_source=wc&utm_medium=connector&utm_campaign=wc\",\n    firefox:\n      \"https://addons.mozilla.org/en-US/firefox/addon/rainbow-extension/?utm_source=wc&utm_medium=connector&utm_campaign=wc\",\n    safari: null,\n    edge: \"https://microsoftedge.microsoft.com/addons/detail/rainbow/cpojfbodiccabbabgimdeohkkpjfpbnf?utm_source=wc&utm_medium=connector&utm_campaign=wc\",\n    opera: null,\n  },\n  rdns: \"me.rainbow\",\n  mobile: {\n    native: \"rainbow://\",\n    universal: \"https://rnbwapp.com\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QACE;IACF,SACE;IACF,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}