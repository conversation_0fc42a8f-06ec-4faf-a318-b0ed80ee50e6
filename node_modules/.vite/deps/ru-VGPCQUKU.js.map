{"version": 3, "sources": ["../../thirdweb/src/react/web/ui/ConnectWallet/locale/ru.ts"], "sourcesContent": ["import type { ConnectLocale } from \"./types.js\";\n\nconst connectLocaleRu: ConnectLocale = {\n  id: \"ru_RU\",\n  signIn: \"Войти\",\n  defaultButtonTitle: \"Подключиться\",\n  connecting: \"Подключение\",\n  switchNetwork: \"Сменить сеть\",\n  switchingNetwork: \"Смена сети\",\n  defaultModalTitle: \"Войти\",\n  recommended: \"Рекомендуется\",\n  installed: \"Установлено\",\n  buy: \"Купить\",\n  continueAsGuest: \"Продолжить как гость\",\n  connectAWallet: \"Подключить кошелек\",\n  newToWallets: \"Новичок в кошельках?\",\n  getStarted: \"Начать\",\n  guest: \"Гость\",\n  send: \"Отправить\",\n  receive: \"Получить\",\n  currentNetwork: \"Текущая сеть\",\n  switchAccount: \"Сменить аккаунт\",\n  requestTestnetFunds: \"Запросить средства тестовой сети\",\n  transactions: \"Транзакции\",\n  payTransactions: \"Фиатные транзакции\",\n  walletTransactions: \"Транзакции кошелька\",\n  viewAllTransactions: \"Просмотреть все транзакции\",\n  backupWallet: \"Создать резервную копию кошелька\",\n  guestWalletWarning:\n    \"Это временный гостевой кошелек. Создайте резервную копию, если не хотите потерять к нему доступ\",\n  switchTo: \"Переключиться на\", // Используется в \"Switch to <Wallet-Name>>\"\n  connectedToSmartWallet: \"Смарт-аккаунт\",\n  confirmInWallet: \"Подтвердить в кошельке\",\n  disconnectWallet: \"Отсоединить кошелек\",\n  copyAddress: \"Скопировать адрес\",\n  personalWallet: \"Личный кошелек\",\n  smartWallet: \"Смарт-кошелек\",\n  or: \"ИЛИ\",\n  goBackButton: \"Назад\",\n  passkeys: {\n    title: \"Ключи доступа\",\n    linkPasskey: \"Привязать ключ доступа\",\n  },\n  welcomeScreen: {\n    defaultTitle: \"Ваш портал в мир децентрализации\",\n    defaultSubtitle: \"Подключите кошелек, чтобы начать\",\n  },\n  agreement: {\n    prefix: \"Подключаясь, вы соглашаетесь с\",\n    termsOfService: \"Условиями использования\",\n    and: \"и\",\n    privacyPolicy: \"Политикой конфиденциальности\",\n  },\n  networkSelector: {\n    title: \"Выбрать сеть\",\n    mainnets: \"Основные сети (мейннетс)\",\n    testnets: \"Тестовые сети (тестнетс)\",\n    allNetworks: \"Все\",\n    addCustomNetwork: \"Добавить кастомную сеть\",\n    inputPlaceholder: \"Поиск сети или Chain ID\",\n    categoryLabel: {\n      recentlyUsed: \"Недавно использованные\",\n      popular: \"Популярные\",\n      others: \"Все сети\",\n    },\n    loading: \"Загрузка\",\n    failedToSwitch: \"Не удалось сменить сеть\",\n  },\n  receiveFundsScreen: {\n    title: \"Получить средства\",\n    instruction:\n      \"Скопируйте адрес кошелька, чтобы отправить средства на этот кошелек\",\n  },\n  sendFundsScreen: {\n    title: \"Отправить средства\",\n    submitButton: \"Отправить\",\n    token: \"Токен\",\n    sendTo: \"Отправить на\",\n    amount: \"Сумма\",\n    successMessage: \"Транзакция успешно выполнена\",\n    invalidAddress: \"Недействительный адрес\",\n    noTokensFound: \"Токены не найдены\",\n    searchToken: \"Найти или вставить адрес токена\",\n    transactionFailed: \"Транзакция не удалась\",\n    transactionRejected: \"Транзакция отклонена\",\n    insufficientFunds: \"Недостаточно средств\",\n    selectTokenTitle: \"Выбрать токен\",\n    sending: \"Отправка\",\n  },\n  signatureScreen: {\n    instructionScreen: {\n      title: \"Войти\",\n      instruction:\n        \"Пожалуйста, подпишите сообщение в кошельке, чтобы продолжить\",\n      signInButton: \"Войти\",\n      disconnectWallet: \"Отсоединить кошелек\",\n    },\n    signingScreen: {\n      title: \"Вход\",\n      prompt: \"Подпись запроса в вашем кошельке\",\n      promptForSafe:\n        \"Подпишите запрос в вашем кошельке и подтвердите транзакцию в Safe\",\n      approveTransactionInSafe: \"Подтвердить транзакцию в Safe\",\n      tryAgain: \"Попробовать снова\",\n      failedToSignIn: \"Не удалось войти\",\n      inProgress: \"Ожидание подтверждения\",\n    },\n  },\n  manageWallet: {\n    title: \"Управление кошельком\",\n    linkedProfiles: \"Привязанные профили\",\n    linkProfile: \"Привязать профиль\",\n    connectAnApp: \"Подключить приложение\",\n    exportPrivateKey: \"Экспортировать приватный ключ\",\n  },\n  viewFunds: {\n    title: \"Просмотр средств\",\n    viewNFTs: \"Просмотр NFTs\",\n    viewTokens: \"Просмотр токенов\",\n    viewAssets: \"Просмотр активов\",\n  },\n};\n\nexport default connectLocaleRu;\n"], "mappings": ";;;AAEA,IAAM,kBAAiC;EACrC,IAAI;EACJ,QAAQ;EACR,oBAAoB;EACpB,YAAY;EACZ,eAAe;EACf,kBAAkB;EAClB,mBAAmB;EACnB,aAAa;EACb,WAAW;EACX,KAAK;EACL,iBAAiB;EACjB,gBAAgB;EAChB,cAAc;EACd,YAAY;EACZ,OAAO;EACP,MAAM;EACN,SAAS;EACT,gBAAgB;EAChB,eAAe;EACf,qBAAqB;EACrB,cAAc;EACd,iBAAiB;EACjB,oBAAoB;EACpB,qBAAqB;EACrB,cAAc;EACd,oBACE;EACF,UAAU;;EACV,wBAAwB;EACxB,iBAAiB;EACjB,kBAAkB;EAClB,aAAa;EACb,gBAAgB;EAChB,aAAa;EACb,IAAI;EACJ,cAAc;EACd,UAAU;IACR,OAAO;IACP,aAAa;;EAEf,eAAe;IACb,cAAc;IACd,iBAAiB;;EAEnB,WAAW;IACT,QAAQ;IACR,gBAAgB;IAChB,KAAK;IACL,eAAe;;EAEjB,iBAAiB;IACf,OAAO;IACP,UAAU;IACV,UAAU;IACV,aAAa;IACb,kBAAkB;IAClB,kBAAkB;IAClB,eAAe;MACb,cAAc;MACd,SAAS;MACT,QAAQ;;IAEV,SAAS;IACT,gBAAgB;;EAElB,oBAAoB;IAClB,OAAO;IACP,aACE;;EAEJ,iBAAiB;IACf,OAAO;IACP,cAAc;IACd,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,gBAAgB;IAChB,gBAAgB;IAChB,eAAe;IACf,aAAa;IACb,mBAAmB;IACnB,qBAAqB;IACrB,mBAAmB;IACnB,kBAAkB;IAClB,SAAS;;EAEX,iBAAiB;IACf,mBAAmB;MACjB,OAAO;MACP,aACE;MACF,cAAc;MACd,kBAAkB;;IAEpB,eAAe;MACb,OAAO;MACP,QAAQ;MACR,eACE;MACF,0BAA0B;MAC1B,UAAU;MACV,gBAAgB;MAChB,YAAY;;;EAGhB,cAAc;IACZ,OAAO;IACP,gBAAgB;IAChB,aAAa;IACb,cAAc;IACd,kBAAkB;;EAEpB,WAAW;IACT,OAAO;IACP,UAAU;IACV,YAAY;IACZ,YAAY;;;AAIhB,IAAA,aAAe;", "names": []}