{"version": 3, "sources": ["../../thirdweb/src/react/web/wallets/shared/locale/ja.ts"], "sourcesContent": ["import type { InAppWalletLocale } from \"./types.js\";\nexport default {\n  signInWithGoogle: \"Google\",\n  signInWithFacebook: \"Facebook\",\n  signInWithApple: \"Apple\",\n  signInWithDiscord: \"Discord\",\n  emailPlaceholder: \"メールアドレス\",\n  submitEmail: \"続行\",\n  signIn: \"サインイン\",\n  or: \"または\",\n  emailRequired: \"メールアドレスは必須です\",\n  invalidEmail: \"無効なメールアドレス\",\n  maxAccountsExceeded:\n    \"アカウントの最大数を超えました。アプリ開発者に通知してください。\",\n  socialLoginScreen: {\n    title: \"サインイン\",\n    instruction: \"ポップアップでアカウントにサインインしてください\",\n    failed: \"サインインに失敗しました\",\n    retry: \"再試行\",\n  },\n  emailLoginScreen: {\n    title: \"サインイン\",\n    enterCodeSendTo: \"送信された確認コードを入力してください\",\n    newDeviceDetected: \"新しいデバイスが検出されました\",\n    enterRecoveryCode:\n      \"初回サインアップ時に送信されたリカバリーコードを入力してください\",\n    invalidCode: \"無効な確認コード\",\n    invalidCodeOrRecoveryCode: \"無効な確認コードまたはリカバリーコード\",\n    verify: \"確認\",\n    failedToSendCode: \"確認コードの送信に失敗しました\",\n    sendingCode: \"確認コードを送信中\",\n    resendCode: \"確認コードを再送\",\n  },\n  createPassword: {\n    title: \"パスワードを作成\",\n    instruction:\n      \"アカウントのパスワードを設定してください。新しいデバイスから接続する際にこのパスワードが必要です。\",\n    saveInstruction: \"必ず保存してください\",\n    inputPlaceholder: \"パスワードを入力してください\",\n    confirmation: \"パスワードを保存しました\",\n    submitButton: \"パスワードを設定\",\n    failedToSetPassword: \"パスワードの設定に失敗しました\",\n  },\n  enterPassword: {\n    title: \"パスワードを入力\",\n    instruction: \"アカウントのパスワードを入力してください\",\n    inputPlaceholder: \"パスワードを入力してください\",\n    submitButton: \"確認\",\n    wrongPassword: \"パスワードが間違っています\",\n  },\n  signInWithEmail: \"メールでサインイン\",\n  invalidPhone: \"無効な電話番号\",\n  phonePlaceholder: \"電話番号\",\n  signInWithPhone: \"電話番号でサインイン\",\n  phoneRequired: \"電話番号は必須です\",\n  passkey: \"パスキー\",\n  linkWallet: \"ウォレットをリンクする\",\n  loginAsGuest: \"ゲストとしてログイン\",\n  signInWithWallet: \"ウォレットでログイン\",\n} satisfies InAppWalletLocale;\n"], "mappings": ";;;AACA,IAAA,aAAe;EACb,kBAAkB;EAClB,oBAAoB;EACpB,iBAAiB;EACjB,mBAAmB;EACnB,kBAAkB;EAClB,aAAa;EACb,QAAQ;EACR,IAAI;EACJ,eAAe;EACf,cAAc;EACd,qBACE;EACF,mBAAmB;IACjB,OAAO;IACP,aAAa;IACb,QAAQ;IACR,OAAO;;EAET,kBAAkB;IAChB,OAAO;IACP,iBAAiB;IACjB,mBAAmB;IACnB,mBACE;IACF,aAAa;IACb,2BAA2B;IAC3B,QAAQ;IACR,kBAAkB;IAClB,aAAa;IACb,YAAY;;EAEd,gBAAgB;IACd,OAAO;IACP,aACE;IACF,iBAAiB;IACjB,kBAAkB;IAClB,cAAc;IACd,cAAc;IACd,qBAAqB;;EAEvB,eAAe;IACb,OAAO;IACP,aAAa;IACb,kBAAkB;IAClB,cAAc;IACd,eAAe;;EAEjB,iBAAiB;EACjB,cAAc;EACd,kBAAkB;EAClB,iBAAiB;EACjB,eAAe;EACf,SAAS;EACT,YAAY;EACZ,cAAc;EACd,kBAAkB;;", "names": []}