{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/org.talkapp/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"org.talkapp\",\n  name: \"T+ Wallet \",\n  homepage: \"https://www.talkapp.org/\",\n  image_id: \"c08ff28f-5a52-4bf2-e63a-205905fd5800\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/hk/app/talk-%E5%8A%A0%E5%AF%86%E8%B2%A8%E5%B9%A3%E4%BA%A4%E6%98%93%E5%8F%8Aai%E8%81%8A%E5%A4%A9%E9%80%9A%E8%A8%8A%E8%BB%9F%E4%BB%B6/id1547227377\",\n    android:\n      \"https://play.google.com/store/apps/details?id=org.talkapp&hl=en&gl=US\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"talkapp://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}