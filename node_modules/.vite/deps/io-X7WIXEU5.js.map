{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.enjin/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.enjin\",\n  name: \"Enjin Wallet\",\n  homepage: \"https://enjin.io/products/wallet\",\n  image_id: \"add9626b-a5fa-4c12-178c-e5584e6dcd00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/enjin-nft-crypto-wallet/id1349078375\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.enjin.mobile.wallet\",\n    mac: \"\",\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"enjinwallet://\",\n    universal: \"https://deeplink.wallet.enjin.io/\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}