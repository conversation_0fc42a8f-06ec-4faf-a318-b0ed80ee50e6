import {
  J,
  w
} from "./chunk-X2AGAVBN.js";
import "./chunk-OS7ZSSJM.js";

// node_modules/@thirdweb-dev/insight/dist/esm/client/client.gen.js
var client = J(w());

// node_modules/@thirdweb-dev/insight/dist/esm/client/sdk.gen.js
var getV1Webhooks = (options) => {
  return ((options == null ? void 0 : options.client) ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/webhooks",
    ...options
  });
};
var postV1Webhooks = (options) => {
  return ((options == null ? void 0 : options.client) ?? client).post({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/webhooks",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options == null ? void 0 : options.headers
    }
  });
};
var deleteV1WebhooksByWebhookId = (options) => {
  return (options.client ?? client).delete({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/webhooks/{webhook_id}",
    ...options
  });
};
var patchV1WebhooksByWebhookId = (options) => {
  return (options.client ?? client).patch({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/webhooks/{webhook_id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options == null ? void 0 : options.headers
    }
  });
};
var postV1WebhooksByWebhookIdVerify = (options) => {
  return (options.client ?? client).post({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/webhooks/{webhook_id}/verify",
    ...options
  });
};
var postV1WebhooksByWebhookIdResendOtp = (options) => {
  return (options.client ?? client).post({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/webhooks/{webhook_id}/resend-otp",
    ...options
  });
};
var postV1WebhooksTest = (options) => {
  return ((options == null ? void 0 : options.client) ?? client).post({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/webhooks/test",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options == null ? void 0 : options.headers
    }
  });
};
var getV1Events = (options) => {
  return ((options == null ? void 0 : options.client) ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/events",
    ...options
  });
};
var getV1EventsByContractAddress = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/events/{contractAddress}",
    ...options
  });
};
var getV1EventsByContractAddressBySignature = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/events/{contractAddress}/{signature}",
    ...options
  });
};
var getV1Transactions = (options) => {
  return ((options == null ? void 0 : options.client) ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/transactions",
    ...options
  });
};
var getV1TransactionsByContractAddress = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/transactions/{contractAddress}",
    ...options
  });
};
var getV1TransactionsByContractAddressBySignature = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/transactions/{contractAddress}/{signature}",
    ...options
  });
};
var getV1TokensTransfersTransactionByTransactionHash = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/tokens/transfers/transaction/{transaction_hash}",
    ...options
  });
};
var getV1TokensTransfersByContractAddress = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/tokens/transfers/{contract_address}",
    ...options
  });
};
var getV1TokensTransfers = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/tokens/transfers",
    ...options
  });
};
var getV1TokensErc20ByOwnerAddress = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/tokens/erc20/{ownerAddress}",
    ...options
  });
};
var getV1TokensErc721ByOwnerAddress = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/tokens/erc721/{ownerAddress}",
    ...options
  });
};
var getV1TokensErc1155ByOwnerAddress = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/tokens/erc1155/{ownerAddress}",
    ...options
  });
};
var getV1TokensPriceSupported = (options) => {
  return ((options == null ? void 0 : options.client) ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/tokens/price/supported",
    ...options
  });
};
var getV1TokensPrice = (options) => {
  return ((options == null ? void 0 : options.client) ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/tokens/price",
    ...options
  });
};
var getV1TokensLookup = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/tokens/lookup",
    ...options
  });
};
var getV1ResolveByInput = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/resolve/{input}",
    ...options
  });
};
var getV1Blocks = (options) => {
  return ((options == null ? void 0 : options.client) ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/blocks",
    ...options
  });
};
var getV1ContractsAbiByContractAddress = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/contracts/abi/{contractAddress}",
    ...options
  });
};
var getV1ContractsMetadataByContractAddress = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/contracts/metadata/{contractAddress}",
    ...options
  });
};
var postV1DecodeByContractAddress = (options) => {
  return (options.client ?? client).post({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/decode/{contractAddress}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options == null ? void 0 : options.headers
    }
  });
};
var getV1NftsBalanceByOwnerAddress = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/nfts/balance/{ownerAddress}",
    ...options
  });
};
var getV1NftsCollectionsByContractAddress = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/nfts/collections/{contract_address}",
    ...options
  });
};
var getV1Nfts = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/nfts",
    ...options
  });
};
var getV1NftsOwnersByContractAddress = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/nfts/owners/{contract_address}",
    ...options
  });
};
var getV1NftsOwnersByContractAddressByTokenId = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/nfts/owners/{contract_address}/{token_id}",
    ...options
  });
};
var getV1NftsTransfers = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/nfts/transfers",
    ...options
  });
};
var getV1NftsTransfersTransactionByTransactionHash = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/nfts/transfers/transaction/{transaction_hash}",
    ...options
  });
};
var getV1NftsTransfersByContractAddress = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/nfts/transfers/{contract_address}",
    ...options
  });
};
var getV1NftsByContractAddress = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/nfts/{contract_address}",
    ...options
  });
};
var getV1NftsTransfersByContractAddressByTokenId = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/nfts/transfers/{contract_address}/{token_id}",
    ...options
  });
};
var getV1NftsByContractAddressByTokenId = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/nfts/{contract_address}/{token_id}",
    ...options
  });
};
var getV1NftsMetadataRefreshByContractAddress = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/nfts/metadata/refresh/{contract_address}",
    ...options
  });
};
var getV1NftsMetadataRefreshByContractAddressByTokenId = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/nfts/metadata/refresh/{contract_address}/{token_id}",
    ...options
  });
};
var getV1WalletsByWalletAddressTransactions = (options) => {
  return (options.client ?? client).get({
    security: [
      {
        name: "x-client-id",
        type: "apiKey"
      },
      {
        scheme: "bearer",
        type: "http"
      },
      {
        in: "query",
        name: "clientId",
        type: "apiKey"
      }
    ],
    url: "/v1/wallets/{wallet_address}/transactions",
    ...options
  });
};

// node_modules/@thirdweb-dev/insight/dist/esm/configure.js
function configure(options) {
  client.setConfig({
    headers: {
      ...options.clientId && { "x-client-id": options.clientId },
      ...options.secretKey && { "x-api-key": options.secretKey }
    },
    ...options.override ?? {}
  });
}
export {
  configure,
  deleteV1WebhooksByWebhookId,
  getV1Blocks,
  getV1ContractsAbiByContractAddress,
  getV1ContractsMetadataByContractAddress,
  getV1Events,
  getV1EventsByContractAddress,
  getV1EventsByContractAddressBySignature,
  getV1Nfts,
  getV1NftsBalanceByOwnerAddress,
  getV1NftsByContractAddress,
  getV1NftsByContractAddressByTokenId,
  getV1NftsCollectionsByContractAddress,
  getV1NftsMetadataRefreshByContractAddress,
  getV1NftsMetadataRefreshByContractAddressByTokenId,
  getV1NftsOwnersByContractAddress,
  getV1NftsOwnersByContractAddressByTokenId,
  getV1NftsTransfers,
  getV1NftsTransfersByContractAddress,
  getV1NftsTransfersByContractAddressByTokenId,
  getV1NftsTransfersTransactionByTransactionHash,
  getV1ResolveByInput,
  getV1TokensErc1155ByOwnerAddress,
  getV1TokensErc20ByOwnerAddress,
  getV1TokensErc721ByOwnerAddress,
  getV1TokensLookup,
  getV1TokensPrice,
  getV1TokensPriceSupported,
  getV1TokensTransfers,
  getV1TokensTransfersByContractAddress,
  getV1TokensTransfersTransactionByTransactionHash,
  getV1Transactions,
  getV1TransactionsByContractAddress,
  getV1TransactionsByContractAddressBySignature,
  getV1WalletsByWalletAddressTransactions,
  getV1Webhooks,
  patchV1WebhooksByWebhookId,
  postV1DecodeByContractAddress,
  postV1Webhooks,
  postV1WebhooksByWebhookIdResendOtp,
  postV1WebhooksByWebhookIdVerify,
  postV1WebhooksTest
};
//# sourceMappingURL=thirdweb-RRHPM3WK.js.map
