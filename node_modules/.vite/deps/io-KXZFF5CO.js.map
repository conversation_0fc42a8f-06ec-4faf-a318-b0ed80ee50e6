{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.trinity-tech/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.trinity-tech\",\n  name: \"Essentials\",\n  homepage: \"https://www.trinity-tech.io/essentials\",\n  image_id: \"058878f4-7364-4e01-434f-2cc09a15cf00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/elastos-essentials/id1568931743\",\n    android:\n      \"https://play.google.com/store/apps/details?id=org.elastos.essentials.app\",\n    mac: \"\",\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: null,\n    universal: \"https://essentials.web3essentials.io\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}