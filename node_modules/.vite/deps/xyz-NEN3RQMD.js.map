{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/xyz.talisman/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"xyz.talisman\",\n  name: \"Talisman Wallet\",\n  homepage: \"https://talisman.xyz\",\n  image_id: \"ba290222-c3f3-4194-23bf-28ba7587af00\",\n  app: {\n    browser: null,\n    ios: null,\n    android: null,\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome:\n      \"https://chromewebstore.google.com/detail/talisman-wallet/fijngjgcjhjmmpcmkeiomlglpeiijkld\",\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: \"xyz.talisman\",\n  mobile: {\n    native: null,\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QACE;IACF,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}