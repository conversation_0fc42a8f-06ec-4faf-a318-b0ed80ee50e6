{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/it.airgap/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"it.airgap\",\n  name: \"AirGap Wallet\",\n  homepage: \"https://airgap.it\",\n  image_id: \"76bfe8cd-cf3f-4341-c33c-60da01065000\",\n  app: {\n    browser: \"https://wallet.airgap.it\",\n    ios: \"https://itunes.apple.com/us/app/airgap-wallet/id1420996542?l=de&ls=1&mt=8\",\n    android: \"https://play.google.com/store/apps/details?id=it.airgap.wallet\",\n    mac: \"https://github.com/airgap-it/airgap-wallet/releases\",\n    windows: \"https://github.com/airgap-it/airgap-wallet/releases\",\n    linux: \"https://github.com/airgap-it/airgap-wallet/releases\",\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"airgap-wallet://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: \"https://wallet.airgap.it\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}