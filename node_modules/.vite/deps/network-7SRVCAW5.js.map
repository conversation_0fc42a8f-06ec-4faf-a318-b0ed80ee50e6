{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/network.blackfort/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"network.blackfort\",\n  name: \"BlackFort Wallet\",\n  homepage: \"https://blackfort.network\",\n  image_id: \"f2cf0909-3e1e-4f67-8c3f-2b69f7a5eb00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/pl/app/blackfort-wallet/id6447954137\",\n    android:\n      \"https://play.google.com/store/apps/details?id=exchange.blackfort.blackfortwallet&hl=en_US\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"blackfortwc://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}