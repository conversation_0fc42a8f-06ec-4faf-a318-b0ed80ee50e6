{"version": 3, "sources": ["../../thirdweb/src/react/web/wallets/shared/locale/vi.ts"], "sourcesContent": ["import type { InAppWalletLocale } from \"./types.js\";\n\nexport default {\n  signInWithGoogle: \"Google\",\n  signInWithFacebook: \"Facebook\",\n  signInWithApple: \"Apple\",\n  signInWithDiscord: \"Discord\",\n  emailPlaceholder: \"Địa chỉ email\",\n  submitEmail: \"Tiếp tục\",\n  signIn: \"Đăng nhập\",\n  or: \"Hoặc\",\n  emailRequired: \"Vui lòng nhập địa chỉ email\",\n  invalidEmail: \"Địa chỉ email không hợp lệ\",\n  maxAccountsExceeded:\n    \"Số tài khoản vượt quá mức quy định. Vui lòng liên hệ với lập trình viên của ứng dụng này\",\n  socialLoginScreen: {\n    title: \"Đăng nhập\",\n    instruction: \"Đăng nhập ở cửa sổ hiện lên\",\n    failed: \"<PERSON><PERSON><PERSON> nhập thất bại\",\n    retry: \"Thử lại\",\n  },\n  emailLoginScreen: {\n    title: \"<PERSON>ăng nhập\",\n    enterCodeSendTo: \"Nhập mã xác nhận được gửi tới\",\n    newDeviceDetected: \"Bạn đang đăng nhập trên một thiết bị mới\",\n    enterRecoveryCode:\n      \"Nhập mã khôi phục mà bạn nhận được khi đăng nhập lần đầu\",\n    invalidCode: \"Mã xác nhận không hợp lệ\",\n    invalidCodeOrRecoveryCode: \"Mã xác nhận hoặc mã khôi phục bị sai\",\n    verify: \"Xác nhận\",\n    failedToSendCode: \"Gửi mã xác nhận thất bại\",\n    sendingCode: \"Đang gửi mã xác nhận\",\n    resendCode: \"Gửi lại mã xác nhận\",\n  },\n  createPassword: {\n    title: \"Tạo mật khẩu\",\n    instruction:\n      \"Đặt mật khẩu cho tài khoản của bạn. Bạn cần mật khẩu này mỗi khi đăng nhập trên một thiết bị mới\",\n    saveInstruction: \"Vui lòng sao lưu mật khẩu\",\n    inputPlaceholder: \"Nhập mật khẩu của bạn\",\n    confirmation: \"Tôi đã sao lưu mật khẩu\",\n    submitButton: \"Đặt mật khẩu\",\n    failedToSetPassword: \"Đặt mật khẩu thất bại\",\n  },\n  enterPassword: {\n    title: \"Nhập mật khẩu\",\n    instruction: \"Nhập mật khẩu cho tài khoản của bạn\",\n    inputPlaceholder: \"Nhập mật khẩu của bạn\",\n    submitButton: \"Xác nhân\",\n    wrongPassword: \"Sai mật khẩu\",\n  },\n  signInWithEmail: \"Đăng nhập bằng email\",\n  invalidPhone: \"Số điện thoại không hợp lệ\",\n  phonePlaceholder: \"Số điện thoại\",\n  signInWithPhone: \"Đăng nhập bằng số điện thoại\",\n  phoneRequired: \"Vui lòng nhập số điện thoại\",\n  passkey: \"Passkey\",\n  linkWallet: \"Link a Wallet\",\n  loginAsGuest: \"Đăng nhập với tư cách khách\",\n  signInWithWallet: \"Đăng nhập bằng Wallet\",\n} satisfies InAppWalletLocale;\n"], "mappings": ";;;AAEA,IAAA,aAAe;EACb,kBAAkB;EAClB,oBAAoB;EACpB,iBAAiB;EACjB,mBAAmB;EACnB,kBAAkB;EAClB,aAAa;EACb,QAAQ;EACR,IAAI;EACJ,eAAe;EACf,cAAc;EACd,qBACE;EACF,mBAAmB;IACjB,OAAO;IACP,aAAa;IACb,QAAQ;IACR,OAAO;;EAET,kBAAkB;IAChB,OAAO;IACP,iBAAiB;IACjB,mBAAmB;IACnB,mBACE;IACF,aAAa;IACb,2BAA2B;IAC3B,QAAQ;IACR,kBAAkB;IAClB,aAAa;IACb,YAAY;;EAEd,gBAAgB;IACd,OAAO;IACP,aACE;IACF,iBAAiB;IACjB,kBAAkB;IAClB,cAAc;IACd,cAAc;IACd,qBAAqB;;EAEvB,eAAe;IACb,OAAO;IACP,aAAa;IACb,kBAAkB;IAClB,cAAc;IACd,eAAe;;EAEjB,iBAAiB;EACjB,cAAc;EACd,kBAAkB;EAClB,iBAAiB;EACjB,eAAe;EACf,SAAS;EACT,YAAY;EACZ,cAAc;EACd,kBAAkB;;", "names": []}