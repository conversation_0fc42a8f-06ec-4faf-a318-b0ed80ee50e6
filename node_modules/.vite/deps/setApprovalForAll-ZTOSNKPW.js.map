{"version": 3, "sources": ["../../thirdweb/src/extensions/erc721/__generated__/IERC721A/write/setApprovalForAll.ts"], "sourcesContent": ["import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport type {\n  BaseTransactionOptions,\n  WithOverrides,\n} from \"../../../../../transaction/types.js\";\nimport { prepareContractCall } from \"../../../../../transaction/prepare-contract-call.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { once } from \"../../../../../utils/promise/once.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"setApprovalForAll\" function.\n */\nexport type SetApprovalForAllParams = WithOverrides<{\n  operator: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"operator\" }>;\n  approved: AbiParameterToPrimitiveType<{ type: \"bool\"; name: \"_approved\" }>;\n}>;\n\nexport const FN_SELECTOR = \"0xa22cb465\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"address\",\n    name: \"operator\",\n  },\n  {\n    type: \"bool\",\n    name: \"_approved\",\n  },\n] as const;\nconst FN_OUTPUTS = [] as const;\n\n/**\n * Checks if the `setApprovalForAll` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `setApprovalForAll` method is supported.\n * @extension ERC721\n * @example\n * ```ts\n * import { isSetApprovalForAllSupported } from \"thirdweb/extensions/erc721\";\n *\n * const supported = isSetApprovalForAllSupported([\"0x...\"]);\n * ```\n */\nexport function isSetApprovalForAllSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"setApprovalForAll\" function.\n * @param options - The options for the setApprovalForAll function.\n * @returns The encoded ABI parameters.\n * @extension ERC721\n * @example\n * ```ts\n * import { encodeSetApprovalForAllParams } from \"thirdweb/extensions/erc721\";\n * const result = encodeSetApprovalForAllParams({\n *  operator: ...,\n *  approved: ...,\n * });\n * ```\n */\nexport function encodeSetApprovalForAllParams(\n  options: SetApprovalForAllParams,\n) {\n  return encodeAbiParameters(FN_INPUTS, [options.operator, options.approved]);\n}\n\n/**\n * Encodes the \"setApprovalForAll\" function into a Hex string with its parameters.\n * @param options - The options for the setApprovalForAll function.\n * @returns The encoded hexadecimal string.\n * @extension ERC721\n * @example\n * ```ts\n * import { encodeSetApprovalForAll } from \"thirdweb/extensions/erc721\";\n * const result = encodeSetApprovalForAll({\n *  operator: ...,\n *  approved: ...,\n * });\n * ```\n */\nexport function encodeSetApprovalForAll(options: SetApprovalForAllParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeSetApprovalForAllParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Prepares a transaction to call the \"setApprovalForAll\" function on the contract.\n * @param options - The options for the \"setApprovalForAll\" function.\n * @returns A prepared transaction object.\n * @extension ERC721\n * @example\n * ```ts\n * import { sendTransaction } from \"thirdweb\";\n * import { setApprovalForAll } from \"thirdweb/extensions/erc721\";\n *\n * const transaction = setApprovalForAll({\n *  contract,\n *  operator: ...,\n *  approved: ...,\n *  overrides: {\n *    ...\n *  }\n * });\n *\n * // Send the transaction\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function setApprovalForAll(\n  options: BaseTransactionOptions<\n    | SetApprovalForAllParams\n    | {\n        asyncParams: () => Promise<SetApprovalForAllParams>;\n      }\n  >,\n) {\n  const asyncOptions = once(async () => {\n    return \"asyncParams\" in options ? await options.asyncParams() : options;\n  });\n\n  return prepareContractCall({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: async () => {\n      const resolvedOptions = await asyncOptions();\n      return [resolvedOptions.operator, resolvedOptions.approved] as const;\n    },\n    value: async () => (await asyncOptions()).overrides?.value,\n    accessList: async () => (await asyncOptions()).overrides?.accessList,\n    gas: async () => (await asyncOptions()).overrides?.gas,\n    gasPrice: async () => (await asyncOptions()).overrides?.gasPrice,\n    maxFeePerGas: async () => (await asyncOptions()).overrides?.maxFeePerGas,\n    maxPriorityFeePerGas: async () =>\n      (await asyncOptions()).overrides?.maxPriorityFeePerGas,\n    nonce: async () => (await asyncOptions()).overrides?.nonce,\n    extraGas: async () => (await asyncOptions()).overrides?.extraGas,\n    erc20Value: async () => (await asyncOptions()).overrides?.erc20Value,\n    authorizationList: async () =>\n      (await asyncOptions()).overrides?.authorizationList,\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBO,IAAM,cAAc;AAC3B,IAAM,YAAY;EAChB;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;;AAGV,IAAM,aAAa,CAAA;AAcb,SAAU,6BAA6B,oBAA4B;AACvE,SAAO,aAAa;IAClB;IACA,QAAQ,CAAC,aAAa,WAAW,UAAU;GAC5C;AACH;AAgBM,SAAU,8BACd,SAAgC;AAEhC,SAAO,oBAAoB,WAAW,CAAC,QAAQ,UAAU,QAAQ,QAAQ,CAAC;AAC5E;AAgBM,SAAU,wBAAwB,SAAgC;AAGtE,SAAQ,cACN,8BAA8B,OAAO,EAAE,MACrC,CAAC;AAEP;AAyBM,SAAU,kBACd,SAKC;AAED,QAAM,eAAe,KAAK,YAAW;AACnC,WAAO,iBAAiB,UAAU,MAAM,QAAQ,YAAW,IAAK;EAClE,CAAC;AAED,SAAO,oBAAoB;IACzB,UAAU,QAAQ;IAClB,QAAQ,CAAC,aAAa,WAAW,UAAU;IAC3C,QAAQ,YAAW;AACjB,YAAM,kBAAkB,MAAM,aAAY;AAC1C,aAAO,CAAC,gBAAgB,UAAU,gBAAgB,QAAQ;IAC5D;IACA,OAAO,YAAS;AAlIpB;AAkIwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,YAAY,YAAS;AAnIzB;AAmI6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,KAAK,YAAS;AApIlB;AAoIsB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACnD,UAAU,YAAS;AArIvB;AAqI2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,cAAc,YAAS;AAtI3B;AAsI+B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC5D,sBAAsB,YAAS;AAvInC;AAwIO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACpC,OAAO,YAAS;AAzIpB;AAyIwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,UAAU,YAAS;AA1IvB;AA0I2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,YAAY,YAAS;AA3IzB;AA2I6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,mBAAmB,YAAS;AA5IhC;AA6IO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;GACrC;AACH;", "names": []}