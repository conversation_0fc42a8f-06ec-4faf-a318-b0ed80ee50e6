{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/jp.co.rakuten-wallet/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"jp.co.rakuten-wallet\",\n  name: \"Rakuten Wallet\",\n  homepage: \"https://www.rakuten-wallet.co.jp/web3/\",\n  image_id: \"a7b5c4b1-8b55-4b6c-af68-4b2786480600\",\n  app: {\n    browser: \"https://www.rakuten-wallet.co.jp/\",\n    ios: \"https://apps.apple.com/jp/app/id6504903632\",\n    android:\n      \"https://play.google.com/store/apps/details?id=jp.co.rakuten.web3wallet.crypto\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"rakutenwalletweb3://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}