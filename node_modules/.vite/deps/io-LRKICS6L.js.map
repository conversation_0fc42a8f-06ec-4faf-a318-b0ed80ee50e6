{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.walletverse/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.walletverse\",\n  name: \"Walletverse\",\n  homepage: \"https://walletverse.io/\",\n  image_id: \"ca91138e-0546-4cfe-071d-2181241dc600\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/wallet-verse-defi-buy-crypto/id6462672660\",\n    android: \"https://play.google.com/store/apps/details?id=com.walletverse\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"walletverse://\",\n    universal: \"https://walletverse.io\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}