{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.loopring.wallet/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.loopring.wallet\",\n  name: \"Loopring\",\n  homepage: \"https://loopring.io/\",\n  image_id: \"2103feda-4fc8-4635-76a7-02a4ed998000\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/loopring-smart-wallet/id1550921126\",\n    android:\n      \"https://play.google.com/store/apps/details?id=loopring.defi.wallet\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: \"io.loopring.wallet\",\n  mobile: {\n    native: \"loopring://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}