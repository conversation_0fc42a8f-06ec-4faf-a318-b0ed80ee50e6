{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/net.myrenegade/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"net.myrenegade\",\n  name: \"Rene<PERSON>\",\n  homepage: \"https://www.myrenegade.net\",\n  image_id: \"6ce2caa3-c597-445a-b61f-0b46b5c15000\",\n  app: {\n    browser: \"https://wallet.myrenegade.net\",\n    ios: \"https://apps.apple.com/us/app/myrenegade/id1670346221\",\n    android: \"https://play.google.com/store/apps/details?id=com.app.renegade\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"renegade-web3wallet://\",\n    universal: \"https://webapp.myrenegade.net\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}