import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/us.binance/index.js
var wallet = {
  id: "us.binance",
  name: "Binance.US",
  homepage: "https://binance.us",
  image_id: "48aa1a7d-c5fe-4ad6-c2f2-e5684b296900",
  app: {
    browser: null,
    ios: "https://itunes.apple.com/app/id1492670702",
    android: "https://play.google.com/store/apps/details?id=com.binance.us",
    mac: "",
    windows: null,
    linux: null,
    chrome: null,
    firefox: null,
    safari: null,
    edge: null,
    opera: null
  },
  rdns: null,
  mobile: {
    native: "bncus://binance.us",
    universal: "https://binance.us/universal_JHHGDSKDJ"
  },
  desktop: {
    native: null,
    universal: null
  }
};
export {
  wallet
};
//# sourceMappingURL=us-RQM7D4H7.js.map
