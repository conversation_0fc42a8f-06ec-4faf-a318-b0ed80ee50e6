import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/react/web/wallets/injected/locale/ja.js
var injectedWalletLocale = (wallet) => ({
  connectionScreen: {
    inProgress: "確認を待っています",
    failed: "接続に失敗しました",
    instruction: `${wallet}で接続リクエストを承認してください`,
    retry: "再試行"
  },
  getStartedScreen: {
    instruction: `QRコードをスキャンして${wallet}アプリをダウンロードしてください`
  },
  scanScreen: {
    instruction: `${wallet}アプリでQRコードをスキャンして接続してください`
  },
  getStartedLink: `${wallet}をお持ちではありませんか？`,
  download: {
    chrome: "Chrome拡張機能をダウンロード",
    android: "Google Playでダウンロード",
    iOS: "App Storeでダウンロード"
  }
});
var ja_default = injectedWalletLocale;
export {
  ja_default as default
};
//# sourceMappingURL=ja-64RV25OJ.js.map
