{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.bladewallet/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.bladewallet\",\n  name: \"Blade Wallet\",\n  homepage: \"https://bladewallet.io\",\n  image_id: \"8fa87652-b043-4992-3a45-78e438d1cd00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/app/apple-store/id1623849951\",\n    android:\n      \"https://play.google.com/store/apps/details?id=org.bladelabs.wallet\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome:\n      \"https://chrome.google.com/webstore/detail/blade-%E2%80%93-hedera-web3-digit/abogmiocnneedmmepnohnhlijcjpcifd\",\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"org.bladelabs.bladewallet://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: \"https://welcome.bladewallet.io/\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QACE;IACF,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}