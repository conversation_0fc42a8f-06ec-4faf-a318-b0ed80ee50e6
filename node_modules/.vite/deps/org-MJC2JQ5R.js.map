{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/org.hot-labs/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"org.hot-labs\",\n  name: \"HOT Wallet\",\n  homepage: \"https://hot-labs.org/wallet\",\n  image_id: \"809867ce-345f-4180-033a-165019d4c700\",\n  app: {\n    browser: \"https://t.me/hot_wallet/app\",\n    ios: \"https://apps.apple.com/us/app/hot-wallet/id6740916148\",\n    android:\n      \"https://play.google.com/store/apps/details?id=app.herewallet.hot&hl=en_US\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome:\n      \"https://chromewebstore.google.com/detail/hot-wallet/mpeengabcnhhjjgleiodimegnkpcenbk?pli=1\",\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"hotwallet://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: \"https://t.me/hot_wallet/app\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QACE;IACF,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}