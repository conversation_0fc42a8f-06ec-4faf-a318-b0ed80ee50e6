{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/org.bytebank/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"org.bytebank\",\n  name: \"ByteBank\",\n  homepage: \"https://www.bytebank.org/\",\n  image_id: \"bc7aacd6-b2e2-4146-7d21-06e0c5d44f00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/sg/app/hideout-wallet/id1620315192?l=zh\",\n    android: \"https://play.google.com/store/apps/details?id=com.hideout.wallet\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"hideoutWallet://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}