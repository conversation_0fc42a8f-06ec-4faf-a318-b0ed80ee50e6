{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.ethermail/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.ethermail\",\n  name: \"EtherMail\",\n  homepage: \"https://ethermail.io\",\n  image_id: \"7f3205c6-6051-4cdb-8ef8-84334a7c7f00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/ethermail/id6451305966\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.ethermail.ethermail_android\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"ethermail://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}