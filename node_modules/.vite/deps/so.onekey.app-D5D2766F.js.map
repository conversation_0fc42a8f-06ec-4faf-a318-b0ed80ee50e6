{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/so.onekey.app.wallet/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"so.onekey.app.wallet\",\n  name: \"<PERSON><PERSON><PERSON>\",\n  homepage: \"https://onekey.so\",\n  image_id: \"2067c771-93e8-4b32-b388-b2a0e1d4dc00\",\n  app: {\n    browser: \"https://onekey.so\",\n    ios: \"https://apps.apple.com/us/app/onekey-open-source-wallet/id1609559473\",\n    android:\n      \"https://play.google.com/store/apps/details?id=so.onekey.app.wallet&hl=en_US&gl=US\",\n    mac: \"https://github.com/OneKeyHQ/app-monorepo/releases\",\n    windows: \"https://github.com/OneKeyHQ/app-monorepo/releases\",\n    linux: \"https://github.com/OneKeyHQ/app-monorepo/releases\",\n    chrome:\n      \"https://chrome.google.com/webstore/detail/onekey/jnmbobjmhlngoefaiojfljckilhhlhcj\",\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: \"so.onekey.app.wallet\",\n  mobile: {\n    native: \"onekey-wallet://\",\n    universal: \"https://app.onekey.so/wc/connect\",\n  },\n  desktop: {\n    native: \"onekey-wallet://\",\n    universal: \"https://app.onekey.so/wc/connect\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QACE;IACF,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}