{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/network.gridlock/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"network.gridlock\",\n  name: \"Gridlock Wallet\",\n  homepage: \"https://gridlock.network/\",\n  image_id: \"471e6f61-b95a-453c-670c-029ef3b2bd00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/app/gridlock-secure-crypto-wallet/id1567057330\",\n    android:\n      \"https://play.google.com/store/apps/details?id=network.gridlock.AppGridlock\",\n    mac: \"\",\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"network.gridlock.AppGridlock://\",\n    universal: \"https://gridlock.page.link/Fihx\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}