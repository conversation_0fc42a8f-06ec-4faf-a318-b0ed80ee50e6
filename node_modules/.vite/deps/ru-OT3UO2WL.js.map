{"version": 3, "sources": ["../../thirdweb/src/react/web/wallets/smartWallet/locale/ru.ts"], "sourcesContent": ["import type { SmartWalletLocale } from \"./types.js\";\n\nconst smartWalletLocalRu: SmartWalletLocale = {\n  connecting: \"Подключение к смарт-аккаунту\",\n  failedToConnect: \"Не удалось подключиться к смарт-аккаунту\",\n  wrongNetworkScreen: {\n    title: \"Неверная сеть\",\n    subtitle: \"Ваш кошелек не подключен к нужной сети\",\n    failedToSwitch: \"Не удалось сменить сеть\",\n  },\n};\n\nexport default smartWalletLocalRu;\n"], "mappings": ";;;AAEA,IAAM,qBAAwC;EAC5C,YAAY;EACZ,iBAAiB;EACjB,oBAAoB;IAClB,OAAO;IACP,UAAU;IACV,gBAAgB;;;AAIpB,IAAA,aAAe;", "names": []}