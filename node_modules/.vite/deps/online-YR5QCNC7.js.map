{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/online.puzzle/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"online.puzzle\",\n  name: \"Puzzle Wallet\",\n  homepage: \"https://puzzle.online\",\n  image_id: \"08cb0a68-6271-4e25-90c3-bcc3c0226a00\",\n  app: {\n    browser: \"https://jigsaw-dev.puzzle.online\",\n    ios: \"https://apps.apple.com/au/app/puzzle-aleo-wallet/id6450268321\",\n    android: \"https://play.google.com/store/apps/details?id=online.puzzle\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome:\n      \"https://chromewebstore.google.com/detail/puzzle-aleo-wallet/fdchdcpieegfofnofhgdombfckhbcokj\",\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"puzzleapp://\",\n    universal: \"https://jigsaw-dev.puzzle.online/\",\n  },\n  desktop: {\n    native: null,\n    universal: \"https://walletconnect.puzzle.online\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QACE;IACF,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}