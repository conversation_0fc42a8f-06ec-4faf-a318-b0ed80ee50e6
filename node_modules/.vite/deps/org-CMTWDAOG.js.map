{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/org.bitizen/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"org.bitizen\",\n  name: \"Bitizen\",\n  homepage: \"https://bitizen.org/\",\n  image_id: \"75dd1471-77e9-4811-ce57-ec8fc980ec00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/bitizen-defi-web3-eth-wallet/id1598283542\",\n    android: \"https://play.google.com/store/apps/details?id=org.bitizen.wallet\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: \"https://bitizen.org/\",\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"bitizen://wallet/\",\n    universal: \"https://bitizen.org/wallet/\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}