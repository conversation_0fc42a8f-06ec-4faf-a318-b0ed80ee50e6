{"version": 3, "sources": ["../../thirdweb/src/extensions/erc721/__generated__/IERC721A/read/ownerOf.ts"], "sourcesContent": ["import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"ownerOf\" function.\n */\nexport type OwnerOfParams = {\n  tokenId: AbiParameterToPrimitiveType<{ type: \"uint256\"; name: \"tokenId\" }>;\n};\n\nexport const FN_SELECTOR = \"0x6352211e\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"uint256\",\n    name: \"tokenId\",\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"address\",\n  },\n] as const;\n\n/**\n * Checks if the `ownerOf` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `ownerOf` method is supported.\n * @extension ERC721\n * @example\n * ```ts\n * import { isOwnerOfSupported } from \"thirdweb/extensions/erc721\";\n * const supported = isOwnerOfSupported([\"0x...\"]);\n * ```\n */\nexport function isOwnerOfSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"ownerOf\" function.\n * @param options - The options for the ownerOf function.\n * @returns The encoded ABI parameters.\n * @extension ERC721\n * @example\n * ```ts\n * import { encodeOwnerOfParams } from \"thirdweb/extensions/erc721\";\n * const result = encodeOwnerOfParams({\n *  tokenId: ...,\n * });\n * ```\n */\nexport function encodeOwnerOfParams(options: OwnerOfParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.tokenId]);\n}\n\n/**\n * Encodes the \"ownerOf\" function into a Hex string with its parameters.\n * @param options - The options for the ownerOf function.\n * @returns The encoded hexadecimal string.\n * @extension ERC721\n * @example\n * ```ts\n * import { encodeOwnerOf } from \"thirdweb/extensions/erc721\";\n * const result = encodeOwnerOf({\n *  tokenId: ...,\n * });\n * ```\n */\nexport function encodeOwnerOf(options: OwnerOfParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeOwnerOfParams(options).slice(2)) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Decodes the result of the ownerOf function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC721\n * @example\n * ```ts\n * import { decodeOwnerOfResult } from \"thirdweb/extensions/erc721\";\n * const result = decodeOwnerOfResultResult(\"...\");\n * ```\n */\nexport function decodeOwnerOfResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"ownerOf\" function on the contract.\n * @param options - The options for the ownerOf function.\n * @returns The parsed result of the function call.\n * @extension ERC721\n * @example\n * ```ts\n * import { ownerOf } from \"thirdweb/extensions/erc721\";\n *\n * const result = await ownerOf({\n *  contract,\n *  tokenId: ...,\n * });\n *\n * ```\n */\nexport async function ownerOf(options: BaseTransactionOptions<OwnerOfParams>) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [options.tokenId],\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeO,IAAM,cAAc;AAC3B,IAAM,YAAY;EAChB;IACE,MAAM;IACN,MAAM;;;AAGV,IAAM,aAAa;EACjB;IACE,MAAM;;;AAeJ,SAAU,mBAAmB,oBAA4B;AAC7D,SAAO,aAAa;IAClB;IACA,QAAQ,CAAC,aAAa,WAAW,UAAU;GAC5C;AACH;AAeM,SAAU,oBAAoB,SAAsB;AACxD,SAAO,oBAAoB,WAAW,CAAC,QAAQ,OAAO,CAAC;AACzD;AAeM,SAAU,cAAc,SAAsB;AAGlD,SAAQ,cACN,oBAAoB,OAAO,EAAE,MAAM,CAAC;AACxC;AAaM,SAAU,oBAAoB,QAAW;AAC7C,SAAO,oBAAoB,YAAY,MAAM,EAAE,CAAC;AAClD;AAkBA,eAAsB,QAAQ,SAA8C;AAC1E,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAAC,aAAa,WAAW,UAAU;IAC3C,QAAQ,CAAC,QAAQ,OAAO;GACzB;AACH;", "names": []}