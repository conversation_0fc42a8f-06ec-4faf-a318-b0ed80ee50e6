{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/xyz.ctrl/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"xyz.ctrl\",\n  name: \"Ctrl Wallet\",\n  homepage: \"https://ctrl.xyz/\",\n  image_id: \"749856b0-3f0e-4876-4d0f-27835310db00\",\n  app: {\n    browser: \"https://ctrl.xyz/\",\n    ios: \"https://apps.apple.com/us/app/ctrl-wallet/id6630386336\",\n    android: \"https://play.google.com/store/apps/details?id=xyz.ctrl.wallet\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome:\n      \"https://chrome.google.com/webstore/detail/ctrl-wallet/hmeobnfnfcmdkdcmlblgagmfpfboieaf?hl=en\",\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: \"xyz.ctrl\",\n  mobile: {\n    native: \"ctrl-mobile://\",\n    universal: \"https://ctrl.xyz/deeplink/wallet\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QACE;IACF,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}