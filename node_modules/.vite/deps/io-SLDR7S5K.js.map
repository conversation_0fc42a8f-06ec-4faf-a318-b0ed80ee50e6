{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.compasswallet/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.compasswallet\",\n  name: \"Compass Wallet\",\n  homepage: \"https://compasswallet.io/\",\n  image_id: \"1d7dea00-96be-4ce8-ca15-d14bddbb5000\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/compass-wallet-for-sei/id6450257441\",\n    android:\n      \"https://play.google.com/store/apps/details?id=io.leapwallet.compass\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome:\n      \"https://chromewebstore.google.com/detail/compass-wallet-for-sei/anokgmphncpekkhclmingpimjmcooifb\",\n    firefox: null,\n    safari: null,\n    edge: \"https://chromewebstore.google.com/detail/compass-wallet-for-sei/anokgmphncpekkhclmingpimjmcooifb\",\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"leapcompass://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QACE;IACF,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}