import {
  addTransactionToStore
} from "./chunk-HFJPNBPY.js";
import "./chunk-HGMV3JDR.js";
import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/transaction/actions/gasless/send-gasless-transaction.js
async function sendGaslessTransaction({ account, transaction, serializableTransaction, gasless }) {
  if (serializableTransaction.value && serializableTransaction.value > 0n) {
    throw new Error("Gasless transactions cannot have a value");
  }
  let result;
  if (gasless.provider === "biconomy") {
    const { relayBiconomyTransaction } = await import("./biconomy-IHRSNMHI.js");
    result = await relayBiconomyTransaction({
      account,
      transaction,
      serializableTransaction,
      gasless
    });
  }
  if (gasless.provider === "openzeppelin") {
    const { relayOpenZeppelinTransaction } = await import("./openzeppelin-P7B6XIY4.js");
    result = await relayOpenZeppelinTransaction({
      account,
      transaction,
      serializableTransaction,
      gasless
    });
  }
  if (gasless.provider === "engine") {
    const { relayEngineTransaction } = await import("./engine-PNAC7ZA5.js");
    result = await relayEngineTransaction({
      account,
      transaction,
      serializableTransaction,
      gasless
    });
  }
  if (!result) {
    throw new Error("Unsupported gasless provider");
  }
  addTransactionToStore({
    address: account.address,
    transactionHash: result.transactionHash,
    chainId: transaction.chain.id
  });
  return result;
}
export {
  sendGaslessTransaction
};
//# sourceMappingURL=send-gasless-transaction-KF7Y3WPR.js.map
