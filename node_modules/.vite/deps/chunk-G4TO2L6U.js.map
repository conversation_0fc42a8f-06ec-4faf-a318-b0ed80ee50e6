{"version": 3, "sources": ["../../thirdweb/src/extensions/erc165/__generated__/IERC165/read/supportsInterface.ts", "../../thirdweb/src/extensions/erc721/read/isERC721.ts", "../../thirdweb/src/extensions/erc1155/read/isERC1155.ts"], "sourcesContent": ["import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"supportsInterface\" function.\n */\nexport type SupportsInterfaceParams = {\n  interfaceId: AbiParameterToPrimitiveType<{\n    type: \"bytes4\";\n    name: \"interfaceId\";\n  }>;\n};\n\nexport const FN_SELECTOR = \"0x01ffc9a7\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"bytes4\",\n    name: \"interfaceId\",\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"bool\",\n  },\n] as const;\n\n/**\n * Checks if the `supportsInterface` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `supportsInterface` method is supported.\n * @extension ERC165\n * @example\n * ```ts\n * import { isSupportsInterfaceSupported } from \"thirdweb/extensions/erc165\";\n * const supported = isSupportsInterfaceSupported([\"0x...\"]);\n * ```\n */\nexport function isSupportsInterfaceSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"supportsInterface\" function.\n * @param options - The options for the supportsInterface function.\n * @returns The encoded ABI parameters.\n * @extension ERC165\n * @example\n * ```ts\n * import { encodeSupportsInterfaceParams } from \"thirdweb/extensions/erc165\";\n * const result = encodeSupportsInterfaceParams({\n *  interfaceId: ...,\n * });\n * ```\n */\nexport function encodeSupportsInterfaceParams(\n  options: SupportsInterfaceParams,\n) {\n  return encodeAbiParameters(FN_INPUTS, [options.interfaceId]);\n}\n\n/**\n * Encodes the \"supportsInterface\" function into a Hex string with its parameters.\n * @param options - The options for the supportsInterface function.\n * @returns The encoded hexadecimal string.\n * @extension ERC165\n * @example\n * ```ts\n * import { encodeSupportsInterface } from \"thirdweb/extensions/erc165\";\n * const result = encodeSupportsInterface({\n *  interfaceId: ...,\n * });\n * ```\n */\nexport function encodeSupportsInterface(options: SupportsInterfaceParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeSupportsInterfaceParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Decodes the result of the supportsInterface function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC165\n * @example\n * ```ts\n * import { decodeSupportsInterfaceResult } from \"thirdweb/extensions/erc165\";\n * const result = decodeSupportsInterfaceResultResult(\"...\");\n * ```\n */\nexport function decodeSupportsInterfaceResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"supportsInterface\" function on the contract.\n * @param options - The options for the supportsInterface function.\n * @returns The parsed result of the function call.\n * @extension ERC165\n * @example\n * ```ts\n * import { supportsInterface } from \"thirdweb/extensions/erc165\";\n *\n * const result = await supportsInterface({\n *  contract,\n *  interfaceId: ...,\n * });\n *\n * ```\n */\nexport async function supportsInterface(\n  options: BaseTransactionOptions<SupportsInterfaceParams>,\n) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [options.interfaceId],\n  });\n}\n", "import type { BaseTransactionOptions } from \"../../../transaction/types.js\";\nimport { supportsInterface } from \"../../erc165/__generated__/IERC165/read/supportsInterface.js\";\n\n/**\n * Check if a contract supports the ERC721 interface.\n * @param options - The transaction options.\n * @returns A boolean indicating whether the contract supports the ERC721 interface.\n * @extension ERC721\n * @example\n * ```ts\n * import { isERC721 } from \"thirdweb/extensions/erc721\";\n * const result = await isERC721({ contract });\n * ```\n */\nexport function isERC721(options: BaseTransactionOptions) {\n  return supportsInterface({\n    contract: options.contract,\n    interfaceId: \"0x80ac58cd\",\n  });\n}\n", "import type { BaseTransactionOptions } from \"../../../transaction/types.js\";\nimport { supportsInterface } from \"../../erc165/__generated__/IERC165/read/supportsInterface.js\";\n\n/**\n * Check if a contract supports the ERC1155 interface.\n * @param options - The transaction options.\n * @returns A boolean indicating whether the contract supports the ERC1155 interface.\n * @extension ERC1155\n * @example\n * ```ts\n * import { isERC1155 } from \"thirdweb/extensions/erc1155\";\n * const result = await isERC1155({ contract });\n * ```\n */\nexport function isERC1155(options: BaseTransactionOptions) {\n  return supportsInterface({\n    contract: options.contract,\n    interfaceId: \"0xd9b67a26\",\n  });\n}\n"], "mappings": ";;;;;AAkBO,IAAM,cAAc;AAC3B,IAAM,YAAY;EAChB;IACE,MAAM;IACN,MAAM;;;AAGV,IAAM,aAAa;EACjB;IACE,MAAM;;;AA8FV,eAAsB,kBACpB,SAAwD;AAExD,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAAC,aAAa,WAAW,UAAU;IAC3C,QAAQ,CAAC,QAAQ,WAAW;GAC7B;AACH;;;ACnHM,SAAU,SAAS,SAA+B;AACtD,SAAO,kBAAkB;IACvB,UAAU,QAAQ;IAClB,aAAa;GACd;AACH;;;ACLM,SAAU,UAAU,SAA+B;AACvD,SAAO,kBAAkB;IACvB,UAAU,QAAQ;IAClB,aAAa;GACd;AACH;", "names": []}