{"version": 3, "sources": ["../../thirdweb/src/wallets/ecosystem/is-ecosystem-wallet.ts"], "sourcesContent": ["import type { Wallet } from \"../interfaces/wallet.js\";\nimport type { EcosystemWalletId } from \"../wallet-types.js\";\n\nexport function isEcosystemWallet(\n  wallet: Wallet,\n): wallet is Wallet<EcosystemWalletId>;\n\nexport function isEcosystemWallet(wallet: string): wallet is EcosystemWalletId;\n\n/**\n * Checks if the given wallet is an ecosystem wallet.\n *\n * @param {Wallet | string} wallet - The wallet or wallet ID to check.\n * @returns {boolean} True if the wallet is an ecosystem wallet, false otherwise.\n * @internal\n */\nexport function isEcosystemWallet(\n  wallet: Wallet | string,\n): wallet is Wallet<EcosystemWalletId> {\n  return typeof wallet === \"string\"\n    ? wallet.startsWith(\"ecosystem.\")\n    : wallet.id.startsWith(\"ecosystem.\");\n}\n"], "mappings": ";AAgBM,SAAU,kBACd,QAAuB;AAEvB,SAAO,OAAO,WAAW,WACrB,OAAO,WAAW,YAAY,IAC9B,OAAO,GAAG,WAAW,YAAY;AACvC;", "names": []}