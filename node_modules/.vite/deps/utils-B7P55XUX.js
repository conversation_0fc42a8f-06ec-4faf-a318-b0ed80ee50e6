import {
  CUSTOM_CHAIN_MAP,
  cacheChains,
  convertApiChainToChain,
  convertLegacyChain,
  convertViemChain,
  define<PERSON>hain,
  getCachedChain,
  getCachedChainIfExists,
  getChainDecimals,
  getChainMetadata,
  getChainNativeCurrencyName,
  getChainServices,
  getChainSymbol,
  getRpcUrlForChain
} from "./chunk-KQKMGIQ6.js";
import "./chunk-MTFDOOBS.js";
import "./chunk-RJUQUX6Y.js";
import "./chunk-PPP72TBL.js";
import "./chunk-3OXDSLPJ.js";
import "./chunk-OSFP2VB7.js";
import "./chunk-OS7ZSSJM.js";
export {
  CUSTOM_CHAIN_MAP,
  cacheChains,
  convertApiChainToChain,
  convertLegacyChain,
  convertViemChain,
  define<PERSON>hai<PERSON>,
  getCached<PERSON>hain,
  getCachedChainIfExists,
  getChainDecimals,
  getChainMetadata,
  getChainNativeCurrencyName,
  getChainServices,
  getChainSymbol,
  getRpcUrlForChain
};
//# sourceMappingURL=utils-B7P55XUX.js.map
