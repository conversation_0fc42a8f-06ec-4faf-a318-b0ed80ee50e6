{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/xyz.roam.wallet/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"xyz.roam.wallet\",\n  name: \"Roam\",\n  homepage: \"https://roam.xyz\",\n  image_id: \"a4500b0c-47e3-4c4a-207e-d72a57f1ca00\",\n  app: {\n    browser: null,\n    ios: \"https://testflight.apple.com/join/hM8Ba1Qd\",\n    android: \"https://google.com\",\n    mac: \"https://google.com\",\n    windows: \"https://google.com\",\n    linux: \"https://google.com\",\n    chrome: \"Roam.xyz\",\n    firefox: \"https://google.com\",\n    safari: \"https://google.com\",\n    edge: \"https://google.com\",\n    opera: \"https://google.com\",\n  },\n  rdns: \"xyz.roam.wallet\",\n  mobile: {\n    native: \"roam://\",\n    universal: null,\n  },\n  desktop: {\n    native: \"roam://\",\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}