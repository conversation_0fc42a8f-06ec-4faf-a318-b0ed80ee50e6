import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/react/web/wallets/injected/locale/ru.js
var injectedWalletLocaleRu = (wallet) => ({
  connectionScreen: {
    inProgress: "Ожидание подтверждения",
    failed: "Подключение не удалось",
    instruction: `Примите запрос на подключение в ${wallet}`,
    retry: "Попробовать снова"
  },
  getStartedScreen: {
    instruction: `Отсканируйте QR-код, чтобы скачать приложение ${wallet}`
  },
  scanScreen: {
    instruction: `Для подключения отсканируйте QR-код с помощью приложения ${wallet}`
  },
  getStartedLink: `Ещё нет ${wallet}?`,
  download: {
    chrome: "Скачать расширение для Chrome",
    android: "Скачать в Google Play",
    iOS: "Скачать в App Store"
  }
});
var ru_default = injectedWalletLocaleRu;
export {
  ru_default as default
};
//# sourceMappingURL=ru-XV76X6VK.js.map
