{"version": 3, "sources": ["../../thirdweb/src/react/web/wallets/injected/locale/ru.ts"], "sourcesContent": ["import type { InjectedWalletLocale } from \"./types.js\";\n\n/**\n * @internal\n */\nconst injectedWalletLocaleRu = (wallet: string): InjectedWalletLocale => ({\n  connectionScreen: {\n    inProgress: \"Ожидание подтверждения\",\n    failed: \"Подключение не удалось\",\n    instruction: `Примите запрос на подключение в ${wallet}`,\n    retry: \"Попробовать снова\",\n  },\n  getStartedScreen: {\n    instruction: `Отсканируйте QR-код, чтобы скачать приложение ${wallet}`,\n  },\n  scanScreen: {\n    instruction: `Для подключения отсканируйте QR-код с помощью приложения ${wallet}`,\n  },\n  getStartedLink: `Ещё нет ${wallet}?`,\n  download: {\n    chrome: \"Скачать расширение для Chrome\",\n    android: \"Скачать в Google Play\",\n    iOS: \"Скачать в App Store\",\n  },\n});\n\nexport default injectedWalletLocaleRu;\n"], "mappings": ";;;AAKA,IAAM,yBAAyB,CAAC,YAA0C;EACxE,kBAAkB;IAChB,YAAY;IACZ,QAAQ;IACR,aAAa,mCAAmC,MAAM;IACtD,OAAO;;EAET,kBAAkB;IAChB,aAAa,iDAAiD,MAAM;;EAEtE,YAAY;IACV,aAAa,4DAA4D,MAAM;;EAEjF,gBAAgB,WAAW,MAAM;EACjC,UAAU;IACR,QAAQ;IACR,SAAS;IACT,KAAK;;;AAIT,IAAA,aAAe;", "names": []}