{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/world.dosi.vault/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"world.dosi.vault\",\n  name: \"DOSI Vault\",\n  homepage: \"https://vault.dosi.world/\",\n  image_id: \"0a0d223e-6bf7-4e12-a5b4-1720deb02000\",\n  app: {\n    browser: \"https://vault.dosi.world/\",\n    ios: \"https://apps.apple.com/kr/app/dosi-vault/id1664013594\",\n    android: \"https://play.google.com/store/apps/details?id=world.dosi.vault\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome:\n      \"https://chrome.google.com/webstore/detail/dosi-vault/blpiicikpimmklhoiploliaenjmecabp?hl=en\",\n    firefox:\n      \"https://addons.mozilla.org/ko/firefox/addon/dosi-vault/?utm_source=addons.mozilla.org&utm_medium=referral&utm_content=search\",\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"app.dosivault://\",\n    universal: \"https://dosivault.page.link/qL6j\",\n  },\n  desktop: {\n    native: null,\n    universal: \"https://vault.dosi.world/\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QACE;IACF,SACE;IACF,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}