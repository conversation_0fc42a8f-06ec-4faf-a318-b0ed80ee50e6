{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/pk.modular/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"pk.modular\",\n  name: \"Modular Wallet Prod\",\n  homepage: \"https://modular.pk\",\n  image_id: \"70485da2-2568-463d-722c-25082997cc00\",\n  app: {\n    browser: \"https://modular.pk\",\n    ios: \"https://testflight.apple.com/join/Zbf6wZaP\",\n    android: \"https://play.google.com/store/apps/details?id=com.modular\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"modularwallet://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: \"https://modular.pk\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}