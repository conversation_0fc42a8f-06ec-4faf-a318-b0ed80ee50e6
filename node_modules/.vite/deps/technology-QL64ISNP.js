import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/technology.obvious/index.js
var wallet = {
  id: "technology.obvious",
  name: "Obvious",
  homepage: "https://obvious.technology",
  image_id: "fe1b9394-55af-4828-a70d-5c5b7de6b200",
  app: {
    browser: null,
    ios: "https://apps.apple.com/in/app/obvious-crypto-wallet/id1643088398",
    android: "https://play.google.com/store/apps/details?id=com.hashhalli.obvious",
    mac: null,
    windows: null,
    linux: null,
    chrome: null,
    firefox: null,
    safari: null,
    edge: null,
    opera: null
  },
  rdns: null,
  mobile: {
    native: "obvious://",
    universal: "https://wallet.obvious.technology"
  },
  desktop: {
    native: null,
    universal: null
  }
};
export {
  wallet
};
//# sourceMappingURL=technology-QL64ISNP.js.map
