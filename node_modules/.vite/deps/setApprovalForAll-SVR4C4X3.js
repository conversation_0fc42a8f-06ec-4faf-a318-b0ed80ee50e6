import {
  once
} from "./chunk-NACC2RRT.js";
import {
  prepareContractCall
} from "./chunk-DH7M67RK.js";
import "./chunk-QGXAPRFG.js";
import {
  detectMethod
} from "./chunk-54TJVF2D.js";
import {
  encodeAbiParameters
} from "./chunk-P4DFI2FE.js";
import "./chunk-HKVYRBWW.js";
import "./chunk-HAADYJEF.js";
import "./chunk-UEKVYVRB.js";
import "./chunk-IHMGPG6V.js";
import "./chunk-MZGV4VDW.js";
import "./chunk-RPGMYTER.js";
import "./chunk-64KT6ODC.js";
import "./chunk-EP3M7YWL.js";
import "./chunk-NTKAF5LO.js";
import "./chunk-2CIJO3V3.js";
import "./chunk-E4AXWHD7.js";
import "./chunk-H7Z32RTP.js";
import "./chunk-YXD4WFHV.js";
import "./chunk-26FWGFQH.js";
import "./chunk-DESKQC7P.js";
import "./chunk-BJ63FHMG.js";
import "./chunk-4LB33PYO.js";
import "./chunk-6CMZOK3K.js";
import "./chunk-HXWRQBIO.js";
import "./chunk-5UQS4U7E.js";
import "./chunk-ZLUBC7IW.js";
import "./chunk-5Q2S2L2S.js";
import "./chunk-VAV3ZUCP.js";
import "./chunk-KQKMGIQ6.js";
import "./chunk-MTFDOOBS.js";
import "./chunk-RJUQUX6Y.js";
import "./chunk-PPP72TBL.js";
import "./chunk-3OXDSLPJ.js";
import "./chunk-OSFP2VB7.js";
import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/extensions/erc1155/__generated__/IERC1155/write/setApprovalForAll.js
var FN_SELECTOR = "0xa22cb465";
var FN_INPUTS = [
  {
    type: "address",
    name: "_operator"
  },
  {
    type: "bool",
    name: "_approved"
  }
];
var FN_OUTPUTS = [];
function isSetApprovalForAllSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS]
  });
}
function encodeSetApprovalForAllParams(options) {
  return encodeAbiParameters(FN_INPUTS, [options.operator, options.approved]);
}
function encodeSetApprovalForAll(options) {
  return FN_SELECTOR + encodeSetApprovalForAllParams(options).slice(2);
}
function setApprovalForAll(options) {
  const asyncOptions = once(async () => {
    return "asyncParams" in options ? await options.asyncParams() : options;
  });
  return prepareContractCall({
    contract: options.contract,
    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS],
    params: async () => {
      const resolvedOptions = await asyncOptions();
      return [resolvedOptions.operator, resolvedOptions.approved];
    },
    value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.value;
    },
    accessList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.accessList;
    },
    gas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gas;
    },
    gasPrice: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gasPrice;
    },
    maxFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxFeePerGas;
    },
    maxPriorityFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxPriorityFeePerGas;
    },
    nonce: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.nonce;
    },
    extraGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.extraGas;
    },
    erc20Value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.erc20Value;
    },
    authorizationList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.authorizationList;
    }
  });
}
export {
  FN_SELECTOR,
  encodeSetApprovalForAll,
  encodeSetApprovalForAllParams,
  isSetApprovalForAllSupported,
  setApprovalForAll
};
//# sourceMappingURL=setApprovalForAll-SVR4C4X3.js.map
