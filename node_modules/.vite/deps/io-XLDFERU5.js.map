{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.transi/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.transi\",\n  name: \"<PERSON><PERSON>\",\n  homepage: \"https://www.transi.io/\",\n  image_id: \"a567089d-69d5-47f6-fd99-db47a448ab00\",\n  app: {\n    browser: \"https://www.transi.io/TransiWallet\",\n    ios: \"https://apps.apple.com/us/app/transi-chat/id1662471884\",\n    android: \"https://www.transi.io/TransiWallet\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"transi://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: \"https://www.transi.io/TransiWallet\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}