import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/xyz.ctrl/index.js
var wallet = {
  id: "xyz.ctrl",
  name: "Ctrl Wallet",
  homepage: "https://ctrl.xyz/",
  image_id: "749856b0-3f0e-4876-4d0f-27835310db00",
  app: {
    browser: "https://ctrl.xyz/",
    ios: "https://apps.apple.com/us/app/ctrl-wallet/id6630386336",
    android: "https://play.google.com/store/apps/details?id=xyz.ctrl.wallet",
    mac: null,
    windows: null,
    linux: null,
    chrome: "https://chrome.google.com/webstore/detail/ctrl-wallet/hmeobnfnfcmdkdcmlblgagmfpfboieaf?hl=en",
    firefox: null,
    safari: null,
    edge: null,
    opera: null
  },
  rdns: "xyz.ctrl",
  mobile: {
    native: "ctrl-mobile://",
    universal: "https://ctrl.xyz/deeplink/wallet"
  },
  desktop: {
    native: null,
    universal: null
  }
};
export {
  wallet
};
//# sourceMappingURL=xyz-ZIDA6LGW.js.map
