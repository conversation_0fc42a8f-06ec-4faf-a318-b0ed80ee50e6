{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.blocto/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.blocto\",\n  name: \"<PERSON><PERSON>\",\n  homepage: \"https://blocto.io/\",\n  image_id: \"374258d3-c749-4f37-7815-77e61f798c00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/app/id1481181682\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.portto.blocto&hl=en&gl=US\",\n    mac: \"\",\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"blocto://\",\n    universal: \"https://blocto.app\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}