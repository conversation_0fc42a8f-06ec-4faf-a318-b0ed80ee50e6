{"version": 3, "sources": ["../../thirdweb/src/transaction/actions/gasless/send-gasless-transaction.ts"], "sourcesContent": ["import type { Account } from \"../../../wallets/interfaces/wallet.js\";\nimport type { PreparedTransaction } from \"../../prepare-transaction.js\";\nimport type { SerializableTransaction } from \"../../serialize-transaction.js\";\nimport { addTransactionToStore } from \"../../transaction-store.js\";\nimport type { WaitForReceiptOptions } from \"../wait-for-tx-receipt.js\";\nimport type { GaslessOptions } from \"./types.js\";\n\ntype SendGaslessTransactionOptions = {\n  account: Account;\n  // TODO: update this to `Transaction<\"prepared\">` once the type is available to ensure only prepared transactions are accepted\n  // biome-ignore lint/suspicious/noExplicitAny: library function that accepts any prepared transaction type\n  transaction: PreparedTransaction<any>;\n  serializableTransaction: SerializableTransaction;\n  gasless: GaslessOptions;\n};\n\nexport async function sendGaslessTransaction({\n  account,\n  transaction,\n  serializableTransaction,\n  gasless,\n}: SendGaslessTransactionOptions): Promise<WaitForReceiptOptions> {\n  // TODO: handle special case for mutlicall transactions!\n  // Steps:\n  // 1. check if the method is `multicall` by comparing the 4bytes data with the `multicall` selector\n  // 2. split the rest of the data into its \"parts\"\n  // 3. solidityPack the parts with the part data + the `account.address`\n  // see v4: `core/classes/transactions.ts>Transaction>prepareGasless:L551`\n\n  if (serializableTransaction.value && serializableTransaction.value > 0n) {\n    throw new Error(\"Gasless transactions cannot have a value\");\n  }\n\n  // TODO: multiply gas by 2 for some reason(?) - we do in v4, *should* we?\n\n  let result: WaitForReceiptOptions | undefined;\n\n  // biconomy\n  if (gasless.provider === \"biconomy\") {\n    const { relayBiconomyTransaction } = await import(\n      \"./providers/biconomy.js\"\n    );\n    result = await relayBiconomyTransaction({\n      account,\n      transaction,\n      serializableTransaction,\n      gasless,\n    });\n  }\n\n  // openzeppelin\n  if (gasless.provider === \"openzeppelin\") {\n    const { relayOpenZeppelinTransaction } = await import(\n      \"./providers/openzeppelin.js\"\n    );\n    result = await relayOpenZeppelinTransaction({\n      account,\n      transaction,\n      serializableTransaction,\n      gasless,\n    });\n  }\n\n  if (gasless.provider === \"engine\") {\n    const { relayEngineTransaction } = await import(\"./providers/engine.js\");\n    result = await relayEngineTransaction({\n      account,\n      transaction,\n      serializableTransaction,\n      gasless,\n    });\n  }\n\n  if (!result) {\n    throw new Error(\"Unsupported gasless provider\");\n  }\n  addTransactionToStore({\n    address: account.address,\n    transactionHash: result.transactionHash,\n    chainId: transaction.chain.id,\n  });\n  return result;\n}\n"], "mappings": ";;;;;;;AAgBA,eAAsB,uBAAuB,EAC3C,SACA,aACA,yBACA,QAAO,GACuB;AAQ9B,MAAI,wBAAwB,SAAS,wBAAwB,QAAQ,IAAI;AACvE,UAAM,IAAI,MAAM,0CAA0C;EAC5D;AAIA,MAAI;AAGJ,MAAI,QAAQ,aAAa,YAAY;AACnC,UAAM,EAAE,yBAAwB,IAAK,MAAM,OACzC,wBAAyB;AAE3B,aAAS,MAAM,yBAAyB;MACtC;MACA;MACA;MACA;KACD;EACH;AAGA,MAAI,QAAQ,aAAa,gBAAgB;AACvC,UAAM,EAAE,6BAA4B,IAAK,MAAM,OAC7C,4BAA6B;AAE/B,aAAS,MAAM,6BAA6B;MAC1C;MACA;MACA;MACA;KACD;EACH;AAEA,MAAI,QAAQ,aAAa,UAAU;AACjC,UAAM,EAAE,uBAAsB,IAAK,MAAM,OAAO,sBAAuB;AACvE,aAAS,MAAM,uBAAuB;MACpC;MACA;MACA;MACA;KACD;EACH;AAEA,MAAI,CAAC,QAAQ;AACX,UAAM,IAAI,MAAM,8BAA8B;EAChD;AACA,wBAAsB;IACpB,SAAS,QAAQ;IACjB,iBAAiB,OAAO;IACxB,SAAS,YAAY,MAAM;GAC5B;AACD,SAAO;AACT;", "names": []}