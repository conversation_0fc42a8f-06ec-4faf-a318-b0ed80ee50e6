{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/org.mathwallet/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"org.mathwallet\",\n  name: \"MathWallet\",\n  homepage: \"https://mathwallet.org/\",\n  image_id: \"26a8f588-3231-4411-60ce-5bb6b805a700\",\n  app: {\n    browser:\n      \"https://chrome.google.com/webstore/detail/math-wallet/afbcbjpbpfadlkmhmclhkeeodmamcflc\",\n    ios: \"https://apps.apple.com/us/app/mathwallet5/id1582612388\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.mathwallet.android\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome:\n      \"https://chrome.google.com/webstore/detail/math-wallet/afbcbjpbpfadlkmhmclhkeeodmamcflc\",\n    firefox: null,\n    safari: null,\n    edge: \"https://microsoftedge.microsoft.com/addons/detail/math-wallet/dfeccadlilpndjjohbjdble<PERSON><PERSON>ahlmm\",\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"mathwallet://\",\n    universal: \"https://www.mathwallet.org\",\n  },\n  desktop: {\n    native: null,\n    universal:\n      \"https://chrome.google.com/webstore/detail/math-wallet/afbcbjpbpfadlkmhmclhkeeodmamcflc\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SACE;IACF,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QACE;IACF,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WACE;;;", "names": []}