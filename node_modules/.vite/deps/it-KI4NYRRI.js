import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/it.airgap/index.js
var wallet = {
  id: "it.airgap",
  name: "AirGap Wallet",
  homepage: "https://airgap.it",
  image_id: "76bfe8cd-cf3f-4341-c33c-60da01065000",
  app: {
    browser: "https://wallet.airgap.it",
    ios: "https://itunes.apple.com/us/app/airgap-wallet/id1420996542?l=de&ls=1&mt=8",
    android: "https://play.google.com/store/apps/details?id=it.airgap.wallet",
    mac: "https://github.com/airgap-it/airgap-wallet/releases",
    windows: "https://github.com/airgap-it/airgap-wallet/releases",
    linux: "https://github.com/airgap-it/airgap-wallet/releases",
    chrome: null,
    firefox: null,
    safari: null,
    edge: null,
    opera: null
  },
  rdns: null,
  mobile: {
    native: "airgap-wallet://",
    universal: null
  },
  desktop: {
    native: null,
    universal: "https://wallet.airgap.it"
  }
};
export {
  wallet
};
//# sourceMappingURL=it-KI4NYRRI.js.map
