{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/technology.obvious/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"technology.obvious\",\n  name: \"Obvious\",\n  homepage: \"https://obvious.technology\",\n  image_id: \"fe1b9394-55af-4828-a70d-5c5b7de6b200\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/in/app/obvious-crypto-wallet/id1643088398\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.hashhalli.obvious\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"obvious://\",\n    universal: \"https://wallet.obvious.technology\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}