import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/network.cvl/index.js
var wallet = {
  id: "network.cvl",
  name: "CVL Wallet",
  homepage: "https://cvl.network",
  image_id: "e4eff15a-35d5-49fe-047f-33e331f46400",
  app: {
    browser: "https://app.cvl.network/",
    ios: "https://apps.apple.com/ru/app/cvl-wallet/id6444357628",
    android: "https://play.google.com/store/apps/details?id=llp.bc_group.cvl_wallet",
    mac: null,
    windows: null,
    linux: null,
    chrome: null,
    firefox: null,
    safari: null,
    edge: null,
    opera: null
  },
  rdns: null,
  mobile: {
    native: null,
    universal: "https://app.cvl.network/"
  },
  desktop: {
    native: null,
    universal: "https://app.cvl.network/"
  }
};
export {
  wallet
};
//# sourceMappingURL=network-I72OASST.js.map
