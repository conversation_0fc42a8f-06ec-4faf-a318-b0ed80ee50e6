{"version": 3, "sources": ["../../thirdweb/src/wallets/defaultWallets.ts"], "sourcesContent": ["import type { Chain } from \"../chains/types.js\";\nimport { COINBASE, METAMASK, RAINBOW, ZERION } from \"./constants.js\";\nimport { createWallet } from \"./create-wallet.js\";\nimport type { Wallet } from \"./interfaces/wallet.js\";\nimport type { AppMetadata } from \"./types.js\";\n\n/**\n * @internal\n */\nexport function getDefaultWallets(options?: {\n  appMetadata?: AppMetadata;\n  chains?: Chain[];\n}): Wallet[] {\n  return [\n    createWallet(\"inApp\"),\n    createWallet(METAMASK),\n    createWallet(COINBASE, {\n      appMetadata: options?.appMetadata,\n      chains: options?.chains,\n    }),\n    createWallet(RAINBOW),\n    createWallet(\"io.rabby\"),\n    createWallet(ZERION),\n  ];\n}\n"], "mappings": ";;;;;;;;;;;AASM,SAAU,kBAAkB,SAGjC;AACC,SAAO;IACL,aAAa,OAAO;IACpB,aAAa,QAAQ;IACrB,aAAa,UAAU;MACrB,aAAa,mCAAS;MACtB,QAAQ,mCAAS;KAClB;IACD,aAAa,OAAO;IACpB,aAAa,UAAU;IACvB,aAAa,MAAM;;AAEvB;", "names": []}