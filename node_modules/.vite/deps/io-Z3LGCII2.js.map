{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.safecryptowallet/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.safecryptowallet\",\n  name: \"SafeWallet\",\n  homepage: \"https://safecryptowallet.io\",\n  image_id: \"c4f43408-612b-4777-c9d6-a022934ce600\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/safe-crypto-wallet-blockchain/id6449599259?mt=8&shortlink=4ybm9n4d&c=safe+wallet&pid=clickwebsite&af_xp=custom&source_caller=ui\",\n    android: null,\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"safewallet://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}