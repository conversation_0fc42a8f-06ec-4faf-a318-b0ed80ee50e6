import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/react/web/wallets/injected/locale/vi.js
var injectedWalletLocaleVi = (wallet) => ({
  connectionScreen: {
    inProgress: "<PERSON>ang đợi xác nhận",
    failed: "Kết nối thất bại",
    instruction: `Kết nối bằng ứng dụng ${wallet}`,
    retry: "Thử lại"
  },
  getStartedScreen: {
    instruction: `Quét mã QR để tải ứng dụng ${wallet}`
  },
  scanScreen: {
    instruction: `Quét mã QR bằng ứng dụng ${wallet} để kết nối`
  },
  getStartedLink: `Tôi không có ứng dụng ${wallet}`,
  download: {
    chrome: "Tải cho trình duyệt Chrome",
    android: "Tải trên Google Play",
    iOS: "Tải trên App Store"
  }
});
var vi_default = injectedWalletLocaleVi;
export {
  vi_default as default
};
//# sourceMappingURL=vi-ZI5OC7VS.js.map
