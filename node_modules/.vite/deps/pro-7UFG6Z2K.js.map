{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/pro.assure/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"pro.assure\",\n  name: \"<PERSON><PERSON>\",\n  homepage: \"https://www.assure.pro\",\n  image_id: \"64db7104-c8b7-44ea-e102-11ce87124200\",\n  app: {\n    browser: null,\n    ios: \"http://itunes.apple.com/app/id1604825026\",\n    android: \"https://play.google.com/store/apps/details?id=com.neuxs.assure\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"assure://\",\n    universal: \"https://www.assure.pro/Official\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}