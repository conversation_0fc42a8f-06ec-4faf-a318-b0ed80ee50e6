{"abi": [{"type": "constructor", "inputs": [{"name": "_proofMaturityDelaySeconds", "type": "uint256", "internalType": "uint256"}, {"name": "_disputeGameFinalityDelaySeconds", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "blacklistDisputeGame", "inputs": [{"name": "_disputeGame", "type": "address", "internalType": "contract IDisputeGame"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "checkWithdrawal", "inputs": [{"name": "_withdrawalHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "_proofSubmitter", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "depositTransaction", "inputs": [{"name": "_to", "type": "address", "internalType": "address"}, {"name": "_value", "type": "uint256", "internalType": "uint256"}, {"name": "_gasLimit", "type": "uint64", "internalType": "uint64"}, {"name": "_isCreation", "type": "bool", "internalType": "bool"}, {"name": "_data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "disputeGameBlacklist", "inputs": [{"name": "", "type": "address", "internalType": "contract IDisputeGame"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "disputeGameFactory", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract DisputeGameFactory"}], "stateMutability": "view"}, {"type": "function", "name": "disputeGameFinalityDelaySeconds", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "donateETH", "inputs": [], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "finalizeWithdrawalTransaction", "inputs": [{"name": "_tx", "type": "tuple", "internalType": "struct Types.WithdrawalTransaction", "components": [{"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "sender", "type": "address", "internalType": "address"}, {"name": "target", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "gasLimit", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "finalizeWithdrawalTransactionExternalProof", "inputs": [{"name": "_tx", "type": "tuple", "internalType": "struct Types.WithdrawalTransaction", "components": [{"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "sender", "type": "address", "internalType": "address"}, {"name": "target", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "gasLimit", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}]}, {"name": "_proofSubmitter", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "guardian", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "_disputeGameFactory", "type": "address", "internalType": "contract DisputeGameFactory"}, {"name": "_systemConfig", "type": "address", "internalType": "contract SystemConfig"}, {"name": "_superchainConfig", "type": "address", "internalType": "contract SuperchainConfig"}, {"name": "_initialRespectedGameType", "type": "uint32", "internalType": "GameType"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "l2Sender", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "minimumGasLimit", "inputs": [{"name": "_byteCount", "type": "uint64", "internalType": "uint64"}], "outputs": [{"name": "", "type": "uint64", "internalType": "uint64"}], "stateMutability": "pure"}, {"type": "function", "name": "numProofSubmitters", "inputs": [{"name": "_withdrawalHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "params", "inputs": [], "outputs": [{"name": "prevBaseFee", "type": "uint128", "internalType": "uint128"}, {"name": "prevBoughtGas", "type": "uint64", "internalType": "uint64"}, {"name": "prevBlockNum", "type": "uint64", "internalType": "uint64"}], "stateMutability": "view"}, {"type": "function", "name": "paused", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "proofMaturityDelaySeconds", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "proofSubmitters", "inputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "proveWithdrawalTransaction", "inputs": [{"name": "_tx", "type": "tuple", "internalType": "struct Types.WithdrawalTransaction", "components": [{"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "sender", "type": "address", "internalType": "address"}, {"name": "target", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "gasLimit", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}]}, {"name": "_disputeGameIndex", "type": "uint256", "internalType": "uint256"}, {"name": "_outputRootProof", "type": "tuple", "internalType": "struct Types.OutputRootProof", "components": [{"name": "version", "type": "bytes32", "internalType": "bytes32"}, {"name": "stateRoot", "type": "bytes32", "internalType": "bytes32"}, {"name": "messagePasserStorageRoot", "type": "bytes32", "internalType": "bytes32"}, {"name": "latestBlockhash", "type": "bytes32", "internalType": "bytes32"}]}, {"name": "_withdrawalProof", "type": "bytes[]", "internalType": "bytes[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "provenWithdra<PERSON>s", "inputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}, {"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "disputeGameProxy", "type": "address", "internalType": "contract IDisputeGame"}, {"name": "timestamp", "type": "uint64", "internalType": "uint64"}], "stateMutability": "view"}, {"type": "function", "name": "respectedGameType", "inputs": [], "outputs": [{"name": "", "type": "uint32", "internalType": "GameType"}], "stateMutability": "view"}, {"type": "function", "name": "respectedGameTypeUpdatedAt", "inputs": [], "outputs": [{"name": "", "type": "uint64", "internalType": "uint64"}], "stateMutability": "view"}, {"type": "function", "name": "setRespectedGameType", "inputs": [{"name": "_gameType", "type": "uint32", "internalType": "GameType"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "superchainConfig", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract SuperchainConfig"}], "stateMutability": "view"}, {"type": "function", "name": "systemConfig", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract SystemConfig"}], "stateMutability": "view"}, {"type": "function", "name": "version", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint8", "indexed": false, "internalType": "uint8"}], "anonymous": false}, {"type": "event", "name": "TransactionDeposited", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "version", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "opaqueData", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "WithdrawalFinalized", "inputs": [{"name": "withdrawalHash", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "success", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "WithdrawalProven", "inputs": [{"name": "withdrawalHash", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": []}, {"type": "error", "name": "CallPaused", "inputs": []}, {"type": "error", "name": "GasEstimation", "inputs": []}, {"type": "error", "name": "LargeCalldata", "inputs": []}, {"type": "error", "name": "OutOfGas", "inputs": []}, {"type": "error", "name": "SmallGasLimit", "inputs": []}, {"type": "error", "name": "Unauthorized", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "1310:23607:135:-:0;;;5985:513;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;6085:57;;;;6152:70;;;;6233:258;6306:1;;;;6233:10;:258::i;:::-;5985:513;;1310:23607;;6730:971;3100:19:43;3123:13;;;;;;3122:14;;3168:34;;;;-1:-1:-1;3186:12:43;;3201:1;3186:12;;;;:16;3168:34;3167:97;;;;3209:33;3236:4;3209:18;;;;;:33;;:::i;:::-;3208:34;:55;;;;-1:-1:-1;3246:12:43;;;;;:17;3208:55;3146:190;;;;-1:-1:-1;;;3146:190:43;;466:2:357;3146:190:43;;;448:21:357;505:2;485:18;;;478:30;544:34;524:18;;;517:62;-1:-1:-1;;;595:18:357;;;588:44;649:19;;3146:190:43;;;;;;;;;3346:12;:16;;-1:-1:-1;;3346:16:43;3361:1;3346:16;;;3372:65;;;;3406:13;:20;;-1:-1:-1;;3406:20:43;;;;;3372:65;6977:18:135::1;:40:::0;;-1:-1:-1;;;;;;6977:40:135;;::::1;-1:-1:-1::0;;;;;6977:40:135;;::::1;::::0;;;::::1;::::0;;;7027:12:::1;:28:::0;;;;::::1;::::0;;::::1;;::::0;;7065:16:::1;:36:::0;;-1:-1:-1;;;;;;7065:36:135::1;6977:40;7065:36:::0;;::::1;;;::::0;;7249:8:::1;::::0;::::1;7245:414;;7287:8;:38:::0;;1338:42:192::1;-1:-1:-1::0;;;;;;7287:38:135;;::::1;;::::0;;7485:26:::1;:52:::0;;-1:-1:-1;;;;;;7603:45:135;7485:52;7521:15:::1;-1:-1:-1::0;;;;;7485:52:135::1;;-1:-1:-1::0;;7603:45:135;;::::1;::::0;::::1;;::::0;;7245:414:::1;7669:25;:23;:25::i;:::-;3461:14:43::0;3457:99;;;3507:5;3491:21;;-1:-1:-1;;3491:21:43;;;3531:14;;-1:-1:-1;831:36:357;;3531:14:43;;819:2:357;804:18;3531:14:43;;;;;;;3457:99;3090:472;6730:971:135;;;;:::o;1175:320:59:-;-1:-1:-1;;;;;1465:19:59;;:23;;;1175:320::o;8340:234:137:-;4888:13:43;;;;;;;4880:69;;;;-1:-1:-1;;;4880:69:43;;1080:2:357;4880:69:43;;;1062:21:357;1119:2;1099:18;;;1092:30;1158:34;1138:18;;;1131:62;-1:-1:-1;;;1209:18:357;;;1202:41;1260:19;;4880:69:43;878:407:357;4880:69:43;8415:6:137::1;:19:::0;-1:-1:-1;;;8415:19:137;::::1;-1:-1:-1::0;;;;;8415:19:137::1;;:24:::0;8411:157:::1;;8464:93;::::0;;::::1;::::0;::::1;::::0;;8494:6:::1;8464:93:::0;;;-1:-1:-1;8464:93:137::1;::::0;::::1;::::0;8541:12:::1;-1:-1:-1::0;;;;;8464:93:137::1;::::0;;;;;;;-1:-1:-1;;;8455:102:137::1;;:6;:102:::0;8411:157:::1;8340:234::o:0;14:245:357:-;93:6;101;154:2;142:9;133:7;129:23;125:32;122:52;;;170:1;167;160:12;122:52;-1:-1:-1;;193:16:357;;249:2;234:18;;;228:25;193:16;;228:25;;-1:-1:-1;14:245:357:o;878:407::-;1310:23607:135;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "1310:23607:135:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;9614:86;9633:10;9645:9;2352:7;9683:5;9690:9;;;;;;;;;;;;9614:18;:86::i;:::-;1310:23607;;;;;3443:32;;;;;;;;;;-1:-1:-1;3443:32:135;;;;;;;;;;;212:42:357;200:55;;;182:74;;170:2;155:18;3443:32:135;;;;;;;;3156:40;;;;;;;;;;-1:-1:-1;3156:40:135;;;;;;;;;;;4041:33;;;;;;;;;;-1:-1:-1;4041:33:135;;;;;;;;;;;730:10:357;718:23;;;700:42;;688:2;673:18;4041:33:135;524:224:357;14882:2403:135;;;;;;;;;;-1:-1:-1;14882:2403:135;;;;;:::i;:::-;;:::i;7954:101::-;;;;;;;;;;;;;:::i;3892:57::-;;;;;;;;;;-1:-1:-1;3892:57:135;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;4257:14:357;;4250:22;4232:41;;4220:2;4205:18;3892:57:135;4092:187:357;10816:3564:135;;;;;;;;;;-1:-1:-1;10816:3564:135;;;;;:::i;:::-;;:::i;4162:40::-;;;;;;;;;;-1:-1:-1;4162:40:135;;;;;;;;;;;;;;5638:18:357;5626:31;;;5608:50;;5596:2;5581:18;4162:40:135;5464:200:357;24767:148:135;;;;;;;;;;-1:-1:-1;24767:148:135;;;;;:::i;:::-;24843:7;24869:32;;;:15;:32;;;;;:39;;24767:148;;;;6000:25:357;;;5988:2;5973:18;24767:148:135;5854:177:357;5882:40:135;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;8115:94::-;;;;;;;;;;;;;:::i;21034:3510::-;;;;;;;;;;-1:-1:-1;21034:3510:135;;;;;:::i;:::-;;:::i;20049:185::-;;;;;;;;;;-1:-1:-1;20049:185:135;;;;;:::i;:::-;;:::i;20481:228::-;;;;;;;;;;-1:-1:-1;20481:228:135;;;;;:::i;:::-;;:::i;14493:178::-;;;;;;;;;;-1:-1:-1;14493:178:135;;;;;:::i;:::-;;:::i;6730:971::-;;;;;;;;;;-1:-1:-1;6730:971:135;;;;;:::i;:::-;;:::i;8453:132::-;;;;;;;;;;-1:-1:-1;8543:35:135;8453:132;;2615:23;;;;;;;;;;-1:-1:-1;2615:23:135;;;;;;;;2729:52;;;;;;;;;;-1:-1:-1;2729:52:135;;;;;:::i;:::-;;;;;;;;;;;;;;;;9078:120;;;;;;;;;;-1:-1:-1;9078:120:135;;;;;:::i;:::-;;:::i;4315:52::-;;;;;;;;;;-1:-1:-1;4315:52:135;;;;;:::i;:::-;;:::i;3712:81::-;;;;;;;;;;-1:-1:-1;3712:81:135;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;9667:42:357;9655:55;;;9637:74;;9759:18;9747:31;;;9742:2;9727:18;;9720:59;9610:18;3712:81:135;9442:343:357;8268:119:135;;;;;;;;;;-1:-1:-1;8352:28:135;8268:119;;3093:28:137;;;;;;;;;;-1:-1:-1;3093:28:137;;;;;;;;;;;;;;;;;;;;;;;;;10018:34:357;10006:47;;;9988:66;;10073:18;10127:15;;;10122:2;10107:18;;10100:43;10179:15;;10159:18;;;10152:43;9976:2;9961:18;3093:28:137;9790:411:357;18015:1855:135;;;;;;:::i;:::-;;:::i;3566:44::-;;;;;;;;;;-1:-1:-1;3566:44:135;;;;;;;;18015:1855;18221:9;3511:18:137;3532:9;3511:30;;18375:11:135::1;:32;;;;-1:-1:-1::0;18390:17:135::1;::::0;::::1;::::0;::::1;18375:32;18371:56;;;18416:11;;;;;;;;;;;;;;18371:56;18591:37;18614:5;:12;18591:15;:37::i;:::-;18579:49;;:9;:49;;;18575:77;;;18637:15;;;;;;;;;;;;;;18575:77;19042:7;19027:5;:12;:22;19023:50;;;19058:15;;;;;;;;;;;;;;19023:50;19179:10;19217:9;19203:23:::0;::::1;19199:108;;-1:-1:-1::0;19285:10:135::1;741:42:237::0;1213:27;19199:108:135::1;19564:23;19607:9;19618:6;19626:9;19637:11;19650:5;19590:66;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;19564:92;;2202:1;19830:3;19803:60;;19824:4;19803:60;;;19852:10;19803:60;;;;;;:::i;:::-;;;;;;;;18236:1634;;3642:29:137::0;3651:7;3660:10;3642:8;:29::i;:::-;3433:245;18015:1855:135;;;;;;:::o;14882:2403::-;5766:8;:6;:8::i;:::-;5762:33;;;5783:12;;;;;;;;;;;;;;5762:33;15328:8:::1;::::0;:39:::1;:8;1338:42:192;15328:39:135;15307:137;;;::::0;::::1;::::0;;12464:2:357;15307:137:135::1;::::0;::::1;12446:21:357::0;12503:2;12483:18;;;12476:30;12542:34;12522:18;;;12515:62;12613:33;12593:18;;;12586:61;12664:19;;15307:137:135::1;;;;;;;;;15495:22;15520:27;15543:3;15520:22;:27::i;:::-;15495:52;;15613:48;15629:14;15645:15;15613;:48::i;:::-;15741:36;::::0;;;:20:::1;:36;::::0;;;;;;;:43;;;::::1;15780:4;15741:43;::::0;;15889:10;;::::1;::::0;15878:8:::1;:21:::0;;::::1;::::0;;::::1;::::0;;;::::1;::::0;;;::::1;::::0;;16558:10;::::1;::::0;16570:12:::1;::::0;::::1;::::0;16584:9:::1;::::0;::::1;::::0;16595:8:::1;::::0;::::1;::::0;16534:70:::1;::::0;16558:10;16570:12;16584:9;16534:23:::1;:70::i;:::-;16672:8;:38:::0;;;::::1;1338:42:192;16672:38:135;::::0;;16869:44:::1;::::0;16519:85;;-1:-1:-1;16889:14:135;;16869:44:::1;::::0;::::1;::::0;16519:85;4257:14:357;4250:22;4232:41;;4220:2;4205:18;;4092:187;16869:44:135::1;;;;;;;;17178:7;17177:8;:53;;;;-1:-1:-1::0;17189:9:135::1;1016:1:192;17189:41:135;17177:53;17173:106;;;17253:15;;;;;;;;;;;;;;17173:106;15062:2223;;14882:2403:::0;;:::o;7954:101::-;7995:7;8021:16;;;;;;;;;;;:25;;;:27;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;8014:34;;7954:101;:::o;10816:3564::-;5766:8;:6;:8::i;:::-;5762:33;;;5783:12;;;;;;;;;;;;;;5762:33;11351:4:::1;11329:27;;:3;:10;;;:27;;::::0;11321:103:::1;;;::::0;::::1;::::0;;13152:2:357;11321:103:135::1;::::0;::::1;13134:21:357::0;13191:2;13171:18;;;13164:30;13230:34;13210:18;;;13203:62;13301:33;13281:18;;;13274:61;13352:19;;11321:103:135::1;12950:427:357::0;11321:103:135::1;11562:18;::::0;:49:::1;::::0;;;;::::1;::::0;::::1;6000:25:357::0;;;11516:17:135::1;::::0;;;11562:18:::1;::::0;;::::1;::::0;:30:::1;::::0;5973:18:357;;11562:49:135::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;11515:96;;;;;11621:16;11640:9;:19;;;:21;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;11776:17;::::0;11621:40;;-1:-1:-1;11776:17:135::1;11758:12:::0;;::::1;11776:17:::0;::::1;11758:41;11750:87;;;::::0;::::1;::::0;;14432:2:357;11750:87:135::1;::::0;::::1;14414:21:357::0;14471:2;14451:18;;;14444:30;14510:34;14490:18;;;14483:62;14581:3;14561:18;;;14554:31;14602:19;;11750:87:135::1;14230:397:357::0;11750:87:135::1;11977:45;;;::::0;;::::1;::::0;::::1;12005:16:::0;11977:45:::1;:::i;:::-;:27;:45::i;:::-;11957:10:::0;:65:::1;11936:153;;;::::0;::::1;::::0;;15487:2:357;11936:153:135::1;::::0;::::1;15469:21:357::0;15526:2;15506:18;;;15499:30;15565:34;15545:18;;;15538:62;15636:11;15616:18;;;15609:39;15665:19;;11936:153:135::1;15285:405:357::0;11936:153:135::1;12200:22;12225:27;12248:3;12225:22;:27::i;:::-;12200:52:::0;-1:-1:-1;12446:26:135::1;12424:9;:16;;;:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:48;;;;;;;;:::i;:::-;::::0;12403:153:::1;;;::::0;::::1;::::0;;16368:2:357;12403:153:135::1;::::0;::::1;16350:21:357::0;16407:2;16387:18;;;16380:30;16446:34;16426:18;;;16419:62;16517:28;16497:18;;;16490:56;16563:19;;12403:153:135::1;16166:422:357::0;12403:153:135::1;12836:147;::::0;;::::1;::::0;::::1;16767:25:357::0;;;12792:18:135::1;16808::357::0;;;16801:34;;;16740:18;;12836:147:135::1;::::0;;;;;::::1;::::0;;;;;;12813:180;;12836:147:::1;12813:180:::0;;::::1;::::0;13408:22;;::::1;6000:25:357::0;;;12813:180:135;-1:-1:-1;13346:240:135::1;::::0;5973:18:357;13408:22:135::1;::::0;;;;;::::1;::::0;;;13346:240;;::::1;::::0;;;::::1;::::0;;::::1;13408:22;13346:240:::0;::::1;::::0;13408:22;13346:240:::1;13489:16:::0;;13346:240:::1;:::i;:::-;13530:16;:41;;;13346:37;:240::i;:::-;13325:337;;;::::0;::::1;::::0;;18169:2:357;13325:337:135::1;::::0;::::1;18151:21:357::0;18208:2;18188:18;;;18181:30;18247:34;18227:18;;;18220:62;18318:20;18298:18;;;18291:48;18356:19;;13325:337:135::1;17967:414:357::0;13325:337:135::1;14020:85;::::0;;;;::::1;::::0;;::::1;::::0;;::::1;::::0;;::::1;14086:15;14020:85:::0;::::1;;::::0;;::::1;::::0;;;-1:-1:-1;13960:33:135;;;:17:::1;:33:::0;;;;;13994:10:::1;13960:45:::0;;;;;;;:145;;;;;;;;::::1;::::0;::::1;::::0;;;;;;::::1;::::0;;;;;;;::::1;::::0;;;14210:10;;::::1;::::0;14198;;::::1;::::0;14165:56;;;;::::1;::::0;;;::::1;::::0;13978:14;;14165:56:::1;::::0;::::1;-1:-1:-1::0;14325:31:135::1;::::0;;;:15:::1;:31;::::0;;;;;;:48;;::::1;::::0;::::1;::::0;;;;;;;::::1;::::0;;;::::1;14362:10;14325:48;::::0;;-1:-1:-1;;;;;;;;10816:3564:135:o;8115:94::-;8154:4;8177:16;;;;;;;;;;;:23;;;:25;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;21034:3510::-;21131:40;21174:34;;;:17;:34;;;;;;;;:51;;;;;;;;;;;;21131:94;;;;;;;;;;;;;;;;;;;;;;;;;21373:38;;;:20;:38;;;;;;21131:94;;;21373:38;;21372:39;21364:101;;;;;;;18838:2:357;21364:101:135;;;18820:21:357;18877:2;18857:18;;;18850:30;18916:34;18896:18;;;18889:62;18987:19;18967:18;;;18960:47;19024:19;;21364:101:135;18636:413:357;21364:101:135;21728:16;:26;;;:31;;21758:1;21728:31;21707:155;;;;;;;19256:2:357;21707:155:135;;;19238:21:357;19295:2;19275:18;;;19268:30;19334:34;19314:18;;;19307:62;19405:34;19385:18;;;19378:62;19477:15;19456:19;;;19449:44;19510:19;;21707:155:135;19054:481:357;21707:155:135;21873:16;21892:34;:16;:26;;;:28;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:32;;5038:9:177;4918:145;21892:34:135;21873:53;;22240:9;22211:38;;:16;:26;;;:38;;;22190:163;;;;;;;20029:2:357;22190:163:135;;;20011:21:357;20068:2;20048:18;;;20041:30;20107:34;20087:18;;;20080:62;20178:34;20158:18;;;20151:62;20250:16;20229:19;;;20222:45;20284:19;;22190:163:135;19827:482:357;22190:163:135;22532:28;22503:16;:26;;;22485:44;;:15;:44;;;;:::i;:::-;:75;22464:175;;;;;;;20835:2:357;22464:175:135;;;20817:21:357;20874:2;20854:18;;;20847:30;20913:34;20893:18;;;20886:62;20984:23;20964:18;;;20957:51;21025:19;;22464:175:135;20633:417:357;22464:175:135;22972:24;22943:16;:23;;;:25;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:53;;;;;;;;:::i;:::-;;22922:154;;;;;;;21257:2:357;22922:154:135;;;21239:21:357;21296:2;21276:18;;;21269:30;21335:34;21315:18;;;21308:62;21406:24;21386:18;;;21379:52;21448:19;;22922:154:135;21055:418:357;22922:154:135;23386:17;;;;23349:60;;:33;:16;:25;;;:27;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:31;;5038:9:177;4918:145;23349:33:135;:60;;;23341:106;;;;;;;14432:2:357;23341:106:135;;;14414:21:357;14471:2;14451:18;;;14444:30;14510:34;14490:18;;;14483:62;14581:3;14561:18;;;14554:31;14602:19;;23341:106:135;14230:397:357;23341:106:135;23722:26;;;;;;;;;23709:39;;;;;23688:161;;;;;;;21989:2:357;23688:161:135;;;21971:21:357;22028:2;22008:18;;;22001:30;22067:34;22047:18;;;22040:62;22138:34;22118:18;;;22111:62;22210:13;22189:19;;;22182:42;22241:19;;23688:161:135;21787:479:357;23688:161:135;24222:35;24184;:16;:27;;;:29;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:35;24166:53;;;;:15;:53;:::i;:::-;:91;24145:180;;;;;;;22473:2:357;24145:180:135;;;22455:21:357;22512:2;22492:18;;;22485:30;22551:34;22531:18;;;22524:62;22622:12;22602:18;;;22595:40;22652:19;;24145:180:135;22271:406:357;24145:180:135;24442:37;;;;:20;:37;;;;;;;;24441:38;24433:104;;;;;;;22884:2:357;24433:104:135;;;22866:21:357;22923:2;22903:18;;;22896:30;22962:34;22942:18;;;22935:62;23033:23;23013:18;;;23006:51;23074:19;;24433:104:135;22682:417:357;24433:104:135;21121:3423;;;21034:3510;;:::o;20049:185::-;20143:10;:8;:10::i;:::-;20129:24;;:10;:24;;;20125:51;;20162:14;;;;;;;;;;;;;;20125:51;20186:34;;;;;;:20;:34;;;;;:41;;;;20223:4;20186:41;;;20049:185::o;20481:228::-;20568:10;:8;:10::i;:::-;20554:24;;:10;:24;;;20550:51;;20587:14;;;;;;;;;;;;;;20550:51;20611:17;:29;;;;;;;20650:52;;;;;;;;;20686:15;20650:52;;;;;;20481:228::o;9921:77::-;:::o;14493:178::-;5766:8;:6;:8::i;:::-;5762:33;;;5783:12;;;;;;;;;;;;;;5762:33;14605:59:::1;14648:3;14653:10;14605:42;:59::i;:::-;14493:178:::0;:::o;6730:971::-;3100:19:43;3123:13;;;;;;3122:14;;3168:34;;;;-1:-1:-1;3186:12:43;;3201:1;3186:12;;;;:16;3168:34;3167:97;;;-1:-1:-1;3236:4:43;1465:19:59;:23;;;3208:55:43;;-1:-1:-1;3246:12:43;;;;;:17;3208:55;3146:190;;;;;;;23306:2:357;3146:190:43;;;23288:21:357;23345:2;23325:18;;;23318:30;23384:34;23364:18;;;23357:62;23455:16;23435:18;;;23428:44;23489:19;;3146:190:43;23104:410:357;3146:190:43;3346:12;:16;;;;3361:1;3346:16;;;3372:65;;;;3406:13;:20;;;;;;;;3372:65;6977:18:135::1;:40:::0;;;;;::::1;;::::0;;::::1;::::0;;;::::1;::::0;;;7027:12:::1;:28:::0;;;;::::1;::::0;;::::1;;::::0;;7065:16:::1;:36:::0;;;::::1;6977:40;7065:36:::0;;::::1;;;::::0;;7249:8:::1;::::0;::::1;7245:414;;7287:8;:38:::0;;1338:42:192::1;7287:38:135::0;;;::::1;;::::0;;7485:26:::1;:52:::0;;7603:45;;7485:52;7521:15:::1;7485:52;;;7603:45:::0;;;::::1;::::0;::::1;;::::0;;7245:414:::1;7669:25;:23;:25::i;:::-;3461:14:43::0;3457:99;;;3507:5;3491:21;;;;;;3531:14;;-1:-1:-1;23671:36:357;;3531:14:43;;23659:2:357;23644:18;3531:14:43;;;;;;;3090:472;6730:971:135;;;;:::o;9078:120::-;9143:6;9168:15;:10;9181:2;9168:15;:::i;:::-;:23;;9186:5;9168:23;:::i;:::-;9161:30;9078:120;-1:-1:-1;;9078:120:135:o;4315:52::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;4315:52:135;;-1:-1:-1;4315:52:135:o;1175:320:59:-;1465:19;;;:23;;;1175:320::o;3911:3974:137:-;4078:6;:19;4043:17;;4063:34;;4078:19;;;;;4063:12;:34;:::i;:::-;4043:54;;4108:28;4139:17;:15;:17::i;:::-;4108:48;;4166:26;4265:6;:27;;;4257:36;;4222:6;:23;;;4214:32;;4207:87;;;;:::i;:::-;4166:128;-1:-1:-1;4309:13:137;;4305:2229;;4666:6;:20;4629:19;;4651:59;;4691:19;;4666:20;;;;;4651:59;:::i;:::-;4629:81;;4724:19;4855:6;:34;;;4847:43;;4818:19;:73;;;;:::i;:::-;4762:6;:18;4747:50;;4785:12;;4762:18;;4747:50;:::i;:::-;4746:146;;;;:::i;:::-;5111:6;:18;4724:168;;-1:-1:-1;5033:17:137;;5053:232;;5096:50;;4724:168;;5111:18;;5096:50;:::i;:::-;5185:6;:21;;;5177:30;;5247:6;:21;;;5239:30;;5053:16;:232::i;:::-;5033:252;;5562:1;5550:9;:13;5546:741;;;5835:437;5882:239;5939:10;6004:6;:34;;;5996:43;;6096:1;6084:9;:13;;;;:::i;:::-;5882:16;:239::i;5835:437::-;5822:450;;5546:741;6380:49;;6481:42;6443:24;6510:12;6481:42;;;6380:6;6481:42;-1:-1:-1;;4305:2229:137;6628:6;:31;;6652:7;;6628:6;:20;;:31;;6652:7;;6628:31;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;6728:6;:23;;;6720:32;;6688:6;:20;;;;;;;;;;;;6680:29;;6673:80;6669:128;;;6776:10;;;;;;;;;;;;;;6669:128;6908:6;:18;6858:20;;6881:46;;6908:18;;6881:16;;;:46;:::i;:::-;6858:69;;7409:15;7442:31;7451:13;7466:6;7442:8;:31::i;:::-;7427:46;;:12;:46;:::i;:::-;7409:64;;7753:15;7785:9;7771:23;;:11;:23;:::i;:::-;7753:41;;7818:7;7808;:17;7804:75;;;7841:27;7850:17;7860:7;7850;:17;:::i;:::-;7841:8;:27::i;:::-;3975:3910;;;;;;3911:3974;;:::o;4456:211:196:-;4590:9;;4601:10;;;;;4613;;;;;4625:9;;;;4636:12;;;;4650:8;;;;4579:80;;4543:7;;4579:80;;4590:9;;4601:10;4650:8;4579:80;;:::i;:::-;;;;;;;;;;;;;4569:91;;;;;;4562:98;;4456:211;;;:::o;4419:2320:200:-;4589:4;4609:13;4632:15;4650:21;4660:7;4669:1;4650:9;:21::i;:::-;4632:39;;4782:10;4772:1146;;4894:10;4891:1;4884:21;5009:2;5005;4998:14;5747:56;5743:2;5736:68;5900:3;5896:2;5889:15;4772:1146;6666:4;6630;6589:9;6583:16;6549:2;6538:9;6534:18;6491:6;6449:7;6415:5;6389:309;6361:337;4419:2320;-1:-1:-1;;;;;;;4419:2320:200:o;4961:384:196:-;5060:7;5137:16;:24;;;5179:16;:26;;;5223:16;:41;;;5282:16;:32;;;5109:219;;;;;;;;;;27392:25:357;;;27448:2;27433:18;;27426:34;;;;27491:2;27476:18;;27469:34;27534:2;27519:18;;27512:34;27379:3;27364:19;;27161:391;1041:343:206;1234:11;1261:16;1280:19;1294:4;1280:13;:19::i;:::-;1261:38;;1318:59;1350:3;1355:6;1363;1371:5;1318:31;:59::i;:::-;1309:68;1041:343;-1:-1:-1;;;;;;1041:343:206:o;8340:234:137:-;4888:13:43;;;;;;;4880:69;;;;;;;27759:2:357;4880:69:43;;;27741:21:357;27798:2;27778:18;;;27771:30;27837:34;27817:18;;;27810:62;27908:13;27888:18;;;27881:41;27939:19;;4880:69:43;27557:407:357;4880:69:43;8415:6:137::1;:19:::0;;;::::1;;;;:24:::0;8411:157:::1;;8464:93;::::0;;::::1;::::0;::::1;::::0;;8494:6:::1;8464:93:::0;;;-1:-1:-1;8464:93:137::1;::::0;::::1;::::0;8541:12:::1;8464:93;;::::0;;;;;;;8455:102;::::1;;:6;:102:::0;8340:234::o;10247:152:135:-;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;10363:12:135;;:29;;;;;;;-1:-1:-1;;10363:12:135;;;;;:27;;:29;;;;;-1:-1:-1;;10363:29:135;;;;;;:12;:29;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;537:161:189:-;616:6;641:50;656:28;671:6;679:4;656:14;:28::i;:::-;686:4;641:14;:50::i;:::-;634:57;;537:161;;;;;;:::o;1040:228::-;1138:6;1257:4;1180:72;1213:19;1220:12;1257:4;1213:19;:::i;:::-;1205:28;;:4;:28;:::i;:::-;1235:16;:9;1247:4;1235:16;:::i;:::-;1180:24;:72::i;:::-;1164:89;;:12;:89;:::i;:::-;1163:98;;;;:::i;413:105:69:-;471:7;502:1;497;:6;;:14;;510:1;497:14;;;-1:-1:-1;506:1:69;;490:21;-1:-1:-1;413:105:69:o;407:192:190:-;461:9;484:18;505:9;484:30;;524:69;556:7;544:9;531:22;;:10;:22;:::i;:::-;:32;524:69;;;579:3;;;:::i;:::-;;;524:69;;;451:148;;407:192;:::o;3615:365:200:-;3696:4;3712:15;3931:2;3916:12;3909:5;3905:24;3901:33;3896:2;3887:7;3883:16;3879:56;3874:2;3867:5;3863:14;3860:76;3853:84;;3615:365;-1:-1:-1;;;;3615:365:200:o;2052:142:206:-;2116:18;2181:4;2171:15;;;;;;2154:33;;;;;;29671:19:357;;29715:2;29706:12;;29542:182;2154:33:206;;;;;;;;;;;;;2146:41;;2052:142;;;:::o;2253:281:205:-;2446:11;2482:45;2494:6;2502:24;2506:4;2512:6;2520:5;2502:3;:24::i;:::-;6693:17:191;;;;;;;6672;;;;;;;;;;:38;;6569:148;2482:45:205;2473:54;2253:281;-1:-1:-1;;;;;2253:281:205:o;311:102:71:-;367:6;397:1;392;:6;;:14;;405:1;392:14;;491:101;547:6;576:1;572;:5;:13;;584:1;572:13;;1208:273:106;1267:6;1391:36;491:4;1410:1;1399:8;1405:1;1399:5;:8::i;:::-;:12;;;;:::i;:::-;1398:28;;;;:::i;:::-;1391:6;:36::i;2830:6314:205:-;2923:19;2976:1;2962:4;:11;:15;2954:49;;;;;;;29931:2:357;2954:49:205;;;29913:21:357;29970:2;29950:18;;;29943:30;30009:23;29989:18;;;29982:51;30050:18;;2954:49:205;29729:345:357;2954:49:205;3014:23;3040:19;3052:6;3040:11;:19::i;:::-;3014:45;;3069:16;3088:21;3104:4;3088:15;:21::i;:::-;3069:40;;3119:26;3165:5;3148:23;;;;;;29671:19:357;;29715:2;29706:12;;29542:182;3148:23:205;;;;;;;;;;;;;3119:52;;3181:23;3295:9;3290:5790;3314:5;:12;3310:1;:16;3290:5790;;;3347:27;3377:5;3383:1;3377:8;;;;;;;;:::i;:::-;;;;;;;3347:38;;3516:3;:10;3497:15;:29;;3489:88;;;;;;;30470:2:357;3489:88:205;;;30452:21:357;30509:2;30489:18;;;30482:30;30548:34;30528:18;;;30521:62;30619:16;30599:18;;;30592:44;30653:19;;3489:88:205;30268:410:357;3489:88:205;3596:15;3615:1;3596:20;3592:837;;3768:19;;3758:30;;;;;;;3741:48;;3729:76;;3741:48;;3758:30;3741:48;29671:19:357;;;29715:2;29706:12;;29542:182;3741:48:205;;;;;;;;;;;;;3791:13;6693:17:191;;;;;;;6672;;;;;;;;;;:38;;6569:148;3729:76:205;3700:176;;;;;;;30885:2:357;3700:176:205;;;30867:21:357;30924:2;30904:18;;;30897:30;30963:31;30943:18;;;30936:59;31012:18;;3700:176:205;30683:353:357;3700:176:205;3592:837;;;3901:19;;:26;3931:2;-1:-1:-1;3897:532:205;;4097:19;;4087:30;;;;;;;4070:48;;4058:76;;4070:48;;4087:30;4070:48;29671:19:357;;;29715:2;29706:12;;29542:182;4058:76:205;4029:186;;;;;;;31243:2:357;4029:186:205;;;31225:21:357;31282:2;31262:18;;;31255:30;31321:34;31301:18;;;31294:62;31392:9;31372:18;;;31365:37;31419:19;;4029:186:205;31041:403:357;3897:532:205;4336:19;;6693:17:191;;;;;;;;;;6672;;;;;;;:38;4316:98:205;;;;;;;31651:2:357;4316:98:205;;;31633:21:357;31690:2;31670:18;;;31663:30;31729:34;31709:18;;;31702:62;31800:8;31780:18;;;31773:36;31826:19;;4316:98:205;31449:402:357;4316:98:205;936:14;803:2;949:1;936:14;:::i;:::-;4447:11;:19;;;:26;:48;4443:4627;;4538:3;:10;4519:15;:29;4515:1346;;5047:52;5067:11;:19;;;803:2;5067:31;;;;;;;;:::i;:::-;;;;;;;5047:19;:52::i;:::-;5038:61;;5145:1;5129:6;:13;:17;5121:89;;;;;;;32191:2:357;5121:89:205;;;32173:21:357;32230:2;32210:18;;;32203:30;32269:34;32249:18;;;32242:62;32340:29;32320:18;;;32313:57;32387:19;;5121:89:205;31989:423:357;5121:89:205;5322:1;5307:5;:12;:16;;;;:::i;:::-;5302:1;:21;5294:92;;;;;;;32619:2:357;5294:92:205;;;32601:21:357;32658:2;32638:18;;;32631:30;32697:34;32677:18;;;32670:62;32768:28;32748:18;;;32741:56;32814:19;;5294:92:205;32417:422:357;5294:92:205;5409:13;;;;;;;;4515:1346;5609:15;5633:3;5637:15;5633:20;;;;;;;;:::i;:::-;;;;;;;;;5627:27;;5609:45;;5676:33;5712:11;:19;;;5732:9;5712:30;;;;;;;;;;:::i;:::-;;;;;;;5676:66;;5780:20;5791:8;5780:10;:20::i;:::-;5764:36;-1:-1:-1;5822:20:205;5841:1;5822:20;;:::i;:::-;;;5447:414;;4443:4627;;;1105:1;5885:11;:19;;;:26;:59;5881:3189;;5964:17;5984:25;5997:11;5984:12;:25::i;:::-;5964:45;;6027:12;6048:4;6053:1;6048:7;;;;;;;;:::i;:::-;;;;;;;;-1:-1:-1;6074:12:205;6094:10;6103:1;6048:7;6094:10;:::i;:::-;6089:16;;:1;:16;:::i;:::-;6074:31;;6123:26;6152:25;6164:4;6170:6;6152:25;;:11;:25::i;:::-;6123:54;;6195:25;6223:33;6235:3;6240:15;6223:11;:33::i;:::-;6195:61;;6274:26;6303:51;6326:13;6341:12;6303:22;:51::i;:::-;6274:80;;6661:18;6637:13;:20;:42;6608:171;;;;;;;33408:2:357;6608:171:205;;;33390:21:357;33447:2;33427:18;;;33420:30;33486:34;33466:18;;;33459:62;33557:28;33537:18;;;33530:56;33603:19;;6608:171:205;33206:422:357;6608:171:205;6802:26;;;1447:1;6802:26;;:55;;-1:-1:-1;6832:25:205;;;1553:1;6832:25;6802:55;6798:2169;;;7498:18;7475:12;:19;:41;7442:185;;;;;;;33835:2:357;7442:185:205;;;33817:21:357;33874:2;33854:18;;;33847:30;33913:34;33893:18;;;33886:62;33984:31;33964:18;;;33957:59;34033:19;;7442:185:205;33633:425:357;7442:185:205;7985:43;8005:11;:19;;;8025:1;8005:22;;;;;;;;:::i;7985:43::-;7976:52;;8074:1;8058:6;:13;:17;8050:87;;;;;;;34265:2:357;8050:87:205;;;34247:21:357;34304:2;34284:18;;;34277:30;34343:34;34323:18;;;34316:62;34414:27;34394:18;;;34387:55;34459:19;;8050:87:205;34063:421:357;8050:87:205;8249:1;8234:5;:12;:16;;;;:::i;:::-;8229:1;:21;8221:90;;;;;;;34691:2:357;8221:90:205;;;34673:21:357;34730:2;34710:18;;;34703:30;34769:34;34749:18;;;34742:62;34840:26;34820:18;;;34813:54;34884:19;;8221:90:205;34489:420:357;8221:90:205;8334:13;;;;;;;;;;;;;;6798:2169;8376:31;;;;;:65;;-1:-1:-1;8411:30:205;;;1339:1;8411:30;8376:65;8372:595;;;8748:34;8759:11;:19;;;8779:1;8759:22;;;;;;;;:::i;:::-;;;;;;;8748:10;:34::i;:::-;8732:50;-1:-1:-1;8804:37:205;8823:18;8804:37;;:::i;:::-;;;8372:595;;;8888:60;;;;;35116:2:357;8888:60:205;;;35098:21:357;35155:2;35135:18;;;35128:30;35194:34;35174:18;;;35167:62;35265:20;35245:18;;;35238:48;35303:19;;8888:60:205;34914:414:357;8372:595:205;5946:3035;;;;;;5881:3189;;;9005:50;;;;;35535:2:357;9005:50:205;;;35517:21:357;35574:2;35554:18;;;35547:30;35613:34;35593:18;;;35586:62;35684:10;35664:18;;;35657:38;35712:19;;9005:50:205;35333:404:357;5881:3189:205;-1:-1:-1;3328:3:205;;;;:::i;:::-;;;;3290:5790;;;-1:-1:-1;9090:47:205;;;;;35944:2:357;9090:47:205;;;35926:21:357;35983:2;35963:18;;;35956:30;36022:34;36002:18;;;35995:62;36093:7;36073:18;;;36066:35;36118:19;;9090:47:205;35742:401:357;4596:2947:106;4644:8;4700:1;4696;:5;4688:27;;;;;;;36350:2:357;4688:27:106;;;36332:21:357;36389:1;36369:18;;;36362:29;36427:11;36407:18;;;36400:39;36456:18;;4688:27:106;36148:332:357;4688:27:106;5107:8;5145:2;5125:16;5138:1;5125:4;:16::i;:::-;5118:29;5175:3;:7;;;5161:22;;;;5208:17;;;6001:31;5997:35;;6052:5;;5459:2;6051:13;;;6068:32;6050:50;6120:5;;6119:13;;6136:33;6118:51;6189:5;;6188:13;;6205:33;6187:51;6258:5;;6257:13;;6274:33;6256:51;6327:5;;6326:13;;6343:32;6325:50;6395:5;;6394:13;;6411:30;6393:48;5398:31;5394:35;;5449:5;;5448:13;;5465:32;5447:50;5517:5;;5516:13;;5533:32;5515:50;5585:5;;5584:13;;5583:50;;5653:5;;5652:13;;5651:50;;5721:5;;5720:13;;;5719:50;;5787:5;;;:46;;6735:10;7125:43;7120:48;7232:71;:75;;;;7227:80;;;;7380:72;7375:77;7523:3;7517:9;;;-1:-1:-1;;4596:2947:106:o;1487:3103::-;1536:8;1718:21;1713:1;:26;1709:40;;-1:-1:-1;1748:1:106;;1487:3103;-1:-1:-1;1487:3103:106:o;1709:40::-;1948:21;1943:1;:26;1939:54;;1971:22;;;;;36687:2:357;1971:22:106;;;36669:21:357;36726:2;36706:18;;;36699:30;36765:14;36745:18;;;36738:42;36797:18;;1971:22:106;36485:336:357;1939:54:106;2266:5;2260:2;2255:7;;;2254:17;;-1:-1:-1;2535:8:106;2601:2;2559:29;2548:7;;;2547:41;2591:5;2547:49;2546:57;;2629:29;2625:33;;2621:37;;;3300:35;;;3355:5;;2935:2;3354:13;;;3371:32;3353:50;3423:5;;3422:13;;3421:51;;3492:5;;3491:13;;3508:34;3490:52;3562:5;;3561:13;;3560:53;;3633:5;;3632:13;;3649:35;3631:53;2941:32;2874:31;2870:35;;2925:5;;2924:13;;2923:50;;;2998:5;;;:40;;3058:5;3057:13;;;3074:35;3056:53;3127:5;;;3136:40;3127:50;4002:10;4502:49;4489:62;4564:3;:7;;;;4488:84;;;;;;-1:-1:-1;;1487:3103:106:o;9434:390:205:-;9553:13;;9500:24;;9553:13;9585:22;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;;;;;;;9585:22:205;;;;;;;;;;;;;;;;9576:31;;9622:9;9617:201;9641:6;9637:1;:10;9617:201;;;9676:72;;;;;;;;9696:6;9703:1;9696:9;;;;;;;;:::i;:::-;;;;;;;9676:72;;;;9716:29;9735:6;9742:1;9735:9;;;;;;;;:::i;:::-;;;;;;;9716:18;:29::i;:::-;9676:72;;;9664:6;9671:1;9664:9;;;;;;;;:::i;:::-;;;;;;;;;;:84;9790:3;;9617:201;;;;9526:298;9434:390;;;:::o;4332:1978:191:-;4395:12;4419:21;4550:4;4544:11;4532:23;;4663:6;4657:13;4836:11;4830:4;4826:22;5195:4;5180:13;5176:24;5169:4;5165:9;5161:40;5151:8;5147:55;5141:4;5134:69;5293:13;5283:8;5276:31;;5434:4;5426:6;5422:17;5571:4;5561:8;5557:19;5662:4;5647:622;5675:11;5672:1;5669:18;5647:622;;;5854:1;5848:4;5844:12;5830;5826:31;5996:1;5984:10;5980:18;5974:25;5968:4;5963:37;6119:1;6113:4;6109:12;6101:6;6093:29;6249:4;6246:1;6242:12;6235:4;6227:6;6223:17;6215:40;-1:-1:-1;;5702:4:191;5695:12;5647:622;;;-1:-1:-1;6295:8:191;;4332:1978;-1:-1:-1;;;;;4332:1978:191:o;3993:464:203:-;4055:17;4085:18;4105;4125:20;4149:18;4163:3;4149:13;:18::i;:::-;4084:83;;-1:-1:-1;4084:83:203;-1:-1:-1;4084:83:203;-1:-1:-1;4198:21:203;4186:8;:33;;;;;;;;:::i;:::-;;4178:103;;;;;;;37028:2:357;4178:103:203;;;37010:21:357;37067:2;37047:18;;;37040:30;37106:34;37086:18;;;37079:62;37177:27;37157:18;;;37150:55;37222:19;;4178:103:203;36826:421:357;4178:103:203;4314:23;4327:10;4314;:23;:::i;:::-;4300:10;;:37;4292:102;;;;;;;37454:2:357;4292:102:203;;;37436:21:357;37493:2;37473:18;;;37466:30;37532:34;37512:18;;;37505:62;37603:22;37583:18;;;37576:50;37643:19;;4292:102:203;37252:416:357;4292:102:203;4412:38;4418:3;:7;;;4427:10;4439;4412:5;:38::i;10121:193:205:-;10195:16;10244:2;10229:5;:12;;;:17;:78;;10281:26;10301:5;10281:19;:26::i;:::-;10229:78;;;10249:29;10272:5;10249:22;:29::i;10495:172::-;10562:21;10606:54;10622:37;10642:5;:13;;;10656:1;10642:16;;;;;;;;:::i;10622:37::-;10606:15;:54::i;3805:237:191:-;3880:12;3918:6;:13;3908:6;:23;3904:70;;-1:-1:-1;3954:9:191;;;;;;;;;-1:-1:-1;3954:9:191;;3947:16;;3904:70;3990:45;3996:6;4004;4028;4012;:13;:22;;;;:::i;:::-;3990:5;:45::i;10892:321:205:-;10980:15;11007:11;11034:2;:9;11022:2;:9;:21;11021:47;;11059:2;:9;11021:47;;;11047:2;:9;11021:47;11007:61;;11078:129;11095:3;11085:7;:13;:43;;;;;11117:2;11120:7;11117:11;;;;;;;;:::i;:::-;;;;;;;;;11102:26;;;:2;11105:7;11102:11;;;;;;;;:::i;:::-;;;;;;;:26;11085:43;11078:129;;;11173:9;;;;;11078:129;;;10997:216;10892:321;;;;:::o;15328:575:106:-;15376:9;15409:1;15405;:5;15397:27;;;;;;;36350:2:357;15397:27:106;;;36332:21:357;36389:1;36369:18;;;36362:29;36427:11;36407:18;;;36400:39;36456:18;;15397:27:106;36148:332:357;15397:27:106;-1:-1:-1;15821:1:106;15473:34;-1:-1:-1;;15467:1:106;15463:49;15566:9;;;15546:18;15543:33;15540:1;15536:41;15530:48;15624:9;;;15612:10;15609:25;15606:1;15602:33;15596:40;15678:9;;;15670:6;15667:21;15664:1;15660:29;15654:36;15730:9;;;15724:4;15721:19;15718:1;15714:27;;;15708:34;;;15781:9;;;15776:3;15773:18;15770:1;15766:26;15760:33;15832:9;;;15824:18;;;15817:26;;15811:33;15876:9;;;-1:-1:-1;15862:25:106;;15328:575::o;3732:130:203:-;3791:21;3831:24;3840:14;3850:3;3840:9;:14::i;:::-;3831:8;:24::i;5246:4079::-;5335:15;5352;5369:17;5705:1;5692:3;:10;;;:14;5684:101;;;;;;;37875:2:357;5684:101:203;;;37857:21:357;37914:2;37894:18;;;37887:30;37953:34;37933:18;;;37926:62;38024:34;38004:18;;;37997:62;38096:12;38075:19;;;38068:41;38126:19;;5684:101:203;37673:478:357;5684:101:203;5816:7;;;;5898:10;;5796:17;5890:19;5943:4;5933:14;;5929:3390;;5999:1;6002;6005:21;5991:36;;;;;;;;;;5929:3390;6058:4;6048:6;:14;6044:3275;;6164:14;6181:13;6190:4;6181:6;:13;:::i;:::-;6164:30;;6247:6;6234:3;:10;;;:19;6209:140;;;;;;;38358:2:357;6209:140:203;;;38340:21:357;38397:2;38377:18;;;38370:30;38436:34;38416:18;;;38409:62;38507:34;38487:18;;;38480:62;38579:16;38558:19;;;38551:45;38613:19;;6209:140:203;38156:482:357;6209:140:203;6471:1;6462:11;;;6456:18;6476:14;6452:39;;6544:11;;;;:41;;-1:-1:-1;6559:26:203;;;;;;6544:41;6519:177;;;;;;;38845:2:357;6519:177:203;;;38827:21:357;38884:2;38864:18;;;38857:30;38923:34;38903:18;;;38896:62;38994:34;38974:18;;;38967:62;39066:15;39045:19;;;39038:44;39099:19;;6519:177:203;38643:481:357;6519:177:203;-1:-1:-1;6719:1:203;;-1:-1:-1;6722:6:203;-1:-1:-1;6730:21:203;;-1:-1:-1;6711:41:203;;-1:-1:-1;;6711:41:203;6044:3275;6783:4;6773:6;:14;6769:2550;;6831:19;6853:13;6862:4;6853:6;:13;:::i;:::-;6831:35;;6919:11;6906:3;:10;;;:24;6881:164;;;;;;;39331:2:357;6881:164:203;;;39313:21:357;39370:2;39350:18;;;39343:30;39409:34;39389:18;;;39382:62;39480:34;39460:18;;;39453:62;39552:19;39531;;;39524:48;39589:19;;6881:164:203;39129:485:357;6881:164:203;7167:1;7158:11;;7152:18;7172:14;7148:39;7060:25;7240:26;;;7215:143;;;;;;;39821:2:357;7215:143:203;;;39803:21:357;39860:2;39840:18;;;39833:30;39899:34;39879:18;;;39872:62;39970:34;39950:18;;;39943:62;40042:12;40021:19;;;40014:41;40072:19;;7215:143:203;39619:478:357;7215:143:203;7488:1;7479:11;;7473:18;7455:1;7451:19;;7446:3;7442:29;7438:54;7537:2;7528:11;;7520:96;;;;;;;40304:2:357;7520:96:203;;;40286:21:357;40343:2;40323:18;;;40316:30;40382:34;40362:18;;;40355:62;40453:34;40433:18;;;40426:62;40525:10;40504:19;;;40497:39;40553:19;;7520:96:203;40102:476:357;7520:96:203;7669:20;7683:6;7669:11;:20;:::i;:::-;7656:10;;:33;7631:168;;;;;;;40785:2:357;7631:168:203;;;40767:21:357;40824:2;40804:18;;;40797:30;40863:34;40843:18;;;40836:62;40934:34;40914:18;;;40907:62;41006:14;40985:19;;;40978:43;41038:19;;7631:168:203;40583:480:357;7631:168:203;7822:15;7826:11;7822:1;:15;:::i;:::-;7814:55;-1:-1:-1;7839:6:203;-1:-1:-1;7847:21:203;;-1:-1:-1;7814:55:203;;-1:-1:-1;;;;7814:55:203;6769:2550;7900:4;7890:6;:14;7886:1433;;8003:15;8021:13;8030:4;8021:6;:13;:::i;:::-;8003:31;;8070:7;8057:3;:10;;;:20;8049:107;;;;;;;41270:2:357;8049:107:203;;;41252:21:357;41309:2;41289:18;;;41282:30;41348:34;41328:18;;;41321:62;41419:34;41399:18;;;41392:62;41491:12;41470:19;;;41463:41;41521:19;;8049:107:203;41068:478:357;8049:107:203;8179:1;;-1:-1:-1;8182:7:203;-1:-1:-1;8179:1:203;;-1:-1:-1;8171:42:203;;-1:-1:-1;;8171:42:203;7886:1433;8270:20;8293:13;8302:4;8293:6;:13;:::i;:::-;8270:36;;8359:12;8346:3;:10;;;:25;8321:161;;;;;;;41753:2:357;8321:161:203;;;41735:21:357;41792:2;41772:18;;;41765:30;41831:34;41811:18;;;41804:62;41902:34;41882:18;;;41875:62;41974:15;41953:19;;;41946:44;42007:19;;8321:161:203;41551:481:357;8321:161:203;8604:1;8595:11;;8589:18;8609:14;8585:39;8497:25;8677:26;;;8652:141;;;;;;;42239:2:357;8652:141:203;;;42221:21:357;42278:2;42258:18;;;42251:30;42317:34;42297:18;;;42290:62;42388:34;42368:18;;;42361:62;42460:10;42439:19;;;42432:39;42488:19;;8652:141:203;42037:476:357;8652:141:203;8926:1;8917:11;;8911:18;8892:1;8888:20;;8883:3;8879:30;8875:55;8976:2;8966:12;;8958:95;;;;;;;42720:2:357;8958:95:203;;;42702:21:357;42759:2;42739:18;;;42732:30;42798:34;42778:18;;;42771:62;42869:34;42849:18;;;42842:62;42941:8;42920:19;;;42913:37;42967:19;;8958:95:203;42518:474:357;8958:95:203;9106:22;9121:7;9106:12;:22;:::i;:::-;9093:10;;:35;9068:168;;;;;;;43199:2:357;9068:168:203;;;43181:21:357;43238:2;43218:18;;;43211:30;43277:34;43257:18;;;43250:62;43348:34;43328:18;;;43321:62;43420:12;43399:19;;;43392:41;43450:19;;9068:168:203;42997:478:357;9068:168:203;9259:16;9263:12;9259:1;:16;:::i;:::-;9251:57;-1:-1:-1;9277:7:203;-1:-1:-1;9286:21:203;;-1:-1:-1;9251:57:203;;-1:-1:-1;;;;9251:57:203;5246:4079;;;;;;:::o;9585:737::-;9676:17;9722:7;9712:18;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;9712:18:203;-1:-1:-1;9705:25:203;-1:-1:-1;9740:54:203;;9772:11;9740:54;10010:11;10024:36;10053:7;10045:4;10024:36;:::i;:::-;10010:50;;10115:2;10109:4;10105:13;10140:1;10154:87;10168:7;10165:1;10162:14;10154:87;;;10226:11;;;10220:18;10206:12;;;10199:40;10191:2;10184:10;10154:87;;;10264:7;10261:1;10258:14;10255:51;;;10302:1;10292:7;10286:4;10282:18;10275:29;10255:51;;;10079:237;9585:737;;;;;:::o;4847:137::-;4912:17;4948:29;4954:3;:7;;;4963:1;4966:3;:10;;;4948:5;:29::i;660:2816:191:-;752:12;824:7;808;818:2;808:12;:23;;800:50;;;;;;;43682:2:357;800:50:191;;;43664:21:357;43721:2;43701:18;;;43694:30;43760:16;43740:18;;;43733:44;43794:18;;800:50:191;43480:338:357;800:50:191;892:6;881:7;872:6;:16;:26;;864:53;;;;;;;43682:2:357;864:53:191;;;43664:21:357;43721:2;43701:18;;;43694:30;43760:16;43740:18;;;43733:44;43794:18;;864:53:191;43480:338:357;864:53:191;965:7;956:6;:16;939:6;:13;:33;;931:63;;;;;;;44025:2:357;931:63:191;;;44007:21:357;44064:2;44044:18;;;44037:30;44103:19;44083:18;;;44076:47;44140:18;;931:63:191;43823:341:357;931:63:191;1015:22;1078:15;;1106:1931;;;;3178:4;3172:11;3159:24;;3365:1;3354:9;3347:20;3413:4;3402:9;3398:20;3392:4;3385:34;1071:2362;;1106:1931;1288:4;1282:11;1269:24;;1947:2;1938:7;1934:16;2329:9;2322:17;2316:4;2312:28;2300:9;2289;2285:25;2281:60;2377:7;2373:2;2369:16;2629:6;2615:9;2608:17;2602:4;2598:28;2586:9;2578:6;2574:22;2570:57;2566:70;2403:389;2662:3;2658:2;2655:11;2403:389;;;2780:9;;2769:21;;2703:4;2695:13;;;;2735;2403:389;;;-1:-1:-1;;2810:26:191;;;3018:2;3001:11;3014:7;2997:25;2991:4;2984:39;-1:-1:-1;1071:2362:191;-1:-1:-1;3460:9:191;660:2816;-1:-1:-1;;;;660:2816:191:o;1298:390:203:-;-1:-1:-1;;;;;;;;;;;;;;;;;1453:1:203;1440:3;:10;:14;1432:101;;;;;;;37875:2:357;1432:101:203;;;37857:21:357;37914:2;37894:18;;;37887:30;37953:34;37933:18;;;37926:62;38024:34;38004:18;;;37997:62;38096:12;38075:19;;;38068:41;38126:19;;1432:101:203;37673:478:357;1432:101:203;-1:-1:-1;1640:41:203;;;;;;;;;1658:10;;1640:41;;1610:2;1601:12;;;1640:41;;;;;;;;1298:390::o;1840:1740::-;1901:21;1935:18;1955;1975:20;1999:18;2013:3;1999:13;:18::i;:::-;1934:83;;-1:-1:-1;1934:83:203;-1:-1:-1;1934:83:203;-1:-1:-1;2048:21:203;2036:8;:33;;;;;;;;:::i;:::-;;2028:102;;;;;;;44371:2:357;2028:102:203;;;44353:21:357;44410:2;44390:18;;;44383:30;44449:34;44429:18;;;44422:62;44520:26;44500:18;;;44493:54;44564:19;;2028:102:203;44169:420:357;2028:102:203;2176:10;;2149:23;2162:10;2149;:23;:::i;:::-;:37;2141:100;;;;;;;44796:2:357;2141:100:203;;;44778:21:357;44835:2;44815:18;;;44808:30;44874:34;44854:18;;;44847:62;44945:20;44925:18;;;44918:48;44983:19;;2141:100:203;44594:414:357;2141:100:203;2651:30;;;1123:2;2651:30;;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;;;;;;;2651:30:203;;;;;;;;;;;;;;-1:-1:-1;2644:37:203;-1:-1:-1;2692:17:203;2740:10;2760:681;2776:10;;2767:19;;2760:681;;;2803:18;2823;2846:150;2877:105;;;;;;;;2908:6;2895:3;:10;;;:19;;;;:::i;:::-;2877:105;;;;2972:6;2961:3;:7;;;2940:38;;;;:::i;:::-;2877:105;;2846:13;:150::i;:::-;2802:194;;;;;3201:153;;;;;;;;3248:10;3235;:23;;;;:::i;:::-;3201:153;;;;3332:6;3321:3;:7;;;3300:38;;;;:::i;:::-;3201:153;;;3183:4;3188:9;3183:15;;;;;;;;:::i;:::-;;;;;;;;;;:171;3369:14;3382:1;3369:14;;:::i;:::-;;-1:-1:-1;3407:23:203;3420:10;3407;:23;:::i;:::-;3397:33;;;;:::i;:::-;;;2788:653;;2760:681;;;-1:-1:-1;3541:23:203;;-1:-1:-1;3548:4:203;;1840:1740;-1:-1:-1;;;1840:1740:203:o;753:184:357:-;805:77;802:1;795:88;902:4;899:1;892:15;926:4;923:1;916:15;942:334;1013:2;1007:9;1069:2;1059:13;;1074:66;1055:86;1043:99;;1172:18;1157:34;;1193:22;;;1154:62;1151:88;;;1219:18;;:::i;:::-;1255:2;1248:22;942:334;;-1:-1:-1;942:334:357:o;1281:154::-;1367:42;1360:5;1356:54;1349:5;1346:65;1336:93;;1425:1;1422;1415:12;1440:589;1482:5;1535:3;1528:4;1520:6;1516:17;1512:27;1502:55;;1553:1;1550;1543:12;1502:55;1589:6;1576:20;1615:18;1611:2;1608:26;1605:52;;;1637:18;;:::i;:::-;1681:114;1789:4;1720:66;1713:4;1709:2;1705:13;1701:86;1697:97;1681:114;:::i;:::-;1820:2;1811:7;1804:19;1866:3;1859:4;1854:2;1846:6;1842:15;1838:26;1835:35;1832:55;;;1883:1;1880;1873:12;1832:55;1948:2;1941:4;1933:6;1929:17;1922:4;1913:7;1909:18;1896:55;1996:1;1971:16;;;1989:4;1967:27;1960:38;;;;1975:7;1440:589;-1:-1:-1;;;1440:589:357:o;2034:1032::-;2102:5;2150:4;2138:9;2133:3;2129:19;2125:30;2122:50;;;2168:1;2165;2158:12;2122:50;2201:2;2195:9;2243:4;2235:6;2231:17;2267:18;2335:6;2323:10;2320:22;2315:2;2303:10;2300:18;2297:46;2294:72;;;2346:18;;:::i;:::-;2386:10;2382:2;2375:22;2415:6;2406:15;;2458:9;2445:23;2437:6;2430:39;2521:2;2510:9;2506:18;2493:32;2478:47;;2534:33;2559:7;2534:33;:::i;:::-;2600:7;2595:2;2587:6;2583:15;2576:32;2660:2;2649:9;2645:18;2632:32;2617:47;;2673:33;2698:7;2673:33;:::i;:::-;2739:7;2734:2;2726:6;2722:15;2715:32;2808:2;2797:9;2793:18;2780:32;2775:2;2767:6;2763:15;2756:57;2875:3;2864:9;2860:19;2847:33;2841:3;2833:6;2829:16;2822:59;2932:3;2921:9;2917:19;2904:33;2890:47;;2960:2;2952:6;2949:14;2946:34;;;2976:1;2973;2966:12;2946:34;;3014:45;3055:3;3046:6;3035:9;3031:22;3014:45;:::i;:::-;3008:3;3000:6;2996:16;2989:71;;;2034:1032;;;;:::o;3071:510::-;3180:6;3188;3241:2;3229:9;3220:7;3216:23;3212:32;3209:52;;;3257:1;3254;3247:12;3209:52;3297:9;3284:23;3330:18;3322:6;3319:30;3316:50;;;3362:1;3359;3352:12;3316:50;3385:72;3449:7;3440:6;3429:9;3425:22;3385:72;:::i;:::-;3375:82;;;3507:2;3496:9;3492:18;3479:32;3520:31;3545:5;3520:31;:::i;:::-;3570:5;3560:15;;;3071:510;;;;;:::o;3817:270::-;3899:6;3952:2;3940:9;3931:7;3927:23;3923:32;3920:52;;;3968:1;3965;3958:12;3920:52;4007:9;3994:23;4026:31;4051:5;4026:31;:::i;4284:1175::-;4486:6;4494;4502;4510;4518;4562:9;4553:7;4549:23;4592:3;4588:2;4584:12;4581:32;;;4609:1;4606;4599:12;4581:32;4649:9;4636:23;4678:18;4719:2;4711:6;4708:14;4705:34;;;4735:1;4732;4725:12;4705:34;4758:72;4822:7;4813:6;4802:9;4798:22;4758:72;:::i;:::-;4748:82;;4877:2;4866:9;4862:18;4849:32;4839:42;;4974:3;4905:66;4901:2;4897:75;4893:85;4890:105;;;4991:1;4988;4981:12;4890:105;5029:2;5018:9;5014:18;5004:28;;5085:3;5074:9;5070:19;5057:33;5041:49;;5115:2;5105:8;5102:16;5099:36;;;5131:1;5128;5121:12;5099:36;5169:8;5158:9;5154:24;5144:34;;5216:7;5209:4;5205:2;5201:13;5197:27;5187:55;;5238:1;5235;5228:12;5187:55;5278:2;5265:16;5251:30;;5304:2;5296:6;5293:14;5290:34;;;5320:1;5317;5310:12;5290:34;;5373:7;5368:2;5358:6;5355:1;5351:14;5347:2;5343:23;5339:32;5336:45;5333:65;;;5394:1;5391;5384:12;5333:65;4284:1175;;;;-1:-1:-1;4284:1175:357;;-1:-1:-1;;;5425:2:357;5417:11;;5447:6;4284:1175::o;5669:180::-;5728:6;5781:2;5769:9;5760:7;5756:23;5752:32;5749:52;;;5797:1;5794;5787:12;5749:52;-1:-1:-1;5820:23:357;;5669:180;-1:-1:-1;5669:180:357:o;6036:258::-;6108:1;6118:113;6132:6;6129:1;6126:13;6118:113;;;6208:11;;;6202:18;6189:11;;;6182:39;6154:2;6147:10;6118:113;;;6249:6;6246:1;6243:13;6240:48;;;-1:-1:-1;;6284:1:357;6266:16;;6259:27;6036:258::o;6299:317::-;6341:3;6379:5;6373:12;6406:6;6401:3;6394:19;6422:63;6478:6;6471:4;6466:3;6462:14;6455:4;6448:5;6444:16;6422:63;:::i;:::-;6530:2;6518:15;6535:66;6514:88;6505:98;;;;6605:4;6501:109;;6299:317;-1:-1:-1;;6299:317:357:o;6621:220::-;6770:2;6759:9;6752:21;6733:4;6790:45;6831:2;6820:9;6816:18;6808:6;6790:45;:::i;6846:315::-;6914:6;6922;6975:2;6963:9;6954:7;6950:23;6946:32;6943:52;;;6991:1;6988;6981:12;6943:52;7027:9;7014:23;7004:33;;7087:2;7076:9;7072:18;7059:32;7100:31;7125:5;7100:31;:::i;7166:144::-;7274:10;7267:5;7263:22;7256:5;7253:33;7243:61;;7300:1;7297;7290:12;7315:300;7405:6;7458:2;7446:9;7437:7;7433:23;7429:32;7426:52;;;7474:1;7471;7464:12;7426:52;7513:9;7500:23;7532:53;7579:5;7532:53;:::i;7620:375::-;7720:6;7773:2;7761:9;7752:7;7748:23;7744:32;7741:52;;;7789:1;7786;7779:12;7741:52;7829:9;7816:23;7862:18;7854:6;7851:30;7848:50;;;7894:1;7891;7884:12;7848:50;7917:72;7981:7;7972:6;7961:9;7957:22;7917:72;:::i;:::-;7907:82;7620:375;-1:-1:-1;;;;7620:375:357:o;8000:800::-;8193:6;8201;8209;8217;8270:3;8258:9;8249:7;8245:23;8241:33;8238:53;;;8287:1;8284;8277:12;8238:53;8326:9;8313:23;8345:31;8370:5;8345:31;:::i;:::-;8395:5;-1:-1:-1;8452:2:357;8437:18;;8424:32;8465:33;8424:32;8465:33;:::i;:::-;8517:7;-1:-1:-1;8576:2:357;8561:18;;8548:32;8589:33;8548:32;8589:33;:::i;:::-;8641:7;-1:-1:-1;8700:2:357;8685:18;;8672:32;8713:55;8672:32;8713:55;:::i;:::-;8000:800;;;;-1:-1:-1;8000:800:357;;-1:-1:-1;;8000:800:357:o;8805:129::-;8890:18;8883:5;8879:30;8872:5;8869:41;8859:69;;8924:1;8921;8914:12;8939:245;8997:6;9050:2;9038:9;9029:7;9025:23;9021:32;9018:52;;;9066:1;9063;9056:12;9018:52;9105:9;9092:23;9124:30;9148:5;9124:30;:::i;9189:248::-;9257:6;9265;9318:2;9306:9;9297:7;9293:23;9289:32;9286:52;;;9334:1;9331;9324:12;9286:52;-1:-1:-1;;9357:23:357;;;9427:2;9412:18;;;9399:32;;-1:-1:-1;9189:248:357:o;10206:118::-;10292:5;10285:13;10278:21;10271:5;10268:32;10258:60;;10314:1;10311;10304:12;10329:799;10429:6;10437;10445;10453;10461;10514:3;10502:9;10493:7;10489:23;10485:33;10482:53;;;10531:1;10528;10521:12;10482:53;10570:9;10557:23;10589:31;10614:5;10589:31;:::i;:::-;10639:5;-1:-1:-1;10691:2:357;10676:18;;10663:32;;-1:-1:-1;10747:2:357;10732:18;;10719:32;10760;10719;10760;:::i;:::-;10811:7;-1:-1:-1;10870:2:357;10855:18;;10842:32;10883:30;10842:32;10883:30;:::i;:::-;10932:7;-1:-1:-1;10990:3:357;10975:19;;10962:33;11018:18;11007:30;;11004:50;;;11050:1;11047;11040:12;11004:50;11073:49;11114:7;11105:6;11094:9;11090:22;11073:49;:::i;:::-;11063:59;;;10329:799;;;;;;;;:::o;11392:642::-;11655:6;11650:3;11643:19;11692:6;11687:2;11682:3;11678:12;11671:28;11751:66;11742:6;11737:3;11733:16;11729:89;11724:2;11719:3;11715:12;11708:111;11872:6;11865:14;11858:22;11853:3;11849:32;11844:2;11839:3;11835:12;11828:54;11625:3;11911:6;11905:13;11927:60;11980:6;11975:2;11970:3;11966:12;11961:2;11953:6;11949:15;11927:60;:::i;:::-;12007:16;;;;12025:2;12003:25;;11392:642;-1:-1:-1;;;;;;11392:642:357:o;12694:251::-;12764:6;12817:2;12805:9;12796:7;12792:23;12788:32;12785:52;;;12833:1;12830;12823:12;12785:52;12865:9;12859:16;12884:31;12909:5;12884:31;:::i;13382:626::-;13556:6;13564;13572;13625:2;13613:9;13604:7;13600:23;13596:32;13593:52;;;13641:1;13638;13631:12;13593:52;13673:9;13667:16;13692:53;13739:5;13692:53;:::i;:::-;13814:2;13799:18;;13793:25;13764:5;;-1:-1:-1;13827:32:357;13793:25;13827:32;:::i;:::-;13930:2;13915:18;;13909:25;13878:7;;-1:-1:-1;13943:33:357;13909:25;13943:33;:::i;:::-;13995:7;13985:17;;;13382:626;;;;;:::o;14013:212::-;14111:6;14164:2;14152:9;14143:7;14139:23;14135:32;14132:52;;;14180:1;14177;14170:12;14132:52;-1:-1:-1;14203:16:357;;14013:212;-1:-1:-1;14013:212:357:o;14632:648::-;14726:6;14779:3;14767:9;14758:7;14754:23;14750:33;14747:53;;;14796:1;14793;14786:12;14747:53;14829:2;14823:9;14871:3;14863:6;14859:16;14941:6;14929:10;14926:22;14905:18;14893:10;14890:34;14887:62;14884:88;;;14952:18;;:::i;:::-;14992:10;14988:2;14981:22;;15040:9;15027:23;15019:6;15012:39;15112:2;15101:9;15097:18;15084:32;15079:2;15071:6;15067:15;15060:57;15178:2;15167:9;15163:18;15150:32;15145:2;15137:6;15133:15;15126:57;15244:2;15233:9;15229:18;15216:32;15211:2;15203:6;15199:15;15192:57;15268:6;15258:16;;;14632:648;;;;:::o;15695:184::-;15747:77;15744:1;15737:88;15844:4;15841:1;15834:15;15868:4;15865:1;15858:15;15884:277;15971:6;16024:2;16012:9;16003:7;15999:23;15995:32;15992:52;;;16040:1;16037;16030:12;15992:52;16072:9;16066:16;16111:1;16104:5;16101:12;16091:40;;16127:1;16124;16117:12;17028:934;17164:9;17198:18;17239:2;17231:6;17228:14;17225:40;;;17245:18;;:::i;:::-;17291:6;17288:1;17284:14;17317:4;17341:28;17365:2;17361;17357:11;17341:28;:::i;:::-;17403:19;;;17473:14;;;;17438:12;;;;17510:14;17499:26;;17496:46;;;17538:1;17535;17528:12;17496:46;17562:5;17576:353;17592:6;17587:3;17584:15;17576:353;;;17678:3;17665:17;17714:2;17701:11;17698:19;17695:109;;;17758:1;17787:2;17783;17776:14;17695:109;17829:57;17871:14;17857:11;17850:5;17846:23;17829:57;:::i;:::-;17817:70;;-1:-1:-1;17907:12:357;;;;17609;;17576:353;;;-1:-1:-1;17951:5:357;17028:934;-1:-1:-1;;;;;;;17028:934:357:o;18386:245::-;18453:6;18506:2;18494:9;18485:7;18481:23;18477:32;18474:52;;;18522:1;18519;18512:12;18474:52;18554:9;18548:16;18573:28;18595:5;18573:28;:::i;19540:282::-;19642:6;19695:2;19683:9;19674:7;19670:23;19666:32;19663:52;;;19711:1;19708;19701:12;19663:52;19743:9;19737:16;19762:30;19786:5;19762:30;:::i;20314:184::-;20366:77;20363:1;20356:88;20463:4;20460:1;20453:15;20487:4;20484:1;20477:15;20503:125;20543:4;20571:1;20568;20565:8;20562:34;;;20576:18;;:::i;:::-;-1:-1:-1;20613:9:357;;20503:125::o;21478:304::-;21579:6;21632:2;21620:9;21611:7;21607:23;21603:32;21600:52;;;21648:1;21645;21638:12;21600:52;21680:9;21674:16;21699:53;21746:5;21699:53;:::i;23718:270::-;23757:7;23789:18;23834:2;23831:1;23827:10;23864:2;23861:1;23857:10;23920:3;23916:2;23912:12;23907:3;23904:21;23897:3;23890:11;23883:19;23879:47;23876:73;;;23929:18;;:::i;:::-;23969:13;;23718:270;-1:-1:-1;;;;23718:270:357:o;23993:236::-;24032:3;24060:18;24105:2;24102:1;24098:10;24135:2;24132:1;24128:10;24166:3;24162:2;24158:12;24153:3;24150:21;24147:47;;;24174:18;;:::i;:::-;24210:13;;23993:236;-1:-1:-1;;;;23993:236:357:o;24234:184::-;24286:77;24283:1;24276:88;24383:4;24380:1;24373:15;24407:4;24404:1;24397:15;24423:308;24462:1;24488;24478:35;;24493:18;;:::i;:::-;24610:66;24607:1;24604:73;24535:66;24532:1;24529:73;24525:153;24522:179;;;24681:18;;:::i;:::-;-1:-1:-1;24715:10:357;;24423:308::o;24736:369::-;24775:4;24811:1;24808;24804:9;24920:1;24852:66;24848:74;24845:1;24841:82;24836:2;24829:10;24825:99;24822:125;;;24927:18;;:::i;:::-;25046:1;24978:66;24974:74;24971:1;24967:82;24963:2;24959:91;24956:117;;;25053:18;;:::i;:::-;-1:-1:-1;;25090:9:357;;24736:369::o;25110:655::-;25149:7;25181:66;25273:1;25270;25266:9;25301:1;25298;25294:9;25346:1;25342:2;25338:10;25335:1;25332:17;25327:2;25323;25319:11;25315:35;25312:61;;;25353:18;;:::i;:::-;25392:66;25484:1;25481;25477:9;25531:1;25527:2;25522:11;25519:1;25515:19;25510:2;25506;25502:11;25498:37;25495:63;;;25538:18;;:::i;:::-;25584:1;25581;25577:9;25567:19;;25631:1;25627:2;25622:11;25619:1;25615:19;25610:2;25606;25602:11;25598:37;25595:63;;;25638:18;;:::i;:::-;25703:1;25699:2;25694:11;25691:1;25687:19;25682:2;25678;25674:11;25670:37;25667:63;;;25710:18;;:::i;:::-;-1:-1:-1;;;25750:9:357;;;;;25110:655;-1:-1:-1;;;25110:655:357:o;25770:367::-;25809:3;25844:1;25841;25837:9;25953:1;25885:66;25881:74;25878:1;25874:82;25869:2;25862:10;25858:99;25855:125;;;25960:18;;:::i;:::-;26079:1;26011:66;26007:74;26004:1;26000:82;25996:2;25992:91;25989:117;;;26086:18;;:::i;:::-;-1:-1:-1;;26122:9:357;;25770:367::o;26142:228::-;26182:7;26308:1;26240:66;26236:74;26233:1;26230:81;26225:1;26218:9;26211:17;26207:105;26204:131;;;26315:18;;:::i;:::-;-1:-1:-1;26355:9:357;;26142:228::o;26375:120::-;26415:1;26441;26431:35;;26446:18;;:::i;:::-;-1:-1:-1;26480:9:357;;26375:120::o;26500:656::-;26787:6;26776:9;26769:25;26750:4;26813:42;26903:2;26895:6;26891:15;26886:2;26875:9;26871:18;26864:43;26955:2;26947:6;26943:15;26938:2;26927:9;26923:18;26916:43;;26995:6;26990:2;26979:9;26975:18;26968:34;27039:6;27033:3;27022:9;27018:19;27011:35;27083:3;27077;27066:9;27062:19;27055:32;27104:46;27145:3;27134:9;27130:19;27122:6;27104:46;:::i;:::-;27096:54;26500:656;-1:-1:-1;;;;;;;;26500:656:357:o;27969:160::-;28046:13;;28099:4;28088:16;;28078:27;;28068:55;;28119:1;28116;28109:12;28068:55;27969:160;;;:::o;28134:1203::-;28237:6;28290:3;28278:9;28269:7;28265:23;28261:33;28258:53;;;28307:1;28304;28297:12;28258:53;28340:2;28334:9;28382:3;28374:6;28370:16;28452:6;28440:10;28437:22;28416:18;28404:10;28401:34;28398:62;28395:88;;;28463:18;;:::i;:::-;28499:2;28492:22;28536:16;;28561:53;28536:16;28561:53;:::i;:::-;28623:21;;28677:47;28720:2;28705:18;;28677:47;:::i;:::-;28672:2;28664:6;28660:15;28653:72;28758:47;28801:2;28790:9;28786:18;28758:47;:::i;:::-;28753:2;28745:6;28741:15;28734:72;28851:2;28840:9;28836:18;28830:25;28864:55;28911:7;28864:55;:::i;:::-;28947:2;28935:15;;28928:32;29005:3;28990:19;;28984:26;29019:55;28984:26;29019:55;:::i;:::-;29102:3;29090:16;;29083:33;29161:3;29146:19;;29140:26;29210:34;29197:48;;29185:61;;29175:89;;29260:1;29257;29250:12;29175:89;29292:3;29280:16;;29273:33;29284:6;28134:1203;-1:-1:-1;;;28134:1203:357:o;29342:195::-;29381:3;29412:66;29405:5;29402:77;29399:103;;29482:18;;:::i;:::-;-1:-1:-1;29529:1:357;29518:13;;29342:195::o;30079:184::-;30131:77;30128:1;30121:88;30228:4;30225:1;30218:15;30252:4;30249:1;30242:15;31856:128;31896:3;31927:1;31923:6;31920:1;31917:13;31914:39;;;31933:18;;:::i;:::-;-1:-1:-1;31969:9:357;;31856:128::o;32844:157::-;32874:1;32908:4;32905:1;32901:12;32932:3;32922:37;;32939:18;;:::i;:::-;32991:3;32984:4;32981:1;32977:12;32973:22;32968:27;;;32844:157;;;;:::o;33006:195::-;33044:4;33081;33078:1;33074:12;33113:4;33110:1;33106:12;33138:3;33133;33130:12;33127:38;;;33145:18;;:::i;:::-;33182:13;;;33006:195;-1:-1:-1;;;33006:195:357:o", "linkReferences": {}, "immutableReferences": {"87151": [{"start": 1594, "length": 32}, {"start": 5764, "length": 32}], "87154": [{"start": 1219, "length": 32}, {"start": 6726, "length": 32}]}}, "methodIdentifiers": {"blacklistDisputeGame(address)": "7d6be8dc", "checkWithdrawal(bytes32,address)": "71c1566e", "depositTransaction(address,uint256,uint64,bool,bytes)": "e9e05c42", "disputeGameBlacklist(address)": "45884d32", "disputeGameFactory()": "f2b4e617", "disputeGameFinalityDelaySeconds()": "952b2797", "donateETH()": "8b4c40b0", "finalizeWithdrawalTransaction((uint256,address,address,uint256,uint256,bytes))": "8c3152e9", "finalizeWithdrawalTransactionExternalProof((uint256,address,address,uint256,uint256,bytes),address)": "43ca1c50", "finalizedWithdrawals(bytes32)": "a14238e7", "guardian()": "452a9320", "initialize(address,address,address,uint32)": "8e819e54", "l2Sender()": "9bf62d82", "minimumGasLimit(uint64)": "a35d99df", "numProofSubmitters(bytes32)": "513747ab", "params()": "cff0ab96", "paused()": "5c975abb", "proofMaturityDelaySeconds()": "bf653a5c", "proofSubmitters(bytes32,uint256)": "a3860f48", "proveWithdrawalTransaction((uint256,address,address,uint256,uint256,bytes),uint256,(bytes32,bytes32,bytes32,bytes32),bytes[])": "4870496f", "provenWithdrawals(bytes32,address)": "bb2c727e", "respectedGameType()": "3c9f397c", "respectedGameTypeUpdatedAt()": "4fd0434c", "setRespectedGameType(uint32)": "7fc48504", "superchainConfig()": "35e80ab3", "systemConfig()": "33d7e2bd", "version()": "54fd4d50"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.15+commit.e14f2714\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_proofMaturityDelaySeconds\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_disputeGameFinalityDelaySeconds\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"BadTarget\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"CallPaused\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"GasEstimation\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"LargeCalldata\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OutOfGas\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"SmallGasLimit\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Unauthorized\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint8\",\"name\":\"version\",\"type\":\"uint8\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"version\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"opaqueData\",\"type\":\"bytes\"}],\"name\":\"TransactionDeposited\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"withdrawalHash\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"success\",\"type\":\"bool\"}],\"name\":\"WithdrawalFinalized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"withdrawalHash\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"WithdrawalProven\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"contract IDisputeGame\",\"name\":\"_disputeGame\",\"type\":\"address\"}],\"name\":\"blacklistDisputeGame\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_withdrawalHash\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"_proofSubmitter\",\"type\":\"address\"}],\"name\":\"checkWithdrawal\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_value\",\"type\":\"uint256\"},{\"internalType\":\"uint64\",\"name\":\"_gasLimit\",\"type\":\"uint64\"},{\"internalType\":\"bool\",\"name\":\"_isCreation\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"_data\",\"type\":\"bytes\"}],\"name\":\"depositTransaction\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"contract IDisputeGame\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"disputeGameBlacklist\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"disputeGameFactory\",\"outputs\":[{\"internalType\":\"contract DisputeGameFactory\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"disputeGameFinalityDelaySeconds\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"donateETH\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"gasLimit\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"internalType\":\"struct Types.WithdrawalTransaction\",\"name\":\"_tx\",\"type\":\"tuple\"}],\"name\":\"finalizeWithdrawalTransaction\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"gasLimit\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"internalType\":\"struct Types.WithdrawalTransaction\",\"name\":\"_tx\",\"type\":\"tuple\"},{\"internalType\":\"address\",\"name\":\"_proofSubmitter\",\"type\":\"address\"}],\"name\":\"finalizeWithdrawalTransactionExternalProof\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"finalizedWithdrawals\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"guardian\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"contract DisputeGameFactory\",\"name\":\"_disputeGameFactory\",\"type\":\"address\"},{\"internalType\":\"contract SystemConfig\",\"name\":\"_systemConfig\",\"type\":\"address\"},{\"internalType\":\"contract SuperchainConfig\",\"name\":\"_superchainConfig\",\"type\":\"address\"},{\"internalType\":\"GameType\",\"name\":\"_initialRespectedGameType\",\"type\":\"uint32\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"l2Sender\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint64\",\"name\":\"_byteCount\",\"type\":\"uint64\"}],\"name\":\"minimumGasLimit\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"\",\"type\":\"uint64\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"_withdrawalHash\",\"type\":\"bytes32\"}],\"name\":\"numProofSubmitters\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"params\",\"outputs\":[{\"internalType\":\"uint128\",\"name\":\"prevBaseFee\",\"type\":\"uint128\"},{\"internalType\":\"uint64\",\"name\":\"prevBoughtGas\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"prevBlockNum\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"paused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"proofMaturityDelaySeconds\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"proofSubmitters\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"gasLimit\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"internalType\":\"struct Types.WithdrawalTransaction\",\"name\":\"_tx\",\"type\":\"tuple\"},{\"internalType\":\"uint256\",\"name\":\"_disputeGameIndex\",\"type\":\"uint256\"},{\"components\":[{\"internalType\":\"bytes32\",\"name\":\"version\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"stateRoot\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"messagePasserStorageRoot\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"latestBlockhash\",\"type\":\"bytes32\"}],\"internalType\":\"struct Types.OutputRootProof\",\"name\":\"_outputRootProof\",\"type\":\"tuple\"},{\"internalType\":\"bytes[]\",\"name\":\"_withdrawalProof\",\"type\":\"bytes[]\"}],\"name\":\"proveWithdrawalTransaction\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"provenWithdrawals\",\"outputs\":[{\"internalType\":\"contract IDisputeGame\",\"name\":\"disputeGameProxy\",\"type\":\"address\"},{\"internalType\":\"uint64\",\"name\":\"timestamp\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"respectedGameType\",\"outputs\":[{\"internalType\":\"GameType\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"respectedGameTypeUpdatedAt\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"GameType\",\"name\":\"_gameType\",\"type\":\"uint32\"}],\"name\":\"setRespectedGameType\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"superchainConfig\",\"outputs\":[{\"internalType\":\"contract SuperchainConfig\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"systemConfig\",\"outputs\":[{\"internalType\":\"contract SystemConfig\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"version\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"stateMutability\":\"payable\",\"type\":\"receive\"}],\"devdoc\":{\"custom:proxied\":\"@title OptimismPortal2\",\"events\":{\"TransactionDeposited(address,address,uint256,bytes)\":{\"params\":{\"from\":\"Address that triggered the deposit transaction.\",\"opaqueData\":\"ABI encoded deposit data to be parsed off-chain.\",\"to\":\"Address that the deposit transaction is directed to.\",\"version\":\"Version of this deposit transaction event.\"}},\"WithdrawalFinalized(bytes32,bool)\":{\"params\":{\"success\":\"Whether the withdrawal transaction was successful.\",\"withdrawalHash\":\"Hash of the withdrawal transaction.\"}},\"WithdrawalProven(bytes32,address,address)\":{\"params\":{\"from\":\"Address that triggered the withdrawal transaction.\",\"to\":\"Address that the withdrawal transaction is directed to.\",\"withdrawalHash\":\"Hash of the withdrawal transaction.\"}}},\"kind\":\"dev\",\"methods\":{\"blacklistDisputeGame(address)\":{\"params\":{\"_disputeGame\":\"Dispute game to blacklist.\"}},\"checkWithdrawal(bytes32,address)\":{\"params\":{\"_proofSubmitter\":\"The submitter of the proof for the withdrawal hash\",\"_withdrawalHash\":\"Hash of the withdrawal to check.\"}},\"depositTransaction(address,uint256,uint64,bool,bytes)\":{\"params\":{\"_data\":\"Data to trigger the recipient with.\",\"_gasLimit\":\"Amount of L2 gas to purchase by burning gas on L1.\",\"_isCreation\":\"Whether or not the transaction is a contract creation.\",\"_to\":\"Target address on L2.\",\"_value\":\"ETH value to send to the recipient.\"}},\"finalizeWithdrawalTransaction((uint256,address,address,uint256,uint256,bytes))\":{\"params\":{\"_tx\":\"Withdrawal transaction to finalize.\"}},\"finalizeWithdrawalTransactionExternalProof((uint256,address,address,uint256,uint256,bytes),address)\":{\"params\":{\"_proofSubmitter\":\"Address of the proof submitter.\",\"_tx\":\"Withdrawal transaction to finalize.\"}},\"guardian()\":{\"custom:legacy\":\"\",\"returns\":{\"_0\":\"Address of the guardian.\"}},\"initialize(address,address,address,uint32)\":{\"params\":{\"_disputeGameFactory\":\"Contract of the DisputeGameFactory.\",\"_superchainConfig\":\"Contract of the SuperchainConfig.\",\"_systemConfig\":\"Contract of the SystemConfig.\"}},\"minimumGasLimit(uint64)\":{\"params\":{\"_byteCount\":\"Number of bytes in the calldata.\"},\"returns\":{\"_0\":\"The minimum gas limit for a deposit.\"}},\"numProofSubmitters(bytes32)\":{\"params\":{\"_withdrawalHash\":\"Hash of the withdrawal.\"},\"returns\":{\"_0\":\"The number of proof submitters for the withdrawal hash.\"}},\"proveWithdrawalTransaction((uint256,address,address,uint256,uint256,bytes),uint256,(bytes32,bytes32,bytes32,bytes32),bytes[])\":{\"params\":{\"_disputeGameIndex\":\"Index of the dispute game to prove the withdrawal against.\",\"_outputRootProof\":\"Inclusion proof of the L2ToL1MessagePasser contract's storage root.\",\"_tx\":\"Withdrawal transaction to finalize.\",\"_withdrawalProof\":\"Inclusion proof of the withdrawal in L2ToL1MessagePasser contract.\"}},\"setRespectedGameType(uint32)\":{\"params\":{\"_gameType\":\"The game type to consult for output proposals.\"}}},\"stateVariables\":{\"disputeGameFactory\":{\"custom:network-specific\":\"\"},\"spacer_52_0_32\":{\"custom:legacy\":\"@custom:spacer provenWithdrawals\"},\"spacer_53_0_1\":{\"custom:legacy\":\"@custom:spacer paused\"},\"spacer_54_0_20\":{\"custom:legacy\":\"@custom:spacer l2Oracle\"},\"systemConfig\":{\"custom:network-specific\":\"\"},\"version\":{\"custom:semver\":\"3.8.0\"}},\"version\":1},\"userdoc\":{\"errors\":{\"BadTarget()\":[{\"notice\":\"Error for when a deposit or withdrawal is to a bad target.\"}],\"CallPaused()\":[{\"notice\":\"Error for when a method cannot be called when paused. This could be renamed         to `Paused` in the future, but it collides with the `Paused` event.\"}],\"GasEstimation()\":[{\"notice\":\"Error for special gas estimation.\"}],\"LargeCalldata()\":[{\"notice\":\"Error for when a deposit has too much calldata.\"}],\"OutOfGas()\":[{\"notice\":\"Error returned when too much gas resource is consumed.\"}],\"SmallGasLimit()\":[{\"notice\":\"Error for when a deposit has too small of a gas limit.\"}],\"Unauthorized()\":[{\"notice\":\"Error for an unauthorized CALLER.\"}]},\"events\":{\"TransactionDeposited(address,address,uint256,bytes)\":{\"notice\":\"Emitted when a transaction is deposited from L1 to L2.         The parameters of this event are read by the rollup node and used to derive deposit         transactions on L2.\"},\"WithdrawalFinalized(bytes32,bool)\":{\"notice\":\"Emitted when a withdrawal transaction is finalized.\"},\"WithdrawalProven(bytes32,address,address)\":{\"notice\":\"Emitted when a withdrawal transaction is proven.\"}},\"kind\":\"user\",\"methods\":{\"blacklistDisputeGame(address)\":{\"notice\":\"Blacklists a dispute game. Should only be used in the event that a dispute game resolves incorrectly.\"},\"checkWithdrawal(bytes32,address)\":{\"notice\":\"Checks if a withdrawal can be finalized. This function will revert if the withdrawal cannot be         finalized, and otherwise has no side-effects.\"},\"constructor\":{\"notice\":\"Constructs the OptimismPortal contract.\"},\"depositTransaction(address,uint256,uint64,bool,bytes)\":{\"notice\":\"Accepts deposits of ETH and data, and emits a TransactionDeposited event for use in         deriving deposit transactions. Note that if a deposit is made by a contract, its         address will be aliased when retrieved using `tx.origin` or `msg.sender`. Consider         using the CrossDomainMessenger contracts for a simpler developer experience.\"},\"disputeGameBlacklist(address)\":{\"notice\":\"A mapping of dispute game addresses to whether or not they are blacklisted.\"},\"disputeGameFactory()\":{\"notice\":\"Address of the DisputeGameFactory.\"},\"disputeGameFinalityDelaySeconds()\":{\"notice\":\"Getter for the dispute game finality delay.\"},\"donateETH()\":{\"notice\":\"Accepts ETH value without triggering a deposit to L2.         This function mainly exists for the sake of the migration between the legacy         Optimism system and Bedrock.\"},\"finalizeWithdrawalTransaction((uint256,address,address,uint256,uint256,bytes))\":{\"notice\":\"Finalizes a withdrawal transaction.\"},\"finalizeWithdrawalTransactionExternalProof((uint256,address,address,uint256,uint256,bytes),address)\":{\"notice\":\"Finalizes a withdrawal transaction, using an external proof submitter.\"},\"finalizedWithdrawals(bytes32)\":{\"notice\":\"A list of withdrawal hashes which have been successfully finalized.\"},\"guardian()\":{\"notice\":\"Getter function for the address of the guardian.         Public getter is legacy and will be removed in the future. Use `SuperchainConfig.guardian()` instead.\"},\"initialize(address,address,address,uint32)\":{\"notice\":\"Initializer.\"},\"l2Sender()\":{\"notice\":\"Address of the L2 account which initiated a withdrawal in this transaction.         If the of this variable is the default L2 sender address, then we are NOT inside of         a call to finalizeWithdrawalTransaction.\"},\"minimumGasLimit(uint64)\":{\"notice\":\"Computes the minimum gas limit for a deposit.         The minimum gas limit linearly increases based on the size of the calldata.         This is to prevent users from creating L2 resource usage without paying for it.         This function can be used when interacting with the portal to ensure forwards         compatibility.\"},\"numProofSubmitters(bytes32)\":{\"notice\":\"External getter for the number of proof submitters for a withdrawal hash.\"},\"params()\":{\"notice\":\"EIP-1559 style gas parameters.\"},\"paused()\":{\"notice\":\"Getter for the current paused status.\"},\"proofMaturityDelaySeconds()\":{\"notice\":\"Getter for the proof maturity delay.\"},\"proofSubmitters(bytes32,uint256)\":{\"notice\":\"Mapping of withdrawal hashes to addresses that have submitted a proof for the withdrawal.\"},\"proveWithdrawalTransaction((uint256,address,address,uint256,uint256,bytes),uint256,(bytes32,bytes32,bytes32,bytes32),bytes[])\":{\"notice\":\"Proves a withdrawal transaction.\"},\"provenWithdrawals(bytes32,address)\":{\"notice\":\"A mapping of withdrawal hashes to proof submitters to `ProvenWithdrawal` data.\"},\"respectedGameType()\":{\"notice\":\"The game type that the OptimismPortal consults for output proposals.\"},\"respectedGameTypeUpdatedAt()\":{\"notice\":\"The timestamp at which the respected game type was last updated.\"},\"setRespectedGameType(uint32)\":{\"notice\":\"Sets the respected game type. Changing this value can alter the security properties of the system,         depending on the new game's behavior.\"},\"superchainConfig()\":{\"notice\":\"Contract of the Superchain Config.\"},\"systemConfig()\":{\"notice\":\"Contract of the SystemConfig.\"},\"version()\":{\"notice\":\"Semantic version.\"}},\"notice\":\"The OptimismPortal is a low-level contract responsible for passing messages between L1         and L2. Messages sent directly to the OptimismPortal have no form of replayability.         Users are encouraged to use the L1CrossDomainMessenger for a higher-level interface.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/L1/OptimismPortal2.sol\":\"OptimismPortal2\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":999999},\"remappings\":[\":@lib-keccak/=lib/lib-keccak/contracts/lib/\",\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":@rari-capital/solmate/=lib/solmate/\",\":@solady-test/=lib/lib-keccak/lib/solady/test/\",\":@solady/=lib/solady/src/\",\":ds-test/=lib/forge-std/lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":kontrol-cheatcodes/=lib/kontrol-cheatcodes/src/\",\":lib-keccak/=lib/lib-keccak/contracts/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":safe-contracts/=lib/safe-contracts/contracts/\",\":solady/=lib/solady/\",\":solmate/=lib/solmate/src/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol\":{\"keccak256\":\"0x247c62047745915c0af6b955470a72d1696ebad4352d7d3011aef1a2463cd888\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d7fc8396619de513c96b6e00301b88dd790e83542aab918425633a5f7297a15a\",\"dweb:/ipfs/QmXbP4kiZyp7guuS7xe8KaybnwkRPGrBc2Kbi3vhcTfpxb\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0x0203dcadc5737d9ef2c211d6fa15d18ebc3b30dfa51903b64870b01a062b0b4e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6eb2fd1e9894dbe778f4b8131adecebe570689e63cf892f4e21257bfe1252497\",\"dweb:/ipfs/QmXgUGNfZvrn6N2miv3nooSs7Jm34A41qz94fu2GtDFcx8\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol\":{\"keccak256\":\"0x611aa3f23e59cfdd1863c536776407b3e33d695152a266fa7cfb34440a29a8a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9b4b2110b7f2b3eb32951bc08046fa90feccffa594e1176cb91cdfb0e94726b4\",\"dweb:/ipfs/QmSxLwYjicf9zWFuieRc8WQwE4FisA1Um5jp1iSa731TGt\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0x963ea7f0b48b032eef72fe3a7582edf78408d6f834115b9feadd673a4d5bd149\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d6520943ea55fdf5f0bafb39ed909f64de17051bc954ff3e88c9e5621412c79c\",\"dweb:/ipfs/QmWZ4rAKTQbNG2HxGs46AcTXShsVytKeLs7CUCdCSv5N7a\"]},\"lib/openzeppelin-contracts/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0x2a21b14ff90012878752f230d3ffd5c3405e5938d06c97a7d89c0a64561d0d66\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3313a8f9bb1f9476857c9050067b31982bf2140b83d84f3bc0cec1f62bbe947f\",\"dweb:/ipfs/Qma17Pk8NRe7aB4UD3jjVxk7nSFaov3eQyv86hcyqkwJRV\"]},\"lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0xd6153ce99bcdcce22b124f755e72553295be6abcd63804cfdffceb188b8bef10\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://35c47bece3c03caaa07fab37dd2bb3413bfbca20db7bd9895024390e0a469487\",\"dweb:/ipfs/QmPGWT2x3QHcKxqe6gRmAkdakhbaRgx3DLzcakHz5M4eXG\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0xd15c3e400531f00203839159b2b8e7209c5158b35618f570c695b7e47f12e9f0\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b600b852e0597aa69989cc263111f02097e2827edc1bdc70306303e3af5e9929\",\"dweb:/ipfs/QmU4WfM28A1nDqghuuGeFmN3CnVrk6opWtiF65K4vhFPeC\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol\":{\"keccak256\":\"0xb3ebde1c8d27576db912d87c3560dab14adfb9cd001be95890ec4ba035e652e7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a709421c4f5d4677db8216055d2d4dac96a613efdb08178a9f7041f0c5cef689\",\"dweb:/ipfs/QmYs2rStvVLDnSJs8HgaMD1ABwoKKWdiVbQyNfLfFWTjTy\"]},\"lib/solady/src/utils/LibClone.sol\":{\"keccak256\":\"0xfd4b40a4584e736d9d0b045fbc748023804c83819f5e018635a9f447834774a4\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://202fc57397118355d9d573c28d36ff45892e632a12b143f4bc5e7266bfb7737e\",\"dweb:/ipfs/QmZYD6Va3nNUC4B9NHZcyvFmK59i3WnEPPpsi8N355GivN\"]},\"lib/solmate/src/utils/FixedPointMathLib.sol\":{\"keccak256\":\"0x622fcd8a49e132df5ec7651cc6ae3aaf0cf59bdcd67a9a804a1b9e2485113b7d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af77088eb606427d4c55e578984a615779c86bc30646a20f7bb27299ba390f7c\",\"dweb:/ipfs/QmZGQdhdQDtHc7gZXWrKXgA3govc74X8U63BiWhPQK3mK8\"]},\"src/L1/OptimismPortal2.sol\":{\"keccak256\":\"0xcd1bb48f8005d9ed77120615d936441a8fd000b15bec1f32416f819999e4f0ca\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://251a0362b91185a1b53b4053651cc189e1411cdabc4003cbdc7f9efabbd7e22f\",\"dweb:/ipfs/QmfW9o4Pxa2SAbiohXRnqDEbpHWZeqFM4d9QmD3gJjFLQE\"]},\"src/L1/ResourceMetering.sol\":{\"keccak256\":\"0xde3ac62c60f27a3f1ba06eec94f4eda45e7ec5544c6a5d6b79543a7184e44408\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://265a2845c4ff0d9076dd0505755cf2bdf799f4fdc09ef016865a26b51f5c3409\",\"dweb:/ipfs/QmRzSdBD8jmQf3U9u2ATRAzzuyo6c5ugz8VA5ZM4vzoGiM\"]},\"src/L1/SuperchainConfig.sol\":{\"keccak256\":\"0x5fab874f980fe3e52c3398ddd25b655c56af0c98c15588b2ad9ebf30671d859d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4e0aa613d38eceb621f8569fc714f521bc1f2df3d029552186ab3cdf2ee5d53f\",\"dweb:/ipfs/QmZDzFxhTXLW79eohQbr1nghNh3oNC4CUfH7uMX8CsjVAB\"]},\"src/L1/SystemConfig.sol\":{\"keccak256\":\"0xc3d6392cbc44e38ddc93b84b82b08ab8e813df771a48926db3f94edde8f2b64a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d326d644dc59a57a71f4ff6eaa5c6d1464697c7df337b641fe82091b9050e6ce\",\"dweb:/ipfs/Qmd6tNzBmm8V4cpcMNyFWbWnKPNMRoysmmA62rZjGqpg7f\"]},\"src/dispute/DisputeGameFactory.sol\":{\"keccak256\":\"0xc7c6b0c2a051d4a14b3833243fa2e93e5e320bb106ef3979ce77098fb9d6629f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd5cbabb1b0b41f9faf3d2329e519936ea43a1e7da1df6a9be90d2513603b09f\",\"dweb:/ipfs/QmQM5FpgogJQnbmJjdQdoxxMzczx5PBiCNbiRUQiJqHyhM\"]},\"src/dispute/interfaces/IDisputeGame.sol\":{\"keccak256\":\"0xe2611453d5cc05f8aa30dc0e5e15ee5ae29fd3eb55a2c034424250baebf12f9b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://274e00fbcea3b8455bbaa042130bf1f7a5b2b769f28ad57afbf9fabfd74a757a\",\"dweb:/ipfs/QmRKQTfYdMjQYVbuZhdXts1d752eUq8RwrjqqwV5XRYLi6\"]},\"src/dispute/interfaces/IDisputeGameFactory.sol\":{\"keccak256\":\"0x204d89d38d4dc0db40fbf898d95e639ac5608810a5a5506a3d80d71177648bda\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://71e5c0ff04f409f30ca4f8ebfae1592c6ca495e315b059f969d11812e6e84dbd\",\"dweb:/ipfs/QmaNKkhkJv7qHzX6bKB3LjpWBupfMPLhoATUGx1HRTLtXh\"]},\"src/dispute/interfaces/IInitializable.sol\":{\"keccak256\":\"0xbc553af6501a972850a98fc6284943f8e95a5183a7b4f64198c16fca2338c9dc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1f1c422ce4a9e72f0bbdec36434206da4af3a32d38f922acab957942e994ce5\",\"dweb:/ipfs/QmNQGWBceLxx1CKSMLfwTM584q8UCgUpF4rrFe8pdbWYtj\"]},\"src/dispute/lib/LibGameId.sol\":{\"keccak256\":\"0x9a9f30500da6eb7eeaa7193515dc5e45dc479f09ae7d522a07283c0fb5f4bfa6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://be113d8198d5822385de3d6ff3d7b3e8241993484aa95604ffaf38c2d33f40e0\",\"dweb:/ipfs/QmY9mHC52fqc4gAFYCGobNyuP4TqugQgs8o1kTF33t17Hc\"]},\"src/dispute/lib/LibHashing.sol\":{\"keccak256\":\"0x5a072cd028094eee55acb84ed8d08d7422b1fb46658b7e043e916781530a383b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b67e54f1318f1fd67b28b16c6861a56e27217c26a12aaea5c446e2ec53143920\",\"dweb:/ipfs/QmVLSTP3PwXzRkR3A4qV9fjZhca9v8J1EnEYuVGUsSirAq\"]},\"src/dispute/lib/LibPosition.sol\":{\"keccak256\":\"0xf7ceb26f0ac7067ff8a43f263451050eef6fba2029eafb83d3cbe35224d894a6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bb403b0d707a8e2e3780a19185b918bfe907ca2d1b939ea74ae095a5cdf3b48\",\"dweb:/ipfs/QmYFzkmF8TRomp1cBEbTsKxiEnqLnX6SvSh4y3rVa84pBR\"]},\"src/dispute/lib/LibUDT.sol\":{\"keccak256\":\"0x9b61b15f5edfac1e6528aec79c1be6ac712d5f6a62140db87ed749e41a46563f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://24ef4ecee91638e278886888192b7d2b1811ab99f4e90a06817a4b2651720046\",\"dweb:/ipfs/QmdisoBv1mE9jDv6jvpcbvKhdmJZMMjQmATrEYfBQQrXtZ\"]},\"src/libraries/Arithmetic.sol\":{\"keccak256\":\"0x91345e053584f82ad04d682ba821cf3ede808304f5b2a88116a894cf692c21db\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://005e3c42d2edfca0a506cbda94d3b0104eddf20c00bd1bd25272f53f2ef74c72\",\"dweb:/ipfs/QmdaW6Nge6NKoGvFqRpQjBpM2fXpc5y8WpZyBnDnKicdJq\"]},\"src/libraries/Burn.sol\":{\"keccak256\":\"0x90a795bcea3ef06d6d5011256c4bd63d1a4271f519246dbf1ee3e8f1c0e21010\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f60c3aa77cf0c484ddda4754157cff4dc0e2eace4bea67990daff4c0612ab5f\",\"dweb:/ipfs/QmSYGanMFve9uBC17X7hFneSFnwnJxz86Jgh6MX9BRMweb\"]},\"src/libraries/Bytes.sol\":{\"keccak256\":\"0x827f47d123b0fdf3b08816d5b33831811704dbf4e554e53f2269354f6bba8859\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3137ac7204d30a245a8b0d67aa6da5286f1bd8c90379daab561f84963b6db782\",\"dweb:/ipfs/QmWRhisw3axJK833gUScs23ETh2MLFbVzzqzYVMKSDN3S9\"]},\"src/libraries/Constants.sol\":{\"keccak256\":\"0xe0aeec7d6e5d1e44a11405d3b5bfc384ea092c39bea0b763ab937a26fd427132\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://11aa3bff9da26ca2545132ec7994866690446a5321023811c254410d9593bd9b\",\"dweb:/ipfs/QmVxWqadxvdfkqdrhfWisDqeAthibn4HEE1P6o9aFxXLhp\"]},\"src/libraries/DisputeErrors.sol\":{\"keccak256\":\"0x869bec0d79d97f2d0a00b1e70bf1e6955a2be585521e0084602e54455c0a6937\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a235c6349437cd2ade72909287404e2993c1c4bd356707299239c71fa3bf780e\",\"dweb:/ipfs/QmcFSh6PWJ5sNg1CeoRyF9EnV8APWDz1kYP98v6ooGxc71\"]},\"src/libraries/DisputeTypes.sol\":{\"keccak256\":\"0xae3d053cf40b3e47669b89438524fec4eb571a78be296cc7e7ba23025b3bdf0c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4a2b90604718ad29d19a8f21d45a5f8c6188320781fdb7102b3fccadae549961\",\"dweb:/ipfs/QmUBTXgRFG7PvoCBJsXmgi2sZPZFPQQZTptQ91LL7tC2xQ\"]},\"src/libraries/Encoding.sol\":{\"keccak256\":\"0x1dafabcbd4877c7abe9698957b0a44b7e911cb8b11c1437a4ed897135669fa87\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6addfacefa26fdb44f56d73fa0172b97740de75629a962905ec2a20a28d40fff\",\"dweb:/ipfs/QmboHMouqU19Rnbqrfo1gkfnuDBFcPiC9wsKgGtF2W1cNA\"]},\"src/libraries/Hashing.sol\":{\"keccak256\":\"0x89c07a0ca102cbe57b4e082543f2dd6dae0e1fd4a87908a334bd076fc914e7b8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://69c83489c9544ab442dc244c2feb2c6811b726a5eb5a509b97fc5ccb90b98c12\",\"dweb:/ipfs/QmPGGJeLasc1HWHzd6odvWcNvFPQrbYtDubZcv8yp1HLtF\"]},\"src/libraries/PortalErrors.sol\":{\"keccak256\":\"0x57adcaa45a1ce9c5af04d0fe4ecbc86e6ff3f947f7957ab55bdade129adcf558\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3cf485f11085ad6d1ba1386fdb340f65eb1048187692ad3d9eb8816e290140c1\",\"dweb:/ipfs/Qmb423b45TLV8PdhFa8Hs72eom5D2C5q4gVcYGNzDh4EMU\"]},\"src/libraries/SafeCall.sol\":{\"keccak256\":\"0x0636a7abb242bb5d6f5606967c8929e6aa7e63468c1e2ce40ad4780d4c4bf94f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a4daec2ac8f9907bbf84ef0a1c48f03bae8657619bc6f42b3a672f25c516f17a\",\"dweb:/ipfs/Qmf8gfRxBv8gEmCkP8YMPb2GGfj9QUnoNUyKE7UR2SWGnq\"]},\"src/libraries/Storage.sol\":{\"keccak256\":\"0x7ce27a05552aa69afa6b2ab6684dfe99f27366cf8ef2046baeb1fb62fff0022f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a6a24f3ed56681720707a5ab0372fd67fcb1a4f6fb072c7140cda28bdb70f269\",\"dweb:/ipfs/QmW9uTpUULV4xmP7A7MoBDeDhVfQgmJG5qVUFGtXxWpWWK\"]},\"src/libraries/Types.sol\":{\"keccak256\":\"0x75900d651301940d24c00d14f0b3b6cbd6dcf379173ceaa31d9bf5be934a9aa4\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://99c2632c5bf4fa3982391c32110eec9fa07917b483b2442cbaf18bdde5bdb24e\",\"dweb:/ipfs/QmSUs6Amkeootf5gKGbKi4mJpvhN2U8i1ED6ef2dskV5xc\"]},\"src/libraries/rlp/RLPReader.sol\":{\"keccak256\":\"0x99731a39bc10203719d448117b0e6ef47771890440d595d118084d7988d59afb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1dbeb75d0cc8de58350cc15df8867bf97d8492e0617b1c62733ace6155c6915a\",\"dweb:/ipfs/QmNiXzskPE72h93F8EXT8wAXKzEh2EERLbubdVMfwTQbtj\"]},\"src/libraries/rlp/RLPWriter.sol\":{\"keccak256\":\"0x60ac401490f321c9c55e996a2c65151cd5e60de5f8f297e7c94d541c29820bb6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://070f5814db07e4a89173d44a36d90e4261ce530f7336034c01635347f2c2d88b\",\"dweb:/ipfs/QmXqr9yW5Kc8MYgr5wSehU5AiqS9pZ4FKxv7vwiwpZCcyV\"]},\"src/libraries/trie/MerkleTrie.sol\":{\"keccak256\":\"0xf8ba770ee6666e73ae43184c700e9c704b2c4ace71f9e3c2227ddc11a8148b4c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4702ccee1fe44aea3ee01d59e6152eb755da083f786f00947fec4437c064fe74\",\"dweb:/ipfs/QmQjFj5J7hrEM1dxJjFszzW2Cs7g7eMhYNBXonF2DXBstE\"]},\"src/libraries/trie/SecureMerkleTrie.sol\":{\"keccak256\":\"0xeaff8315cfd21197bc6bc859c2decf5d4f4838c9c357c502cdf2b1eac863d288\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://79dcdcaa560aea51d138da4f5dc553a1808b6de090b2dc1629f18375edbff681\",\"dweb:/ipfs/QmbE4pUPhf5fLKW4W6cEjhQs55gEDvHmbmoBqkW1yz3bnw\"]},\"src/universal/ISemver.sol\":{\"keccak256\":\"0xba34562a8026f59886d2e07d1d58d90b9691d00e0788c6263cef6c22740cab44\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0826f998632f83c103c3085bf2e872db79a69022b6d2e0444c83a64ca5283c2a\",\"dweb:/ipfs/QmcJ7PNqkAfKqbjFGRordtAg1v9DvcBSKvdTkVvciLyvQR\"]},\"src/vendor/AddressAliasHelper.sol\":{\"keccak256\":\"0x6ecb83b4ec80fbe49c22f4f95d90482de64660ef5d422a19f4d4b04df31c1237\",\"license\":\"Apache-2.0\",\"urls\":[\"bzz-raw://1d0885be6e473962f9a0622176a22300165ac0cc1a1d7f2e22b11c3d656ace88\",\"dweb:/ipfs/QmPRa3KmRpXW5P9ykveKRDgYN5zYo4cYLAYSnoqHX3KnXR\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.15+commit.e14f2714"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint256", "name": "_proofMaturityDelaySeconds", "type": "uint256"}, {"internalType": "uint256", "name": "_disputeGameFinalityDelaySeconds", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"inputs": [], "type": "error", "name": "CallPaused"}, {"inputs": [], "type": "error", "name": "GasEstimation"}, {"inputs": [], "type": "error", "name": "LargeCalldata"}, {"inputs": [], "type": "error", "name": "OutOfGas"}, {"inputs": [], "type": "error", "name": "SmallGasLimit"}, {"inputs": [], "type": "error", "name": "Unauthorized"}, {"inputs": [{"internalType": "uint8", "name": "version", "type": "uint8", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "version", "type": "uint256", "indexed": true}, {"internalType": "bytes", "name": "opaqueData", "type": "bytes", "indexed": false}], "type": "event", "name": "TransactionDeposited", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "withdrawalHash", "type": "bytes32", "indexed": true}, {"internalType": "bool", "name": "success", "type": "bool", "indexed": false}], "type": "event", "name": "WithdrawalFinalized", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "withdrawalHash", "type": "bytes32", "indexed": true}, {"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}], "type": "event", "name": "WithdrawalProven", "anonymous": false}, {"inputs": [{"internalType": "contract IDisputeGame", "name": "_disputeGame", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "blacklistDisputeGame"}, {"inputs": [{"internalType": "bytes32", "name": "_withdrawalHash", "type": "bytes32"}, {"internalType": "address", "name": "_proofSubmitter", "type": "address"}], "stateMutability": "view", "type": "function", "name": "checkWithdrawal"}, {"inputs": [{"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "uint256", "name": "_value", "type": "uint256"}, {"internalType": "uint64", "name": "_gasLimit", "type": "uint64"}, {"internalType": "bool", "name": "_isCreation", "type": "bool"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "depositTransaction"}, {"inputs": [{"internalType": "contract IDisputeGame", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "disputeGameBlacklist", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "disputeGameFactory", "outputs": [{"internalType": "contract DisputeGameFactory", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "disputeGameFinalityDelaySeconds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "payable", "type": "function", "name": "donateETH"}, {"inputs": [{"internalType": "struct Types.WithdrawalTransaction", "name": "_tx", "type": "tuple", "components": [{"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "target", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "gasLimit", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}]}], "stateMutability": "nonpayable", "type": "function", "name": "finalizeWithdrawalTransaction"}, {"inputs": [{"internalType": "struct Types.WithdrawalTransaction", "name": "_tx", "type": "tuple", "components": [{"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "target", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "gasLimit", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}]}, {"internalType": "address", "name": "_proofSubmitter", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "finalizeWithdrawalTransactionExternalProof"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "guardian", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "contract DisputeGameFactory", "name": "_disputeGameFactory", "type": "address"}, {"internalType": "contract SystemConfig", "name": "_systemConfig", "type": "address"}, {"internalType": "contract SuperchainConfig", "name": "_superchainConfig", "type": "address"}, {"internalType": "GameType", "name": "_initialRespectedGameType", "type": "uint32"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "l2Sender", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint64", "name": "_byteCount", "type": "uint64"}], "stateMutability": "pure", "type": "function", "name": "minimumGasLimit", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}]}, {"inputs": [{"internalType": "bytes32", "name": "_withdrawalHash", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "numProofSubmitters", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "params", "outputs": [{"internalType": "uint128", "name": "prevBaseFee", "type": "uint128"}, {"internalType": "uint64", "name": "prevBoughtGas", "type": "uint64"}, {"internalType": "uint64", "name": "prevBlockNum", "type": "uint64"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "proofMaturityDelaySeconds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "proofSubmitters", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "struct Types.WithdrawalTransaction", "name": "_tx", "type": "tuple", "components": [{"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "target", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "gasLimit", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}]}, {"internalType": "uint256", "name": "_disputeGameIndex", "type": "uint256"}, {"internalType": "struct Types.OutputRootProof", "name": "_outputRootProof", "type": "tuple", "components": [{"internalType": "bytes32", "name": "version", "type": "bytes32"}, {"internalType": "bytes32", "name": "stateRoot", "type": "bytes32"}, {"internalType": "bytes32", "name": "messagePasserStorageRoot", "type": "bytes32"}, {"internalType": "bytes32", "name": "latestBlockhash", "type": "bytes32"}]}, {"internalType": "bytes[]", "name": "_withdrawalProof", "type": "bytes[]"}], "stateMutability": "nonpayable", "type": "function", "name": "proveWithdrawalTransaction"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}, {"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "provenWithdra<PERSON>s", "outputs": [{"internalType": "contract IDisputeGame", "name": "disputeGameProxy", "type": "address"}, {"internalType": "uint64", "name": "timestamp", "type": "uint64"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "respectedGameType", "outputs": [{"internalType": "GameType", "name": "", "type": "uint32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "respectedGameTypeUpdatedAt", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}]}, {"inputs": [{"internalType": "GameType", "name": "_gameType", "type": "uint32"}], "stateMutability": "nonpayable", "type": "function", "name": "setRespectedGameType"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "superchainConfig", "outputs": [{"internalType": "contract SuperchainConfig", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "systemConfig", "outputs": [{"internalType": "contract SystemConfig", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "version", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "payable", "type": "receive"}], "devdoc": {"kind": "dev", "methods": {"blacklistDisputeGame(address)": {"params": {"_disputeGame": "Dispute game to blacklist."}}, "checkWithdrawal(bytes32,address)": {"params": {"_proofSubmitter": "The submitter of the proof for the withdrawal hash", "_withdrawalHash": "Hash of the withdrawal to check."}}, "depositTransaction(address,uint256,uint64,bool,bytes)": {"params": {"_data": "Data to trigger the recipient with.", "_gasLimit": "Amount of L2 gas to purchase by burning gas on L1.", "_isCreation": "Whether or not the transaction is a contract creation.", "_to": "Target address on L2.", "_value": "ETH value to send to the recipient."}}, "finalizeWithdrawalTransaction((uint256,address,address,uint256,uint256,bytes))": {"params": {"_tx": "Withdrawal transaction to finalize."}}, "finalizeWithdrawalTransactionExternalProof((uint256,address,address,uint256,uint256,bytes),address)": {"params": {"_proofSubmitter": "Address of the proof submitter.", "_tx": "Withdrawal transaction to finalize."}}, "guardian()": {"custom:legacy": "", "returns": {"_0": "Address of the guardian."}}, "initialize(address,address,address,uint32)": {"params": {"_disputeGameFactory": "Contract of the DisputeGameFactory.", "_superchainConfig": "Contract of the SuperchainConfig.", "_systemConfig": "Contract of the SystemConfig."}}, "minimumGasLimit(uint64)": {"params": {"_byteCount": "Number of bytes in the calldata."}, "returns": {"_0": "The minimum gas limit for a deposit."}}, "numProofSubmitters(bytes32)": {"params": {"_withdrawalHash": "Hash of the withdrawal."}, "returns": {"_0": "The number of proof submitters for the withdrawal hash."}}, "proveWithdrawalTransaction((uint256,address,address,uint256,uint256,bytes),uint256,(bytes32,bytes32,bytes32,bytes32),bytes[])": {"params": {"_disputeGameIndex": "Index of the dispute game to prove the withdrawal against.", "_outputRootProof": "Inclusion proof of the L2ToL1MessagePasser contract's storage root.", "_tx": "Withdrawal transaction to finalize.", "_withdrawalProof": "Inclusion proof of the withdrawal in L2ToL1MessagePasser contract."}}, "setRespectedGameType(uint32)": {"params": {"_gameType": "The game type to consult for output proposals."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"blacklistDisputeGame(address)": {"notice": "Blacklists a dispute game. Should only be used in the event that a dispute game resolves incorrectly."}, "checkWithdrawal(bytes32,address)": {"notice": "Checks if a withdrawal can be finalized. This function will revert if the withdrawal cannot be         finalized, and otherwise has no side-effects."}, "constructor": {"notice": "Constructs the OptimismPortal contract."}, "depositTransaction(address,uint256,uint64,bool,bytes)": {"notice": "Accepts deposits of ETH and data, and emits a TransactionDeposited event for use in         deriving deposit transactions. Note that if a deposit is made by a contract, its         address will be aliased when retrieved using `tx.origin` or `msg.sender`. Consider         using the CrossDomainMessenger contracts for a simpler developer experience."}, "disputeGameBlacklist(address)": {"notice": "A mapping of dispute game addresses to whether or not they are blacklisted."}, "disputeGameFactory()": {"notice": "Address of the DisputeGameFactory."}, "disputeGameFinalityDelaySeconds()": {"notice": "Getter for the dispute game finality delay."}, "donateETH()": {"notice": "Accepts ETH value without triggering a deposit to L2.         This function mainly exists for the sake of the migration between the legacy         Optimism system and Bedrock."}, "finalizeWithdrawalTransaction((uint256,address,address,uint256,uint256,bytes))": {"notice": "Finalizes a withdrawal transaction."}, "finalizeWithdrawalTransactionExternalProof((uint256,address,address,uint256,uint256,bytes),address)": {"notice": "Finalizes a withdrawal transaction, using an external proof submitter."}, "finalizedWithdrawals(bytes32)": {"notice": "A list of withdrawal hashes which have been successfully finalized."}, "guardian()": {"notice": "Getter function for the address of the guardian.         Public getter is legacy and will be removed in the future. Use `SuperchainConfig.guardian()` instead."}, "initialize(address,address,address,uint32)": {"notice": "Initializer."}, "l2Sender()": {"notice": "Address of the L2 account which initiated a withdrawal in this transaction.         If the of this variable is the default L2 sender address, then we are NOT inside of         a call to finalizeWithdrawalTransaction."}, "minimumGasLimit(uint64)": {"notice": "Computes the minimum gas limit for a deposit.         The minimum gas limit linearly increases based on the size of the calldata.         This is to prevent users from creating L2 resource usage without paying for it.         This function can be used when interacting with the portal to ensure forwards         compatibility."}, "numProofSubmitters(bytes32)": {"notice": "External getter for the number of proof submitters for a withdrawal hash."}, "params()": {"notice": "EIP-1559 style gas parameters."}, "paused()": {"notice": "Getter for the current paused status."}, "proofMaturityDelaySeconds()": {"notice": "Getter for the proof maturity delay."}, "proofSubmitters(bytes32,uint256)": {"notice": "Mapping of withdrawal hashes to addresses that have submitted a proof for the withdrawal."}, "proveWithdrawalTransaction((uint256,address,address,uint256,uint256,bytes),uint256,(bytes32,bytes32,bytes32,bytes32),bytes[])": {"notice": "Proves a withdrawal transaction."}, "provenWithdrawals(bytes32,address)": {"notice": "A mapping of withdrawal hashes to proof submitters to `ProvenWithdrawal` data."}, "respectedGameType()": {"notice": "The game type that the OptimismPortal consults for output proposals."}, "respectedGameTypeUpdatedAt()": {"notice": "The timestamp at which the respected game type was last updated."}, "setRespectedGameType(uint32)": {"notice": "Sets the respected game type. Changing this value can alter the security properties of the system,         depending on the new game's behavior."}, "superchainConfig()": {"notice": "Contract of the Superchain Config."}, "systemConfig()": {"notice": "Contract of the SystemConfig."}, "version()": {"notice": "Semantic version."}}, "version": 1}}, "settings": {"remappings": ["@lib-keccak/=lib/lib-keccak/contracts/lib/", "@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "@rari-capital/solmate/=lib/solmate/", "@solady-test/=lib/lib-keccak/lib/solady/test/", "@solady/=lib/solady/src/", "ds-test/=lib/forge-std/lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "kontrol-cheatcodes/=lib/kontrol-cheatcodes/src/", "lib-keccak/=lib/lib-keccak/contracts/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "safe-contracts/=lib/safe-contracts/contracts/", "solady/=lib/solady/", "solmate/=lib/solmate/src/"], "optimizer": {"enabled": true, "runs": 999999}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/L1/OptimismPortal2.sol": "OptimismPortal2"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol": {"keccak256": "0x247c62047745915c0af6b955470a72d1696ebad4352d7d3011aef1a2463cd888", "urls": ["bzz-raw://d7fc8396619de513c96b6e00301b88dd790e83542aab918425633a5f7297a15a", "dweb:/ipfs/QmXbP4kiZyp7guuS7xe8KaybnwkRPGrBc2Kbi3vhcTfpxb"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0x0203dcadc5737d9ef2c211d6fa15d18ebc3b30dfa51903b64870b01a062b0b4e", "urls": ["bzz-raw://6eb2fd1e9894dbe778f4b8131adecebe570689e63cf892f4e21257bfe1252497", "dweb:/ipfs/QmXgUGNfZvrn6N2miv3nooSs7Jm34A41qz94fu2GtDFcx8"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/AddressUpgradeable.sol": {"keccak256": "0x611aa3f23e59cfdd1863c536776407b3e33d695152a266fa7cfb34440a29a8a3", "urls": ["bzz-raw://9b4b2110b7f2b3eb32951bc08046fa90feccffa594e1176cb91cdfb0e94726b4", "dweb:/ipfs/QmSxLwYjicf9zWFuieRc8WQwE4FisA1Um5jp1iSa731TGt"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0x963ea7f0b48b032eef72fe3a7582edf78408d6f834115b9feadd673a4d5bd149", "urls": ["bzz-raw://d6520943ea55fdf5f0bafb39ed909f64de17051bc954ff3e88c9e5621412c79c", "dweb:/ipfs/QmWZ4rAKTQbNG2HxGs46AcTXShsVytKeLs7CUCdCSv5N7a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/utils/Initializable.sol": {"keccak256": "0x2a21b14ff90012878752f230d3ffd5c3405e5938d06c97a7d89c0a64561d0d66", "urls": ["bzz-raw://3313a8f9bb1f9476857c9050067b31982bf2140b83d84f3bc0cec1f62bbe947f", "dweb:/ipfs/Qma17Pk8NRe7aB4UD3jjVxk7nSFaov3eQyv86hcyqkwJRV"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0xd6153ce99bcdcce22b124f755e72553295be6abcd63804cfdffceb188b8bef10", "urls": ["bzz-raw://35c47bece3c03caaa07fab37dd2bb3413bfbca20db7bd9895024390e0a469487", "dweb:/ipfs/QmPGWT2x3QHcKxqe6gRmAkdakhbaRgx3DLzcakHz5M4eXG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0xd15c3e400531f00203839159b2b8e7209c5158b35618f570c695b7e47f12e9f0", "urls": ["bzz-raw://b600b852e0597aa69989cc263111f02097e2827edc1bdc70306303e3af5e9929", "dweb:/ipfs/QmU4WfM28A1nDqghuuGeFmN3CnVrk6opWtiF65K4vhFPeC"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol": {"keccak256": "0xb3ebde1c8d27576db912d87c3560dab14adfb9cd001be95890ec4ba035e652e7", "urls": ["bzz-raw://a709421c4f5d4677db8216055d2d4dac96a613efdb08178a9f7041f0c5cef689", "dweb:/ipfs/QmYs2rStvVLDnSJs8HgaMD1ABwoKKWdiVbQyNfLfFWTjTy"], "license": "MIT"}, "lib/solady/src/utils/LibClone.sol": {"keccak256": "0xfd4b40a4584e736d9d0b045fbc748023804c83819f5e018635a9f447834774a4", "urls": ["bzz-raw://202fc57397118355d9d573c28d36ff45892e632a12b143f4bc5e7266bfb7737e", "dweb:/ipfs/QmZYD6Va3nNUC4B9NHZcyvFmK59i3WnEPPpsi8N355GivN"], "license": "MIT"}, "lib/solmate/src/utils/FixedPointMathLib.sol": {"keccak256": "0x622fcd8a49e132df5ec7651cc6ae3aaf0cf59bdcd67a9a804a1b9e2485113b7d", "urls": ["bzz-raw://af77088eb606427d4c55e578984a615779c86bc30646a20f7bb27299ba390f7c", "dweb:/ipfs/QmZGQdhdQDtHc7gZXWrKXgA3govc74X8U63BiWhPQK3mK8"], "license": "MIT"}, "src/L1/OptimismPortal2.sol": {"keccak256": "0xcd1bb48f8005d9ed77120615d936441a8fd000b15bec1f32416f819999e4f0ca", "urls": ["bzz-raw://251a0362b91185a1b53b4053651cc189e1411cdabc4003cbdc7f9efabbd7e22f", "dweb:/ipfs/QmfW9o4Pxa2SAbiohXRnqDEbpHWZeqFM4d9QmD3gJjFLQE"], "license": "MIT"}, "src/L1/ResourceMetering.sol": {"keccak256": "0xde3ac62c60f27a3f1ba06eec94f4eda45e7ec5544c6a5d6b79543a7184e44408", "urls": ["bzz-raw://265a2845c4ff0d9076dd0505755cf2bdf799f4fdc09ef016865a26b51f5c3409", "dweb:/ipfs/QmRzSdBD8jmQf3U9u2ATRAzzuyo6c5ugz8VA5ZM4vzoGiM"], "license": "MIT"}, "src/L1/SuperchainConfig.sol": {"keccak256": "0x5fab874f980fe3e52c3398ddd25b655c56af0c98c15588b2ad9ebf30671d859d", "urls": ["bzz-raw://4e0aa613d38eceb621f8569fc714f521bc1f2df3d029552186ab3cdf2ee5d53f", "dweb:/ipfs/QmZDzFxhTXLW79eohQbr1nghNh3oNC4CUfH7uMX8CsjVAB"], "license": "MIT"}, "src/L1/SystemConfig.sol": {"keccak256": "0xc3d6392cbc44e38ddc93b84b82b08ab8e813df771a48926db3f94edde8f2b64a", "urls": ["bzz-raw://d326d644dc59a57a71f4ff6eaa5c6d1464697c7df337b641fe82091b9050e6ce", "dweb:/ipfs/Qmd6tNzBmm8V4cpcMNyFWbWnKPNMRoysmmA62rZjGqpg7f"], "license": "MIT"}, "src/dispute/DisputeGameFactory.sol": {"keccak256": "0xc7c6b0c2a051d4a14b3833243fa2e93e5e320bb106ef3979ce77098fb9d6629f", "urls": ["bzz-raw://cd5cbabb1b0b41f9faf3d2329e519936ea43a1e7da1df6a9be90d2513603b09f", "dweb:/ipfs/QmQM5FpgogJQnbmJjdQdoxxMzczx5PBiCNbiRUQiJqHyhM"], "license": "MIT"}, "src/dispute/interfaces/IDisputeGame.sol": {"keccak256": "0xe2611453d5cc05f8aa30dc0e5e15ee5ae29fd3eb55a2c034424250baebf12f9b", "urls": ["bzz-raw://274e00fbcea3b8455bbaa042130bf1f7a5b2b769f28ad57afbf9fabfd74a757a", "dweb:/ipfs/QmRKQTfYdMjQYVbuZhdXts1d752eUq8RwrjqqwV5XRYLi6"], "license": "MIT"}, "src/dispute/interfaces/IDisputeGameFactory.sol": {"keccak256": "0x204d89d38d4dc0db40fbf898d95e639ac5608810a5a5506a3d80d71177648bda", "urls": ["bzz-raw://71e5c0ff04f409f30ca4f8ebfae1592c6ca495e315b059f969d11812e6e84dbd", "dweb:/ipfs/QmaNKkhkJv7qHzX6bKB3LjpWBupfMPLhoATUGx1HRTLtXh"], "license": "MIT"}, "src/dispute/interfaces/IInitializable.sol": {"keccak256": "0xbc553af6501a972850a98fc6284943f8e95a5183a7b4f64198c16fca2338c9dc", "urls": ["bzz-raw://b1f1c422ce4a9e72f0bbdec36434206da4af3a32d38f922acab957942e994ce5", "dweb:/ipfs/QmNQGWBceLxx1CKSMLfwTM584q8UCgUpF4rrFe8pdbWYtj"], "license": "MIT"}, "src/dispute/lib/LibGameId.sol": {"keccak256": "0x9a9f30500da6eb7eeaa7193515dc5e45dc479f09ae7d522a07283c0fb5f4bfa6", "urls": ["bzz-raw://be113d8198d5822385de3d6ff3d7b3e8241993484aa95604ffaf38c2d33f40e0", "dweb:/ipfs/QmY9mHC52fqc4gAFYCGobNyuP4TqugQgs8o1kTF33t17Hc"], "license": "MIT"}, "src/dispute/lib/LibHashing.sol": {"keccak256": "0x5a072cd028094eee55acb84ed8d08d7422b1fb46658b7e043e916781530a383b", "urls": ["bzz-raw://b67e54f1318f1fd67b28b16c6861a56e27217c26a12aaea5c446e2ec53143920", "dweb:/ipfs/QmVLSTP3PwXzRkR3A4qV9fjZhca9v8J1EnEYuVGUsSirAq"], "license": "MIT"}, "src/dispute/lib/LibPosition.sol": {"keccak256": "0xf7ceb26f0ac7067ff8a43f263451050eef6fba2029eafb83d3cbe35224d894a6", "urls": ["bzz-raw://3bb403b0d707a8e2e3780a19185b918bfe907ca2d1b939ea74ae095a5cdf3b48", "dweb:/ipfs/QmYFzkmF8TRomp1cBEbTsKxiEnqLnX6SvSh4y3rVa84pBR"], "license": "MIT"}, "src/dispute/lib/LibUDT.sol": {"keccak256": "0x9b61b15f5edfac1e6528aec79c1be6ac712d5f6a62140db87ed749e41a46563f", "urls": ["bzz-raw://24ef4ecee91638e278886888192b7d2b1811ab99f4e90a06817a4b2651720046", "dweb:/ipfs/QmdisoBv1mE9jDv6jvpcbvKhdmJZMMjQmATrEYfBQQrXtZ"], "license": "MIT"}, "src/libraries/Arithmetic.sol": {"keccak256": "0x91345e053584f82ad04d682ba821cf3ede808304f5b2a88116a894cf692c21db", "urls": ["bzz-raw://005e3c42d2edfca0a506cbda94d3b0104eddf20c00bd1bd25272f53f2ef74c72", "dweb:/ipfs/QmdaW6Nge6NKoGvFqRpQjBpM2fXpc5y8WpZyBnDnKicdJq"], "license": "MIT"}, "src/libraries/Burn.sol": {"keccak256": "0x90a795bcea3ef06d6d5011256c4bd63d1a4271f519246dbf1ee3e8f1c0e21010", "urls": ["bzz-raw://9f60c3aa77cf0c484ddda4754157cff4dc0e2eace4bea67990daff4c0612ab5f", "dweb:/ipfs/QmSYGanMFve9uBC17X7hFneSFnwnJxz86Jgh6MX9BRMweb"], "license": "MIT"}, "src/libraries/Bytes.sol": {"keccak256": "0x827f47d123b0fdf3b08816d5b33831811704dbf4e554e53f2269354f6bba8859", "urls": ["bzz-raw://3137ac7204d30a245a8b0d67aa6da5286f1bd8c90379daab561f84963b6db782", "dweb:/ipfs/QmWRhisw3axJK833gUScs23ETh2MLFbVzzqzYVMKSDN3S9"], "license": "MIT"}, "src/libraries/Constants.sol": {"keccak256": "0xe0aeec7d6e5d1e44a11405d3b5bfc384ea092c39bea0b763ab937a26fd427132", "urls": ["bzz-raw://11aa3bff9da26ca2545132ec7994866690446a5321023811c254410d9593bd9b", "dweb:/ipfs/QmVxWqadxvdfkqdrhfWisDqeAthibn4HEE1P6o9aFxXLhp"], "license": "MIT"}, "src/libraries/DisputeErrors.sol": {"keccak256": "0x869bec0d79d97f2d0a00b1e70bf1e6955a2be585521e0084602e54455c0a6937", "urls": ["bzz-raw://a235c6349437cd2ade72909287404e2993c1c4bd356707299239c71fa3bf780e", "dweb:/ipfs/QmcFSh6PWJ5sNg1CeoRyF9EnV8APWDz1kYP98v6ooGxc71"], "license": "MIT"}, "src/libraries/DisputeTypes.sol": {"keccak256": "0xae3d053cf40b3e47669b89438524fec4eb571a78be296cc7e7ba23025b3bdf0c", "urls": ["bzz-raw://4a2b90604718ad29d19a8f21d45a5f8c6188320781fdb7102b3fccadae549961", "dweb:/ipfs/QmUBTXgRFG7PvoCBJsXmgi2sZPZFPQQZTptQ91LL7tC2xQ"], "license": "MIT"}, "src/libraries/Encoding.sol": {"keccak256": "0x1dafabcbd4877c7abe9698957b0a44b7e911cb8b11c1437a4ed897135669fa87", "urls": ["bzz-raw://6addfacefa26fdb44f56d73fa0172b97740de75629a962905ec2a20a28d40fff", "dweb:/ipfs/QmboHMouqU19Rnbqrfo1gkfnuDBFcPiC9wsKgGtF2W1cNA"], "license": "MIT"}, "src/libraries/Hashing.sol": {"keccak256": "0x89c07a0ca102cbe57b4e082543f2dd6dae0e1fd4a87908a334bd076fc914e7b8", "urls": ["bzz-raw://69c83489c9544ab442dc244c2feb2c6811b726a5eb5a509b97fc5ccb90b98c12", "dweb:/ipfs/QmPGGJeLasc1HWHzd6odvWcNvFPQrbYtDubZcv8yp1HLtF"], "license": "MIT"}, "src/libraries/PortalErrors.sol": {"keccak256": "0x57adcaa45a1ce9c5af04d0fe4ecbc86e6ff3f947f7957ab55bdade129adcf558", "urls": ["bzz-raw://3cf485f11085ad6d1ba1386fdb340f65eb1048187692ad3d9eb8816e290140c1", "dweb:/ipfs/Qmb423b45TLV8PdhFa8Hs72eom5D2C5q4gVcYGNzDh4EMU"], "license": "MIT"}, "src/libraries/SafeCall.sol": {"keccak256": "0x0636a7abb242bb5d6f5606967c8929e6aa7e63468c1e2ce40ad4780d4c4bf94f", "urls": ["bzz-raw://a4daec2ac8f9907bbf84ef0a1c48f03bae8657619bc6f42b3a672f25c516f17a", "dweb:/ipfs/Qmf8gfRxBv8gEmCkP8YMPb2GGfj9QUnoNUyKE7UR2SWGnq"], "license": "MIT"}, "src/libraries/Storage.sol": {"keccak256": "0x7ce27a05552aa69afa6b2ab6684dfe99f27366cf8ef2046baeb1fb62fff0022f", "urls": ["bzz-raw://a6a24f3ed56681720707a5ab0372fd67fcb1a4f6fb072c7140cda28bdb70f269", "dweb:/ipfs/QmW9uTpUULV4xmP7A7MoBDeDhVfQgmJG5qVUFGtXxWpWWK"], "license": "MIT"}, "src/libraries/Types.sol": {"keccak256": "0x75900d651301940d24c00d14f0b3b6cbd6dcf379173ceaa31d9bf5be934a9aa4", "urls": ["bzz-raw://99c2632c5bf4fa3982391c32110eec9fa07917b483b2442cbaf18bdde5bdb24e", "dweb:/ipfs/QmSUs6Amkeootf5gKGbKi4mJpvhN2U8i1ED6ef2dskV5xc"], "license": "MIT"}, "src/libraries/rlp/RLPReader.sol": {"keccak256": "0x99731a39bc10203719d448117b0e6ef47771890440d595d118084d7988d59afb", "urls": ["bzz-raw://1dbeb75d0cc8de58350cc15df8867bf97d8492e0617b1c62733ace6155c6915a", "dweb:/ipfs/QmNiXzskPE72h93F8EXT8wAXKzEh2EERLbubdVMfwTQbtj"], "license": "MIT"}, "src/libraries/rlp/RLPWriter.sol": {"keccak256": "0x60ac401490f321c9c55e996a2c65151cd5e60de5f8f297e7c94d541c29820bb6", "urls": ["bzz-raw://070f5814db07e4a89173d44a36d90e4261ce530f7336034c01635347f2c2d88b", "dweb:/ipfs/QmXqr9yW5Kc8MYgr5wSehU5AiqS9pZ4FKxv7vwiwpZCcyV"], "license": "MIT"}, "src/libraries/trie/MerkleTrie.sol": {"keccak256": "0xf8ba770ee6666e73ae43184c700e9c704b2c4ace71f9e3c2227ddc11a8148b4c", "urls": ["bzz-raw://4702ccee1fe44aea3ee01d59e6152eb755da083f786f00947fec4437c064fe74", "dweb:/ipfs/QmQjFj5J7hrEM1dxJjFszzW2Cs7g7eMhYNBXonF2DXBstE"], "license": "MIT"}, "src/libraries/trie/SecureMerkleTrie.sol": {"keccak256": "0xeaff8315cfd21197bc6bc859c2decf5d4f4838c9c357c502cdf2b1eac863d288", "urls": ["bzz-raw://79dcdcaa560aea51d138da4f5dc553a1808b6de090b2dc1629f18375edbff681", "dweb:/ipfs/QmbE4pUPhf5fLKW4W6cEjhQs55gEDvHmbmoBqkW1yz3bnw"], "license": "MIT"}, "src/universal/ISemver.sol": {"keccak256": "0xba34562a8026f59886d2e07d1d58d90b9691d00e0788c6263cef6c22740cab44", "urls": ["bzz-raw://0826f998632f83c103c3085bf2e872db79a69022b6d2e0444c83a64ca5283c2a", "dweb:/ipfs/QmcJ7PNqkAfKqbjFGRordtAg1v9DvcBSKvdTkVvciLyvQR"], "license": "MIT"}, "src/vendor/AddressAliasHelper.sol": {"keccak256": "0x6ecb83b4ec80fbe49c22f4f95d90482de64660ef5d422a19f4d4b04df31c1237", "urls": ["bzz-raw://1d0885be6e473962f9a0622176a22300165ac0cc1a1d7f2e22b11c3d656ace88", "dweb:/ipfs/QmPRa3KmRpXW5P9ykveKRDgYN5zYo4cYLAYSnoqHX3KnXR"], "license": "Apache-2.0"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 49534, "contract": "src/L1/OptimismPortal2.sol:OptimismPortal2", "label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8"}, {"astId": 49537, "contract": "src/L1/OptimismPortal2.sol:OptimismPortal2", "label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool"}, {"astId": 88262, "contract": "src/L1/OptimismPortal2.sol:OptimismPortal2", "label": "params", "offset": 0, "slot": "1", "type": "t_struct(ResourceParams)88245_storage"}, {"astId": 88267, "contract": "src/L1/OptimismPortal2.sol:OptimismPortal2", "label": "__gap", "offset": 0, "slot": "2", "type": "t_array(t_uint256)48_storage"}, {"astId": 87165, "contract": "src/L1/OptimismPortal2.sol:OptimismPortal2", "label": "l2Sender", "offset": 0, "slot": "50", "type": "t_address"}, {"astId": 87170, "contract": "src/L1/OptimismPortal2.sol:OptimismPortal2", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "51", "type": "t_mapping(t_bytes32,t_bool)"}, {"astId": 87173, "contract": "src/L1/OptimismPortal2.sol:OptimismPortal2", "label": "spacer_52_0_32", "offset": 0, "slot": "52", "type": "t_bytes32"}, {"astId": 87176, "contract": "src/L1/OptimismPortal2.sol:OptimismPortal2", "label": "spacer_53_0_1", "offset": 0, "slot": "53", "type": "t_bool"}, {"astId": 87180, "contract": "src/L1/OptimismPortal2.sol:OptimismPortal2", "label": "superchainConfig", "offset": 1, "slot": "53", "type": "t_contract(SuperchainConfig)88793"}, {"astId": 87183, "contract": "src/L1/OptimismPortal2.sol:OptimismPortal2", "label": "spacer_54_0_20", "offset": 0, "slot": "54", "type": "t_address"}, {"astId": 87187, "contract": "src/L1/OptimismPortal2.sol:OptimismPortal2", "label": "systemConfig", "offset": 0, "slot": "55", "type": "t_contract(SystemConfig)89607"}, {"astId": 87191, "contract": "src/L1/OptimismPortal2.sol:OptimismPortal2", "label": "disputeGameFactory", "offset": 0, "slot": "56", "type": "t_contract(DisputeGameFactory)97682"}, {"astId": 87199, "contract": "src/L1/OptimismPortal2.sol:OptimismPortal2", "label": "provenWithdra<PERSON>s", "offset": 0, "slot": "57", "type": "t_mapping(t_bytes32,t_mapping(t_address,t_struct(ProvenWithdrawal)87148_storage))"}, {"astId": 87205, "contract": "src/L1/OptimismPortal2.sol:OptimismPortal2", "label": "disputeGameBlacklist", "offset": 0, "slot": "58", "type": "t_mapping(t_contract(IDisputeGame)100327,t_bool)"}, {"astId": 87209, "contract": "src/L1/OptimismPortal2.sol:OptimismPortal2", "label": "respectedGameType", "offset": 0, "slot": "59", "type": "t_userDefinedValueType(GameType)103271"}, {"astId": 87212, "contract": "src/L1/OptimismPortal2.sol:OptimismPortal2", "label": "respectedGameTypeUpdatedAt", "offset": 4, "slot": "59", "type": "t_uint64"}, {"astId": 87218, "contract": "src/L1/OptimismPortal2.sol:OptimismPortal2", "label": "proofSubmitters", "offset": 0, "slot": "60", "type": "t_mapping(t_bytes32,t_array(t_address)dyn_storage)"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_address)dyn_storage": {"encoding": "dynamic_array", "label": "address[]", "numberOfBytes": "32", "base": "t_address"}, "t_array(t_uint256)48_storage": {"encoding": "inplace", "label": "uint256[48]", "numberOfBytes": "1536", "base": "t_uint256"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"encoding": "inplace", "label": "bytes32", "numberOfBytes": "32"}, "t_contract(DisputeGameFactory)97682": {"encoding": "inplace", "label": "contract DisputeGameFactory", "numberOfBytes": "20"}, "t_contract(IDisputeGame)100327": {"encoding": "inplace", "label": "contract IDisputeGame", "numberOfBytes": "20"}, "t_contract(SuperchainConfig)88793": {"encoding": "inplace", "label": "contract SuperchainConfig", "numberOfBytes": "20"}, "t_contract(SystemConfig)89607": {"encoding": "inplace", "label": "contract SystemConfig", "numberOfBytes": "20"}, "t_mapping(t_address,t_struct(ProvenWithdrawal)87148_storage)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => struct OptimismPortal2.ProvenWithdrawal)", "numberOfBytes": "32", "value": "t_struct(ProvenWithdrawal)87148_storage"}, "t_mapping(t_bytes32,t_array(t_address)dyn_storage)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => address[])", "numberOfBytes": "32", "value": "t_array(t_address)dyn_storage"}, "t_mapping(t_bytes32,t_bool)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => bool)", "numberOfBytes": "32", "value": "t_bool"}, "t_mapping(t_bytes32,t_mapping(t_address,t_struct(ProvenWithdrawal)87148_storage))": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => mapping(address => struct OptimismPortal2.ProvenWithdrawal))", "numberOfBytes": "32", "value": "t_mapping(t_address,t_struct(ProvenWithdrawal)87148_storage)"}, "t_mapping(t_contract(IDisputeGame)100327,t_bool)": {"encoding": "mapping", "key": "t_contract(IDisputeGame)100327", "label": "mapping(contract IDisputeGame => bool)", "numberOfBytes": "32", "value": "t_bool"}, "t_struct(ProvenWithdrawal)87148_storage": {"encoding": "inplace", "label": "struct OptimismPortal2.<PERSON><PERSON><PERSON><PERSON>dra<PERSON>", "numberOfBytes": "32", "members": [{"astId": 87145, "contract": "src/L1/OptimismPortal2.sol:OptimismPortal2", "label": "disputeGameProxy", "offset": 0, "slot": "0", "type": "t_contract(IDisputeGame)100327"}, {"astId": 87147, "contract": "src/L1/OptimismPortal2.sol:OptimismPortal2", "label": "timestamp", "offset": 20, "slot": "0", "type": "t_uint64"}]}, "t_struct(ResourceParams)88245_storage": {"encoding": "inplace", "label": "struct ResourceMetering.ResourceParams", "numberOfBytes": "32", "members": [{"astId": 88240, "contract": "src/L1/OptimismPortal2.sol:OptimismPortal2", "label": "prevBaseFee", "offset": 0, "slot": "0", "type": "t_uint128"}, {"astId": 88242, "contract": "src/L1/OptimismPortal2.sol:OptimismPortal2", "label": "prevBoughtGas", "offset": 16, "slot": "0", "type": "t_uint64"}, {"astId": 88244, "contract": "src/L1/OptimismPortal2.sol:OptimismPortal2", "label": "prevBlockNum", "offset": 24, "slot": "0", "type": "t_uint64"}]}, "t_uint128": {"encoding": "inplace", "label": "uint128", "numberOfBytes": "16"}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"encoding": "inplace", "label": "uint64", "numberOfBytes": "8"}, "t_uint8": {"encoding": "inplace", "label": "uint8", "numberOfBytes": "1"}, "t_userDefinedValueType(GameType)103271": {"encoding": "inplace", "label": "GameType", "numberOfBytes": "4"}}}, "userdoc": {"version": 1, "kind": "user", "methods": {"blacklistDisputeGame(address)": {"notice": "Blacklists a dispute game. Should only be used in the event that a dispute game resolves incorrectly."}, "checkWithdrawal(bytes32,address)": {"notice": "Checks if a withdrawal can be finalized. This function will revert if the withdrawal cannot be         finalized, and otherwise has no side-effects."}, "constructor": {"notice": "Constructs the OptimismPortal contract."}, "depositTransaction(address,uint256,uint64,bool,bytes)": {"notice": "Accepts deposits of ETH and data, and emits a TransactionDeposited event for use in         deriving deposit transactions. Note that if a deposit is made by a contract, its         address will be aliased when retrieved using `tx.origin` or `msg.sender`. Consider         using the CrossDomainMessenger contracts for a simpler developer experience."}, "disputeGameBlacklist(address)": {"notice": "A mapping of dispute game addresses to whether or not they are blacklisted."}, "disputeGameFactory()": {"notice": "Address of the DisputeGameFactory."}, "disputeGameFinalityDelaySeconds()": {"notice": "Getter for the dispute game finality delay."}, "donateETH()": {"notice": "Accepts ETH value without triggering a deposit to L2.         This function mainly exists for the sake of the migration between the legacy         Optimism system and Bedrock."}, "finalizeWithdrawalTransaction((uint256,address,address,uint256,uint256,bytes))": {"notice": "Finalizes a withdrawal transaction."}, "finalizeWithdrawalTransactionExternalProof((uint256,address,address,uint256,uint256,bytes),address)": {"notice": "Finalizes a withdrawal transaction, using an external proof submitter."}, "finalizedWithdrawals(bytes32)": {"notice": "A list of withdrawal hashes which have been successfully finalized."}, "guardian()": {"notice": "Getter function for the address of the guardian.         Public getter is legacy and will be removed in the future. Use `SuperchainConfig.guardian()` instead."}, "initialize(address,address,address,uint32)": {"notice": "Initializer."}, "l2Sender()": {"notice": "Address of the L2 account which initiated a withdrawal in this transaction.         If the of this variable is the default L2 sender address, then we are NOT inside of         a call to finalizeWithdrawalTransaction."}, "minimumGasLimit(uint64)": {"notice": "Computes the minimum gas limit for a deposit.         The minimum gas limit linearly increases based on the size of the calldata.         This is to prevent users from creating L2 resource usage without paying for it.         This function can be used when interacting with the portal to ensure forwards         compatibility."}, "numProofSubmitters(bytes32)": {"notice": "External getter for the number of proof submitters for a withdrawal hash."}, "params()": {"notice": "EIP-1559 style gas parameters."}, "paused()": {"notice": "Getter for the current paused status."}, "proofMaturityDelaySeconds()": {"notice": "Getter for the proof maturity delay."}, "proofSubmitters(bytes32,uint256)": {"notice": "Mapping of withdrawal hashes to addresses that have submitted a proof for the withdrawal."}, "proveWithdrawalTransaction((uint256,address,address,uint256,uint256,bytes),uint256,(bytes32,bytes32,bytes32,bytes32),bytes[])": {"notice": "Proves a withdrawal transaction."}, "provenWithdrawals(bytes32,address)": {"notice": "A mapping of withdrawal hashes to proof submitters to `ProvenWithdrawal` data."}, "respectedGameType()": {"notice": "The game type that the OptimismPortal consults for output proposals."}, "respectedGameTypeUpdatedAt()": {"notice": "The timestamp at which the respected game type was last updated."}, "setRespectedGameType(uint32)": {"notice": "Sets the respected game type. Changing this value can alter the security properties of the system,         depending on the new game's behavior."}, "superchainConfig()": {"notice": "Contract of the Superchain Config."}, "systemConfig()": {"notice": "Contract of the SystemConfig."}, "version()": {"notice": "Semantic version."}}, "events": {"TransactionDeposited(address,address,uint256,bytes)": {"notice": "Emitted when a transaction is deposited from L1 to L2.         The parameters of this event are read by the rollup node and used to derive deposit         transactions on L2."}, "WithdrawalFinalized(bytes32,bool)": {"notice": "Emitted when a withdrawal transaction is finalized."}, "WithdrawalProven(bytes32,address,address)": {"notice": "Emitted when a withdrawal transaction is proven."}}, "errors": {"BadTarget()": [{"notice": "Error for when a deposit or withdrawal is to a bad target."}], "CallPaused()": [{"notice": "Error for when a method cannot be called when paused. This could be renamed         to `Paused` in the future, but it collides with the `Paused` event."}], "GasEstimation()": [{"notice": "Error for special gas estimation."}], "LargeCalldata()": [{"notice": "Error for when a deposit has too much calldata."}], "OutOfGas()": [{"notice": "Error returned when too much gas resource is consumed."}], "SmallGasLimit()": [{"notice": "Error for when a deposit has too small of a gas limit."}], "Unauthorized()": [{"notice": "Error for an unauthorized CALLER."}]}, "notice": "The OptimismPortal is a low-level contract responsible for passing messages between L1         and L2. Messages sent directly to the OptimismPortal have no form of replayability.         Users are encouraged to use the L1CrossDomainMessenger for a higher-level interface."}, "devdoc": {"version": 1, "kind": "dev", "methods": {"blacklistDisputeGame(address)": {"params": {"_disputeGame": "Dispute game to blacklist."}}, "checkWithdrawal(bytes32,address)": {"params": {"_proofSubmitter": "The submitter of the proof for the withdrawal hash", "_withdrawalHash": "Hash of the withdrawal to check."}}, "depositTransaction(address,uint256,uint64,bool,bytes)": {"params": {"_data": "Data to trigger the recipient with.", "_gasLimit": "Amount of L2 gas to purchase by burning gas on L1.", "_isCreation": "Whether or not the transaction is a contract creation.", "_to": "Target address on L2.", "_value": "ETH value to send to the recipient."}}, "finalizeWithdrawalTransaction((uint256,address,address,uint256,uint256,bytes))": {"params": {"_tx": "Withdrawal transaction to finalize."}}, "finalizeWithdrawalTransactionExternalProof((uint256,address,address,uint256,uint256,bytes),address)": {"params": {"_proofSubmitter": "Address of the proof submitter.", "_tx": "Withdrawal transaction to finalize."}}, "guardian()": {"returns": {"_0": "Address of the guardian."}}, "initialize(address,address,address,uint32)": {"params": {"_disputeGameFactory": "Contract of the DisputeGameFactory.", "_superchainConfig": "Contract of the SuperchainConfig.", "_systemConfig": "Contract of the SystemConfig."}}, "minimumGasLimit(uint64)": {"params": {"_byteCount": "Number of bytes in the calldata."}, "returns": {"_0": "The minimum gas limit for a deposit."}}, "numProofSubmitters(bytes32)": {"params": {"_withdrawalHash": "Hash of the withdrawal."}, "returns": {"_0": "The number of proof submitters for the withdrawal hash."}}, "proveWithdrawalTransaction((uint256,address,address,uint256,uint256,bytes),uint256,(bytes32,bytes32,bytes32,bytes32),bytes[])": {"params": {"_disputeGameIndex": "Index of the dispute game to prove the withdrawal against.", "_outputRootProof": "Inclusion proof of the L2ToL1MessagePasser contract's storage root.", "_tx": "Withdrawal transaction to finalize.", "_withdrawalProof": "Inclusion proof of the withdrawal in L2ToL1MessagePasser contract."}}, "setRespectedGameType(uint32)": {"params": {"_gameType": "The game type to consult for output proposals."}}}, "events": {"TransactionDeposited(address,address,uint256,bytes)": {"params": {"from": "Address that triggered the deposit transaction.", "opaqueData": "ABI encoded deposit data to be parsed off-chain.", "to": "Address that the deposit transaction is directed to.", "version": "Version of this deposit transaction event."}}, "WithdrawalFinalized(bytes32,bool)": {"params": {"success": "Whether the withdrawal transaction was successful.", "withdrawalHash": "Hash of the withdrawal transaction."}}, "WithdrawalProven(bytes32,address,address)": {"params": {"from": "Address that triggered the withdrawal transaction.", "to": "Address that the withdrawal transaction is directed to.", "withdrawalHash": "Hash of the withdrawal transaction."}}}}, "ast": {"absolutePath": "src/L1/OptimismPortal2.sol", "id": 87972, "exportedSymbols": {"AddressAliasHelper": [111913], "BadTarget": [103969], "BondAmount": [103259], "CallPaused": [103990], "Claim": [103255], "ClaimHash": [103257], "Clock": [103267], "Constants": [103096], "DisputeGameFactory": [97682], "Duration": [103263], "GameId": [103265], "GameStatus": [103277], "GameType": [103271], "GameTypes": [103317], "GasEstimation": [103993], "Hash": [103253], "Hashing": [103936], "IDisputeGame": [100327], "ISemver": [109417], "Initializable": [49678], "LargeCalldata": [103972], "LibClaim": [101086], "LibClock": [101073], "LibDuration": [101099], "LibGameId": [100778], "LibGameType": [101151], "LibHash": [101112], "LibHashing": [100800], "LibPosition": [101018], "LibTimestamp": [101125], "LibVMStatus": [101138], "LocalPreimageKey": [103373], "NoValue": [103984], "OnlyCustomGasToken": [103981], "OptimismPortal2": [87971], "OutputRoot": [103283], "Position": [103269], "ResourceMetering": [88581], "SafeCall": [104213], "SecureMerkleTrie": [106033], "SmallGasLimit": [103975], "SuperchainConfig": [88793], "SystemConfig": [89607], "Timestamp": [103261], "TransferFailed": [103978], "Types": [104349], "Unauthorized": [103987], "VMStatus": [103273], "VMStatuses": [103351]}, "nodeType": "SourceUnit", "src": "32:24886:135", "nodes": [{"id": 87106, "nodeType": "PragmaDirective", "src": "32:23:135", "nodes": [], "literals": ["solidity", "0.8", ".15"]}, {"id": 87108, "nodeType": "ImportDirective", "src": "57:86:135", "nodes": [], "absolutePath": "lib/openzeppelin-contracts/contracts/proxy/utils/Initializable.sol", "file": "@openzeppelin/contracts/proxy/utils/Initializable.sol", "nameLocation": "-1:-1:-1", "scope": 87972, "sourceUnit": 49679, "symbolAliases": [{"foreign": {"id": 87107, "name": "Initializable", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 49678, "src": "66:13:135", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 87110, "nodeType": "ImportDirective", "src": "144:54:135", "nodes": [], "absolutePath": "src/libraries/SafeCall.sol", "file": "src/libraries/SafeCall.sol", "nameLocation": "-1:-1:-1", "scope": 87972, "sourceUnit": 104214, "symbolAliases": [{"foreign": {"id": 87109, "name": "SafeCall", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 104213, "src": "153:8:135", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 87113, "nodeType": "ImportDirective", "src": "199:86:135", "nodes": [], "absolutePath": "src/dispute/DisputeGameFactory.sol", "file": "src/dispute/DisputeGameFactory.sol", "nameLocation": "-1:-1:-1", "scope": 87972, "sourceUnit": 97683, "symbolAliases": [{"foreign": {"id": 87111, "name": "DisputeGameFactory", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 97682, "src": "208:18:135", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}, {"foreign": {"id": 87112, "name": "IDisputeGame", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 100327, "src": "228:12:135", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 87115, "nodeType": "ImportDirective", "src": "286:55:135", "nodes": [], "absolutePath": "src/L1/SystemConfig.sol", "file": "src/L1/SystemConfig.sol", "nameLocation": "-1:-1:-1", "scope": 87972, "sourceUnit": 89608, "symbolAliases": [{"foreign": {"id": 87114, "name": "SystemConfig", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 89607, "src": "295:12:135", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 87117, "nodeType": "ImportDirective", "src": "342:63:135", "nodes": [], "absolutePath": "src/L1/SuperchainConfig.sol", "file": "src/L1/SuperchainConfig.sol", "nameLocation": "-1:-1:-1", "scope": 87972, "sourceUnit": 88794, "symbolAliases": [{"foreign": {"id": 87116, "name": "SuperchainConfig", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 88793, "src": "351:16:135", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 87119, "nodeType": "ImportDirective", "src": "406:56:135", "nodes": [], "absolutePath": "src/libraries/Constants.sol", "file": "src/libraries/Constants.sol", "nameLocation": "-1:-1:-1", "scope": 87972, "sourceUnit": 103097, "symbolAliases": [{"foreign": {"id": 87118, "name": "Constants", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103096, "src": "415:9:135", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 87121, "nodeType": "ImportDirective", "src": "463:48:135", "nodes": [], "absolutePath": "src/libraries/Types.sol", "file": "src/libraries/Types.sol", "nameLocation": "-1:-1:-1", "scope": 87972, "sourceUnit": 104350, "symbolAliases": [{"foreign": {"id": 87120, "name": "Types", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 104349, "src": "472:5:135", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 87123, "nodeType": "ImportDirective", "src": "512:52:135", "nodes": [], "absolutePath": "src/libraries/Hashing.sol", "file": "src/libraries/Hashing.sol", "nameLocation": "-1:-1:-1", "scope": 87972, "sourceUnit": 103937, "symbolAliases": [{"foreign": {"id": 87122, "name": "Hashing", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103936, "src": "521:7:135", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 87125, "nodeType": "ImportDirective", "src": "565:75:135", "nodes": [], "absolutePath": "src/libraries/trie/SecureMerkleTrie.sol", "file": "src/libraries/trie/SecureMerkleTrie.sol", "nameLocation": "-1:-1:-1", "scope": 87972, "sourceUnit": 106034, "symbolAliases": [{"foreign": {"id": 87124, "name": "SecureMerkleTrie", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 106033, "src": "574:16:135", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 87127, "nodeType": "ImportDirective", "src": "641:71:135", "nodes": [], "absolutePath": "src/vendor/AddressAliasHelper.sol", "file": "src/vendor/AddressAliasHelper.sol", "nameLocation": "-1:-1:-1", "scope": 87972, "sourceUnit": 111914, "symbolAliases": [{"foreign": {"id": 87126, "name": "AddressAliasHelper", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 111913, "src": "650:18:135", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 87129, "nodeType": "ImportDirective", "src": "713:63:135", "nodes": [], "absolutePath": "src/L1/ResourceMetering.sol", "file": "src/L1/ResourceMetering.sol", "nameLocation": "-1:-1:-1", "scope": 87972, "sourceUnit": 88582, "symbolAliases": [{"foreign": {"id": 87128, "name": "ResourceMetering", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 88581, "src": "722:16:135", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 87131, "nodeType": "ImportDirective", "src": "777:52:135", "nodes": [], "absolutePath": "src/universal/ISemver.sol", "file": "src/universal/ISemver.sol", "nameLocation": "-1:-1:-1", "scope": 87972, "sourceUnit": 109418, "symbolAliases": [{"foreign": {"id": 87130, "name": "ISemver", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 109417, "src": "786:7:135", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 87133, "nodeType": "ImportDirective", "src": "830:56:135", "nodes": [], "absolutePath": "src/libraries/Constants.sol", "file": "src/libraries/Constants.sol", "nameLocation": "-1:-1:-1", "scope": 87972, "sourceUnit": 103097, "symbolAliases": [{"foreign": {"id": 87132, "name": "Constants", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103096, "src": "839:9:135", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 87134, "nodeType": "ImportDirective", "src": "888:40:135", "nodes": [], "absolutePath": "src/libraries/PortalErrors.sol", "file": "src/libraries/PortalErrors.sol", "nameLocation": "-1:-1:-1", "scope": 87972, "sourceUnit": 103994, "symbolAliases": [], "unitAlias": ""}, {"id": 87135, "nodeType": "ImportDirective", "src": "929:40:135", "nodes": [], "absolutePath": "src/libraries/DisputeTypes.sol", "file": "src/libraries/DisputeTypes.sol", "nameLocation": "-1:-1:-1", "scope": 87972, "sourceUnit": 103374, "symbolAliases": [], "unitAlias": ""}, {"id": 87971, "nodeType": "ContractDefinition", "src": "1310:23607:135", "nodes": [{"id": 87148, "nodeType": "StructDefinition", "src": "1635:96:135", "nodes": [], "canonicalName": "OptimismPortal2.<PERSON><PERSON><PERSON><PERSON>", "members": [{"constant": false, "id": 87145, "mutability": "mutable", "name": "disputeGameProxy", "nameLocation": "1682:16:135", "nodeType": "VariableDeclaration", "scope": 87148, "src": "1669:29:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_IDisputeGame_$100327", "typeString": "contract IDisputeGame"}, "typeName": {"id": 87144, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 87143, "name": "IDisputeGame", "nodeType": "IdentifierPath", "referencedDeclaration": 100327, "src": "1669:12:135"}, "referencedDeclaration": 100327, "src": "1669:12:135", "typeDescriptions": {"typeIdentifier": "t_contract$_IDisputeGame_$100327", "typeString": "contract IDisputeGame"}}, "visibility": "internal"}, {"constant": false, "id": 87147, "mutability": "mutable", "name": "timestamp", "nameLocation": "1715:9:135", "nodeType": "VariableDeclaration", "scope": 87148, "src": "1708:16:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "typeName": {"id": 87146, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "1708:6:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "visibility": "internal"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nameLocation": "1642:16:135", "scope": 87971, "visibility": "public"}, {"id": 87151, "nodeType": "VariableDeclaration", "src": "1841:55:135", "nodes": [], "constant": false, "documentation": {"id": 87149, "nodeType": "StructuredDocumentation", "src": "1737:99:135", "text": "@notice The delay between when a withdrawal transaction is proven and when it may be finalized."}, "mutability": "immutable", "name": "PROOF_MATURITY_DELAY_SECONDS", "nameLocation": "1868:28:135", "scope": 87971, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 87150, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1841:7:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"id": 87154, "nodeType": "VariableDeclaration", "src": "2043:62:135", "nodes": [], "constant": false, "documentation": {"id": 87152, "nodeType": "StructuredDocumentation", "src": "1903:135:135", "text": "@notice The delay between when a dispute game is resolved and when a withdrawal proven against it may be\n         finalized."}, "mutability": "immutable", "name": "DISPUTE_GAME_FINALITY_DELAY_SECONDS", "nameLocation": "2070:35:135", "scope": 87971, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 87153, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2043:7:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"id": 87158, "nodeType": "VariableDeclaration", "src": "2158:45:135", "nodes": [], "constant": true, "documentation": {"id": 87155, "nodeType": "StructuredDocumentation", "src": "2112:41:135", "text": "@notice Version of the deposit event."}, "mutability": "constant", "name": "DEPOSIT_VERSION", "nameLocation": "2184:15:135", "scope": 87971, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 87156, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2158:7:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "30", "id": 87157, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2202:1:135", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "visibility": "internal"}, {"id": 87162, "nodeType": "VariableDeclaration", "src": "2299:60:135", "nodes": [], "constant": true, "documentation": {"id": 87159, "nodeType": "StructuredDocumentation", "src": "2210:84:135", "text": "@notice The L2 gas limit set when eth is deposited using the receive() function."}, "mutability": "constant", "name": "RECEIVE_DEFAULT_GAS_LIMIT", "nameLocation": "2324:25:135", "scope": 87971, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "typeName": {"id": 87160, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "2299:6:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "value": {"hexValue": "3130305f303030", "id": 87161, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2352:7:135", "typeDescriptions": {"typeIdentifier": "t_rational_100000_by_1", "typeString": "int_const 100000"}, "value": "100_000"}, "visibility": "internal"}, {"id": 87165, "nodeType": "VariableDeclaration", "src": "2615:23:135", "nodes": [], "constant": false, "documentation": {"id": 87163, "nodeType": "StructuredDocumentation", "src": "2366:244:135", "text": "@notice Address of the L2 account which initiated a withdrawal in this transaction.\n         If the of this variable is the default L2 sender address, then we are NOT inside of\n         a call to finalizeWithdrawalTransaction."}, "functionSelector": "9bf62d82", "mutability": "mutable", "name": "l2Sender", "nameLocation": "2630:8:135", "scope": 87971, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 87164, "name": "address", "nodeType": "ElementaryTypeName", "src": "2615:7:135", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "public"}, {"id": 87170, "nodeType": "VariableDeclaration", "src": "2729:52:135", "nodes": [], "constant": false, "documentation": {"id": 87166, "nodeType": "StructuredDocumentation", "src": "2645:79:135", "text": "@notice A list of withdrawal hashes which have been successfully finalized."}, "functionSelector": "a14238e7", "mutability": "mutable", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nameLocation": "2761:20:135", "scope": 87971, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_bool_$", "typeString": "mapping(bytes32 => bool)"}, "typeName": {"id": 87169, "keyType": {"id": 87167, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "2737:7:135", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "Mapping", "src": "2729:24:135", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_bool_$", "typeString": "mapping(bytes32 => bool)"}, "valueType": {"id": 87168, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2748:4:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}}, "visibility": "public"}, {"id": 87173, "nodeType": "VariableDeclaration", "src": "2930:30:135", "nodes": [], "constant": false, "documentation": {"id": 87171, "nodeType": "StructuredDocumentation", "src": "2788:137:135", "text": "@custom:legacy\n @custom:spacer provenWithdrawals\n @notice Spacer taking up the legacy `provenWithdrawals` mapping slot."}, "mutability": "mutable", "name": "spacer_52_0_32", "nameLocation": "2946:14:135", "scope": 87971, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 87172, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "2930:7:135", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "private"}, {"id": 87176, "nodeType": "VariableDeclaration", "src": "3072:26:135", "nodes": [], "constant": false, "documentation": {"id": 87174, "nodeType": "StructuredDocumentation", "src": "2967:100:135", "text": "@custom:legacy\n @custom:spacer paused\n @notice Spacer for backwards compatibility."}, "mutability": "mutable", "name": "spacer_53_0_1", "nameLocation": "3085:13:135", "scope": 87971, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 87175, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3072:4:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "private"}, {"id": 87180, "nodeType": "VariableDeclaration", "src": "3156:40:135", "nodes": [], "constant": false, "documentation": {"id": 87177, "nodeType": "StructuredDocumentation", "src": "3105:46:135", "text": "@notice Contract of the Superchain Config."}, "functionSelector": "35e80ab3", "mutability": "mutable", "name": "superchainConfig", "nameLocation": "3180:16:135", "scope": 87971, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_SuperchainConfig_$88793", "typeString": "contract SuperchainConfig"}, "typeName": {"id": 87179, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 87178, "name": "SuperchainConfig", "nodeType": "IdentifierPath", "referencedDeclaration": 88793, "src": "3156:16:135"}, "referencedDeclaration": 88793, "src": "3156:16:135", "typeDescriptions": {"typeIdentifier": "t_contract$_SuperchainConfig_$88793", "typeString": "contract SuperchainConfig"}}, "visibility": "public"}, {"id": 87183, "nodeType": "VariableDeclaration", "src": "3327:30:135", "nodes": [], "constant": false, "documentation": {"id": 87181, "nodeType": "StructuredDocumentation", "src": "3203:119:135", "text": "@custom:legacy\n @custom:spacer l2Oracle\n @notice Spacer taking up the legacy `l2Oracle` address slot."}, "mutability": "mutable", "name": "spacer_54_0_20", "nameLocation": "3343:14:135", "scope": 87971, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 87182, "name": "address", "nodeType": "ElementaryTypeName", "src": "3327:7:135", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "private"}, {"id": 87187, "nodeType": "VariableDeclaration", "src": "3443:32:135", "nodes": [], "constant": false, "documentation": {"id": 87184, "nodeType": "StructuredDocumentation", "src": "3364:74:135", "text": "@notice Contract of the SystemConfig.\n @custom:network-specific"}, "functionSelector": "33d7e2bd", "mutability": "mutable", "name": "systemConfig", "nameLocation": "3463:12:135", "scope": 87971, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_SystemConfig_$89607", "typeString": "contract SystemConfig"}, "typeName": {"id": 87186, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 87185, "name": "SystemConfig", "nodeType": "IdentifierPath", "referencedDeclaration": 89607, "src": "3443:12:135"}, "referencedDeclaration": 89607, "src": "3443:12:135", "typeDescriptions": {"typeIdentifier": "t_contract$_SystemConfig_$89607", "typeString": "contract SystemConfig"}}, "visibility": "public"}, {"id": 87191, "nodeType": "VariableDeclaration", "src": "3566:44:135", "nodes": [], "constant": false, "documentation": {"id": 87188, "nodeType": "StructuredDocumentation", "src": "3482:79:135", "text": "@notice Address of the DisputeGameFactory.\n @custom:network-specific"}, "functionSelector": "f2b4e617", "mutability": "mutable", "name": "disputeGameFactory", "nameLocation": "3592:18:135", "scope": 87971, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_DisputeGameFactory_$97682", "typeString": "contract DisputeGameFactory"}, "typeName": {"id": 87190, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 87189, "name": "DisputeGameFactory", "nodeType": "IdentifierPath", "referencedDeclaration": 97682, "src": "3566:18:135"}, "referencedDeclaration": 97682, "src": "3566:18:135", "typeDescriptions": {"typeIdentifier": "t_contract$_DisputeGameFactory_$97682", "typeString": "contract DisputeGameFactory"}}, "visibility": "public"}, {"id": 87199, "nodeType": "VariableDeclaration", "src": "3712:81:135", "nodes": [], "constant": false, "documentation": {"id": 87192, "nodeType": "StructuredDocumentation", "src": "3617:90:135", "text": "@notice A mapping of withdrawal hashes to proof submitters to `ProvenWithdrawal` data."}, "functionSelector": "bb2c727e", "mutability": "mutable", "name": "provenWithdra<PERSON>s", "nameLocation": "3776:17:135", "scope": 87971, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_mapping$_t_address_$_t_struct$_ProvenWithdrawal_$87148_storage_$_$", "typeString": "mapping(bytes32 => mapping(address => struct OptimismPortal2.ProvenWithdrawal))"}, "typeName": {"id": 87198, "keyType": {"id": 87193, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "3720:7:135", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "Mapping", "src": "3712:56:135", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_mapping$_t_address_$_t_struct$_ProvenWithdrawal_$87148_storage_$_$", "typeString": "mapping(bytes32 => mapping(address => struct OptimismPortal2.ProvenWithdrawal))"}, "valueType": {"id": 87197, "keyType": {"id": 87194, "name": "address", "nodeType": "ElementaryTypeName", "src": "3739:7:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "3731:36:135", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_struct$_ProvenWithdrawal_$87148_storage_$", "typeString": "mapping(address => struct OptimismPortal2.ProvenWithdrawal)"}, "valueType": {"id": 87196, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 87195, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType": "IdentifierPath", "referencedDeclaration": 87148, "src": "3750:16:135"}, "referencedDeclaration": 87148, "src": "3750:16:135", "typeDescriptions": {"typeIdentifier": "t_struct$_ProvenWithdrawal_$87148_storage_ptr", "typeString": "struct OptimismPortal2.<PERSON><PERSON><PERSON><PERSON>dra<PERSON>"}}}}, "visibility": "public"}, {"id": 87205, "nodeType": "VariableDeclaration", "src": "3892:57:135", "nodes": [], "constant": false, "documentation": {"id": 87200, "nodeType": "StructuredDocumentation", "src": "3800:87:135", "text": "@notice A mapping of dispute game addresses to whether or not they are blacklisted."}, "functionSelector": "45884d32", "mutability": "mutable", "name": "disputeGameBlacklist", "nameLocation": "3929:20:135", "scope": 87971, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_contract$_IDisputeGame_$100327_$_t_bool_$", "typeString": "mapping(contract IDisputeGame => bool)"}, "typeName": {"id": 87204, "keyType": {"id": 87202, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 87201, "name": "IDisputeGame", "nodeType": "IdentifierPath", "referencedDeclaration": 100327, "src": "3900:12:135"}, "referencedDeclaration": 100327, "src": "3900:12:135", "typeDescriptions": {"typeIdentifier": "t_contract$_IDisputeGame_$100327", "typeString": "contract IDisputeGame"}}, "nodeType": "Mapping", "src": "3892:29:135", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_contract$_IDisputeGame_$100327_$_t_bool_$", "typeString": "mapping(contract IDisputeGame => bool)"}, "valueType": {"id": 87203, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3916:4:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}}, "visibility": "public"}, {"id": 87209, "nodeType": "VariableDeclaration", "src": "4041:33:135", "nodes": [], "constant": false, "documentation": {"id": 87206, "nodeType": "StructuredDocumentation", "src": "3956:80:135", "text": "@notice The game type that the OptimismPortal consults for output proposals."}, "functionSelector": "3c9f397c", "mutability": "mutable", "name": "respectedGameType", "nameLocation": "4057:17:135", "scope": 87971, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_GameType_$103271", "typeString": "GameType"}, "typeName": {"id": 87208, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 87207, "name": "GameType", "nodeType": "IdentifierPath", "referencedDeclaration": 103271, "src": "4041:8:135"}, "referencedDeclaration": 103271, "src": "4041:8:135", "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_GameType_$103271", "typeString": "GameType"}}, "visibility": "public"}, {"id": 87212, "nodeType": "VariableDeclaration", "src": "4162:40:135", "nodes": [], "constant": false, "documentation": {"id": 87210, "nodeType": "StructuredDocumentation", "src": "4081:76:135", "text": "@notice The timestamp at which the respected game type was last updated."}, "functionSelector": "4fd0434c", "mutability": "mutable", "name": "respectedGameTypeUpdatedAt", "nameLocation": "4176:26:135", "scope": 87971, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "typeName": {"id": 87211, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "4162:6:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "visibility": "public"}, {"id": 87218, "nodeType": "VariableDeclaration", "src": "4315:52:135", "nodes": [], "constant": false, "documentation": {"id": 87213, "nodeType": "StructuredDocumentation", "src": "4209:101:135", "text": "@notice Mapping of withdrawal hashes to addresses that have submitted a proof for the withdrawal."}, "functionSelector": "a3860f48", "mutability": "mutable", "name": "proofSubmitters", "nameLocation": "4352:15:135", "scope": 87971, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_array$_t_address_$dyn_storage_$", "typeString": "mapping(bytes32 => address[])"}, "typeName": {"id": 87217, "keyType": {"id": 87214, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "4323:7:135", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "Mapping", "src": "4315:29:135", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_array$_t_address_$dyn_storage_$", "typeString": "mapping(bytes32 => address[])"}, "valueType": {"baseType": {"id": 87215, "name": "address", "nodeType": "ElementaryTypeName", "src": "4334:7:135", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 87216, "nodeType": "ArrayTypeName", "src": "4334:9:135", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}}, "visibility": "public"}, {"id": 87229, "nodeType": "EventDefinition", "src": "4878:112:135", "nodes": [], "anonymous": false, "documentation": {"id": 87219, "nodeType": "StructuredDocumentation", "src": "4374:499:135", "text": "@notice Emitted when a transaction is deposited from L1 to L2.\n         The parameters of this event are read by the rollup node and used to derive deposit\n         transactions on L2.\n @param from       Address that triggered the deposit transaction.\n @param to         Address that the deposit transaction is directed to.\n @param version    Version of this deposit transaction event.\n @param opaqueData ABI encoded deposit data to be parsed off-chain."}, "eventSelector": "b3813568d9991fc951961fcb4c784893574240a28925604d09fc577c55bb7c32", "name": "TransactionDeposited", "nameLocation": "4884:20:135", "parameters": {"id": 87228, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 87221, "indexed": true, "mutability": "mutable", "name": "from", "nameLocation": "4921:4:135", "nodeType": "VariableDeclaration", "scope": 87229, "src": "4905:20:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 87220, "name": "address", "nodeType": "ElementaryTypeName", "src": "4905:7:135", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 87223, "indexed": true, "mutability": "mutable", "name": "to", "nameLocation": "4943:2:135", "nodeType": "VariableDeclaration", "scope": 87229, "src": "4927:18:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 87222, "name": "address", "nodeType": "ElementaryTypeName", "src": "4927:7:135", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 87225, "indexed": true, "mutability": "mutable", "name": "version", "nameLocation": "4963:7:135", "nodeType": "VariableDeclaration", "scope": 87229, "src": "4947:23:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 87224, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4947:7:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 87227, "indexed": false, "mutability": "mutable", "name": "opaqueData", "nameLocation": "4978:10:135", "nodeType": "VariableDeclaration", "scope": 87229, "src": "4972:16:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 87226, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "4972:5:135", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "4904:85:135"}}, {"id": 87238, "nodeType": "EventDefinition", "src": "5294:97:135", "nodes": [], "anonymous": false, "documentation": {"id": 87230, "nodeType": "StructuredDocumentation", "src": "4996:293:135", "text": "@notice Emitted when a withdrawal transaction is proven.\n @param withdrawalHash Hash of the withdrawal transaction.\n @param from           Address that triggered the withdrawal transaction.\n @param to             Address that the withdrawal transaction is directed to."}, "eventSelector": "67a6208cfcc0801d50f6cbe764733f4fddf66ac0b04442061a8a8c0cb6b63f62", "name": "WithdrawalProven", "nameLocation": "5300:16:135", "parameters": {"id": 87237, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 87232, "indexed": true, "mutability": "mutable", "name": "withdrawalHash", "nameLocation": "5333:14:135", "nodeType": "VariableDeclaration", "scope": 87238, "src": "5317:30:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 87231, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "5317:7:135", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 87234, "indexed": true, "mutability": "mutable", "name": "from", "nameLocation": "5365:4:135", "nodeType": "VariableDeclaration", "scope": 87238, "src": "5349:20:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 87233, "name": "address", "nodeType": "ElementaryTypeName", "src": "5349:7:135", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 87236, "indexed": true, "mutability": "mutable", "name": "to", "nameLocation": "5387:2:135", "nodeType": "VariableDeclaration", "scope": 87238, "src": "5371:18:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 87235, "name": "address", "nodeType": "ElementaryTypeName", "src": "5371:7:135", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "5316:74:135"}}, {"id": 87245, "nodeType": "EventDefinition", "src": "5612:72:135", "nodes": [], "anonymous": false, "documentation": {"id": 87239, "nodeType": "StructuredDocumentation", "src": "5397:210:135", "text": "@notice Emitted when a withdrawal transaction is finalized.\n @param withdrawalHash Hash of the withdrawal transaction.\n @param success        Whether the withdrawal transaction was successful."}, "eventSelector": "db5c7652857aa163daadd670e116628fb42e869d8ac4251ef8971d9e5727df1b", "name": "WithdrawalFinalized", "nameLocation": "5618:19:135", "parameters": {"id": 87244, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 87241, "indexed": true, "mutability": "mutable", "name": "withdrawalHash", "nameLocation": "5654:14:135", "nodeType": "VariableDeclaration", "scope": 87245, "src": "5638:30:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 87240, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "5638:7:135", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 87243, "indexed": false, "mutability": "mutable", "name": "success", "nameLocation": "5675:7:135", "nodeType": "VariableDeclaration", "scope": 87245, "src": "5670:12:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 87242, "name": "bool", "nodeType": "ElementaryTypeName", "src": "5670:4:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "5637:46:135"}}, {"id": 87256, "nodeType": "ModifierDefinition", "src": "5727:86:135", "nodes": [], "body": {"id": 87255, "nodeType": "Block", "src": "5752:61:135", "nodes": [], "statements": [{"condition": {"arguments": [], "expression": {"argumentTypes": [], "id": 87248, "name": "paused", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87383, "src": "5766:6:135", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_bool_$", "typeString": "function () view returns (bool)"}}, "id": 87249, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "5766:8:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 87253, "nodeType": "IfStatement", "src": "5762:33:135", "trueBody": {"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 87250, "name": "CallPaused", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103990, "src": "5783:10:135", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 87251, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "5783:12:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87252, "nodeType": "RevertStatement", "src": "5776:19:135"}}, {"id": 87254, "nodeType": "PlaceholderStatement", "src": "5805:1:135"}]}, "documentation": {"id": 87246, "nodeType": "StructuredDocumentation", "src": "5690:32:135", "text": "@notice Reverts when paused."}, "name": "whenNotPaused", "nameLocation": "5736:13:135", "parameters": {"id": 87247, "nodeType": "ParameterList", "parameters": [], "src": "5749:2:135"}, "virtual": false, "visibility": "internal"}, {"id": 87260, "nodeType": "VariableDeclaration", "src": "5882:40:135", "nodes": [], "baseFunctions": [109416], "constant": true, "documentation": {"id": 87257, "nodeType": "StructuredDocumentation", "src": "5819:58:135", "text": "@notice Semantic version.\n @custom:semver 3.8.0"}, "functionSelector": "54fd4d50", "mutability": "constant", "name": "version", "nameLocation": "5905:7:135", "scope": 87971, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 87258, "name": "string", "nodeType": "ElementaryTypeName", "src": "5882:6:135", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"hexValue": "332e382e30", "id": 87259, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5915:7:135", "typeDescriptions": {"typeIdentifier": "t_stringliteral_f9c59c463d339610f985b3aa69b5b5031ed3afd32f941c9c4c60b492e8c1a90f", "typeString": "literal_string \"3.8.0\""}, "value": "3.8.0"}, "visibility": "public"}, {"id": 87302, "nodeType": "FunctionDefinition", "src": "5985:513:135", "nodes": [], "body": {"id": 87301, "nodeType": "Block", "src": "6075:423:135", "nodes": [], "statements": [{"expression": {"id": 87270, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 87268, "name": "PROOF_MATURITY_DELAY_SECONDS", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87151, "src": "6085:28:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 87269, "name": "_proofMaturityDelaySeconds", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87263, "src": "6116:26:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6085:57:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 87271, "nodeType": "ExpressionStatement", "src": "6085:57:135"}, {"expression": {"id": 87274, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 87272, "name": "DISPUTE_GAME_FINALITY_DELAY_SECONDS", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87154, "src": "6152:35:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 87273, "name": "_disputeGameFinalityDelaySeconds", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87265, "src": "6190:32:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6152:70:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 87275, "nodeType": "ExpressionStatement", "src": "6152:70:135"}, {"expression": {"arguments": [{"arguments": [{"arguments": [{"hexValue": "30", "id": 87280, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "6306:1:135", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 87279, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "6298:7:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 87278, "name": "address", "nodeType": "ElementaryTypeName", "src": "6298:7:135", "typeDescriptions": {}}}, "id": 87281, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "6298:10:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 87277, "name": "DisputeGameFactory", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 97682, "src": "6279:18:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_DisputeGameFactory_$97682_$", "typeString": "type(contract DisputeGameFactory)"}}, "id": 87282, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "6279:30:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_DisputeGameFactory_$97682", "typeString": "contract DisputeGameFactory"}}, {"arguments": [{"arguments": [{"hexValue": "30", "id": 87286, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "6359:1:135", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 87285, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "6351:7:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 87284, "name": "address", "nodeType": "ElementaryTypeName", "src": "6351:7:135", "typeDescriptions": {}}}, "id": 87287, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "6351:10:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 87283, "name": "SystemConfig", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 89607, "src": "6338:12:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_SystemConfig_$89607_$", "typeString": "type(contract SystemConfig)"}}, "id": 87288, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "6338:24:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_SystemConfig_$89607", "typeString": "contract SystemConfig"}}, {"arguments": [{"arguments": [{"hexValue": "30", "id": 87292, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "6420:1:135", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 87291, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "6412:7:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 87290, "name": "address", "nodeType": "ElementaryTypeName", "src": "6412:7:135", "typeDescriptions": {}}}, "id": 87293, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "6412:10:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 87289, "name": "SuperchainConfig", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 88793, "src": "6395:16:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_SuperchainConfig_$88793_$", "typeString": "type(contract SuperchainConfig)"}}, "id": 87294, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "6395:28:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_SuperchainConfig_$88793", "typeString": "contract SuperchainConfig"}}, {"arguments": [{"hexValue": "30", "id": 87297, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "6478:1:135", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "expression": {"id": 87295, "name": "GameType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103271, "src": "6464:8:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_userDefinedValueType$_GameType_$103271_$", "typeString": "type(GameType)"}}, "id": 87296, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberName": "wrap", "nodeType": "MemberAccess", "src": "6464:13:135", "typeDescriptions": {"typeIdentifier": "t_function_wrap_pure$_t_uint32_$returns$_t_userDefinedValueType$_GameType_$103271_$", "typeString": "function (uint32) pure returns (GameType)"}}, "id": 87298, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "6464:16:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_GameType_$103271", "typeString": "GameType"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_DisputeGameFactory_$97682", "typeString": "contract DisputeGameFactory"}, {"typeIdentifier": "t_contract$_SystemConfig_$89607", "typeString": "contract SystemConfig"}, {"typeIdentifier": "t_contract$_SuperchainConfig_$88793", "typeString": "contract SuperchainConfig"}, {"typeIdentifier": "t_userDefinedValueType$_GameType_$103271", "typeString": "GameType"}], "id": 87276, "name": "initialize", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87361, "src": "6233:10:135", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_contract$_DisputeGameFactory_$97682_$_t_contract$_SystemConfig_$89607_$_t_contract$_SuperchainConfig_$88793_$_t_userDefinedValueType$_GameType_$103271_$returns$__$", "typeString": "function (contract DisputeGameFactory,contract SystemConfig,contract SuperchainConfig,GameType)"}}, "id": 87299, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": ["_disputeGameFactory", "_systemConfig", "_superchainConfig", "_initialRespectedGameType"], "nodeType": "FunctionCall", "src": "6233:258:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87300, "nodeType": "ExpressionStatement", "src": "6233:258:135"}]}, "documentation": {"id": 87261, "nodeType": "StructuredDocumentation", "src": "5929:51:135", "text": "@notice Constructs the OptimismPortal contract."}, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "parameters": {"id": 87266, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 87263, "mutability": "mutable", "name": "_proofMaturityDelaySeconds", "nameLocation": "6005:26:135", "nodeType": "VariableDeclaration", "scope": 87302, "src": "5997:34:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 87262, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "5997:7:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 87265, "mutability": "mutable", "name": "_disputeGameFinalityDelaySeconds", "nameLocation": "6041:32:135", "nodeType": "VariableDeclaration", "scope": 87302, "src": "6033:40:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 87264, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "6033:7:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "5996:78:135"}, "returnParameters": {"id": 87267, "nodeType": "ParameterList", "parameters": [], "src": "6075:0:135"}, "scope": 87971, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 87361, "nodeType": "FunctionDefinition", "src": "6730:971:135", "nodes": [], "body": {"id": 87360, "nodeType": "Block", "src": "6967:734:135", "nodes": [], "statements": [{"expression": {"id": 87322, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 87320, "name": "disputeGameFactory", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87191, "src": "6977:18:135", "typeDescriptions": {"typeIdentifier": "t_contract$_DisputeGameFactory_$97682", "typeString": "contract DisputeGameFactory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 87321, "name": "_disputeGameFactory", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87306, "src": "6998:19:135", "typeDescriptions": {"typeIdentifier": "t_contract$_DisputeGameFactory_$97682", "typeString": "contract DisputeGameFactory"}}, "src": "6977:40:135", "typeDescriptions": {"typeIdentifier": "t_contract$_DisputeGameFactory_$97682", "typeString": "contract DisputeGameFactory"}}, "id": 87323, "nodeType": "ExpressionStatement", "src": "6977:40:135"}, {"expression": {"id": 87326, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 87324, "name": "systemConfig", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87187, "src": "7027:12:135", "typeDescriptions": {"typeIdentifier": "t_contract$_SystemConfig_$89607", "typeString": "contract SystemConfig"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 87325, "name": "_systemConfig", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87309, "src": "7042:13:135", "typeDescriptions": {"typeIdentifier": "t_contract$_SystemConfig_$89607", "typeString": "contract SystemConfig"}}, "src": "7027:28:135", "typeDescriptions": {"typeIdentifier": "t_contract$_SystemConfig_$89607", "typeString": "contract SystemConfig"}}, "id": 87327, "nodeType": "ExpressionStatement", "src": "7027:28:135"}, {"expression": {"id": 87330, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 87328, "name": "superchainConfig", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87180, "src": "7065:16:135", "typeDescriptions": {"typeIdentifier": "t_contract$_SuperchainConfig_$88793", "typeString": "contract SuperchainConfig"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 87329, "name": "_superchainConfig", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87312, "src": "7084:17:135", "typeDescriptions": {"typeIdentifier": "t_contract$_SuperchainConfig_$88793", "typeString": "contract SuperchainConfig"}}, "src": "7065:36:135", "typeDescriptions": {"typeIdentifier": "t_contract$_SuperchainConfig_$88793", "typeString": "contract SuperchainConfig"}}, "id": 87331, "nodeType": "ExpressionStatement", "src": "7065:36:135"}, {"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 87337, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 87332, "name": "l2Sender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87165, "src": "7249:8:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"hexValue": "30", "id": 87335, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "7269:1:135", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 87334, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "7261:7:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 87333, "name": "address", "nodeType": "ElementaryTypeName", "src": "7261:7:135", "typeDescriptions": {}}}, "id": 87336, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "7261:10:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "7249:22:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 87356, "nodeType": "IfStatement", "src": "7245:414:135", "trueBody": {"id": 87355, "nodeType": "Block", "src": "7273:386:135", "statements": [{"expression": {"id": 87341, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 87338, "name": "l2Sender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87165, "src": "7287:8:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"expression": {"id": 87339, "name": "Constants", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103096, "src": "7298:9:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_Constants_$103096_$", "typeString": "type(library Constants)"}}, "id": 87340, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "DEFAULT_L2_SENDER", "nodeType": "MemberAccess", "referencedDeclaration": 103058, "src": "7298:27:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "7287:38:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 87342, "nodeType": "ExpressionStatement", "src": "7287:38:135"}, {"expression": {"id": 87349, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 87343, "name": "respectedGameTypeUpdatedAt", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87212, "src": "7485:26:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"expression": {"id": 87346, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "7521:5:135", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 87347, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "timestamp", "nodeType": "MemberAccess", "src": "7521:15:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 87345, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "7514:6:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint64_$", "typeString": "type(uint64)"}, "typeName": {"id": 87344, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "7514:6:135", "typeDescriptions": {}}}, "id": 87348, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "7514:23:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "src": "7485:52:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "id": 87350, "nodeType": "ExpressionStatement", "src": "7485:52:135"}, {"expression": {"id": 87353, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 87351, "name": "respectedGameType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87209, "src": "7603:17:135", "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_GameType_$103271", "typeString": "GameType"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 87352, "name": "_initialRespectedGameType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87315, "src": "7623:25:135", "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_GameType_$103271", "typeString": "GameType"}}, "src": "7603:45:135", "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_GameType_$103271", "typeString": "GameType"}}, "id": 87354, "nodeType": "ExpressionStatement", "src": "7603:45:135"}]}}, {"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 87357, "name": "__ResourceMetering_init", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 88580, "src": "7669:23:135", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 87358, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "7669:25:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87359, "nodeType": "ExpressionStatement", "src": "7669:25:135"}]}, "documentation": {"id": 87303, "nodeType": "StructuredDocumentation", "src": "6504:221:135", "text": "@notice Initializer.\n @param _disputeGameFactory Contract of the DisputeGameFactory.\n @param _systemConfig Contract of the SystemConfig.\n @param _superchainConfig Contract of the SuperchainConfig."}, "functionSelector": "8e819e54", "implemented": true, "kind": "function", "modifiers": [{"id": 87318, "kind": "modifierInvocation", "modifierName": {"id": 87317, "name": "initializer", "nodeType": "IdentifierPath", "referencedDeclaration": 49598, "src": "6951:11:135"}, "nodeType": "ModifierInvocation", "src": "6951:11:135"}], "name": "initialize", "nameLocation": "6739:10:135", "parameters": {"id": 87316, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 87306, "mutability": "mutable", "name": "_disputeGameFactory", "nameLocation": "6778:19:135", "nodeType": "VariableDeclaration", "scope": 87361, "src": "6759:38:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_DisputeGameFactory_$97682", "typeString": "contract DisputeGameFactory"}, "typeName": {"id": 87305, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 87304, "name": "DisputeGameFactory", "nodeType": "IdentifierPath", "referencedDeclaration": 97682, "src": "6759:18:135"}, "referencedDeclaration": 97682, "src": "6759:18:135", "typeDescriptions": {"typeIdentifier": "t_contract$_DisputeGameFactory_$97682", "typeString": "contract DisputeGameFactory"}}, "visibility": "internal"}, {"constant": false, "id": 87309, "mutability": "mutable", "name": "_systemConfig", "nameLocation": "6820:13:135", "nodeType": "VariableDeclaration", "scope": 87361, "src": "6807:26:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_SystemConfig_$89607", "typeString": "contract SystemConfig"}, "typeName": {"id": 87308, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 87307, "name": "SystemConfig", "nodeType": "IdentifierPath", "referencedDeclaration": 89607, "src": "6807:12:135"}, "referencedDeclaration": 89607, "src": "6807:12:135", "typeDescriptions": {"typeIdentifier": "t_contract$_SystemConfig_$89607", "typeString": "contract SystemConfig"}}, "visibility": "internal"}, {"constant": false, "id": 87312, "mutability": "mutable", "name": "_superchainConfig", "nameLocation": "6860:17:135", "nodeType": "VariableDeclaration", "scope": 87361, "src": "6843:34:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_SuperchainConfig_$88793", "typeString": "contract SuperchainConfig"}, "typeName": {"id": 87311, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 87310, "name": "SuperchainConfig", "nodeType": "IdentifierPath", "referencedDeclaration": 88793, "src": "6843:16:135"}, "referencedDeclaration": 88793, "src": "6843:16:135", "typeDescriptions": {"typeIdentifier": "t_contract$_SuperchainConfig_$88793", "typeString": "contract SuperchainConfig"}}, "visibility": "internal"}, {"constant": false, "id": 87315, "mutability": "mutable", "name": "_initialRespectedGameType", "nameLocation": "6896:25:135", "nodeType": "VariableDeclaration", "scope": 87361, "src": "6887:34:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_GameType_$103271", "typeString": "GameType"}, "typeName": {"id": 87314, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 87313, "name": "GameType", "nodeType": "IdentifierPath", "referencedDeclaration": 103271, "src": "6887:8:135"}, "referencedDeclaration": 103271, "src": "6887:8:135", "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_GameType_$103271", "typeString": "GameType"}}, "visibility": "internal"}], "src": "6749:178:135"}, "returnParameters": {"id": 87319, "nodeType": "ParameterList", "parameters": [], "src": "6967:0:135"}, "scope": 87971, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 87372, "nodeType": "FunctionDefinition", "src": "7954:101:135", "nodes": [], "body": {"id": 87371, "nodeType": "Block", "src": "8004:51:135", "nodes": [], "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 87367, "name": "superchainConfig", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87180, "src": "8021:16:135", "typeDescriptions": {"typeIdentifier": "t_contract$_SuperchainConfig_$88793", "typeString": "contract SuperchainConfig"}}, "id": 87368, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "guardian", "nodeType": "MemberAccess", "referencedDeclaration": 88693, "src": "8021:25:135", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_address_$", "typeString": "function () view external returns (address)"}}, "id": 87369, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "8021:27:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 87366, "id": 87370, "nodeType": "Return", "src": "8014:34:135"}]}, "documentation": {"id": 87362, "nodeType": "StructuredDocumentation", "src": "7707:242:135", "text": "@notice Getter function for the address of the guardian.\n         Public getter is legacy and will be removed in the future. Use `SuperchainConfig.guardian()` instead.\n @return Address of the guardian.\n @custom:legacy"}, "functionSelector": "452a9320", "implemented": true, "kind": "function", "modifiers": [], "name": "guardian", "nameLocation": "7963:8:135", "parameters": {"id": 87363, "nodeType": "ParameterList", "parameters": [], "src": "7971:2:135"}, "returnParameters": {"id": 87366, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 87365, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 87372, "src": "7995:7:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 87364, "name": "address", "nodeType": "ElementaryTypeName", "src": "7995:7:135", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "7994:9:135"}, "scope": 87971, "stateMutability": "view", "virtual": false, "visibility": "public"}, {"id": 87383, "nodeType": "FunctionDefinition", "src": "8115:94:135", "nodes": [], "body": {"id": 87382, "nodeType": "Block", "src": "8160:49:135", "nodes": [], "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 87378, "name": "superchainConfig", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87180, "src": "8177:16:135", "typeDescriptions": {"typeIdentifier": "t_contract$_SuperchainConfig_$88793", "typeString": "contract SuperchainConfig"}}, "id": 87379, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "paused", "nodeType": "MemberAccess", "referencedDeclaration": 88707, "src": "8177:23:135", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_bool_$", "typeString": "function () view external returns (bool)"}}, "id": 87380, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "8177:25:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 87377, "id": 87381, "nodeType": "Return", "src": "8170:32:135"}]}, "documentation": {"id": 87373, "nodeType": "StructuredDocumentation", "src": "8061:49:135", "text": "@notice Getter for the current paused status."}, "functionSelector": "5c975abb", "implemented": true, "kind": "function", "modifiers": [], "name": "paused", "nameLocation": "8124:6:135", "parameters": {"id": 87374, "nodeType": "ParameterList", "parameters": [], "src": "8130:2:135"}, "returnParameters": {"id": 87377, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 87376, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 87383, "src": "8154:4:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 87375, "name": "bool", "nodeType": "ElementaryTypeName", "src": "8154:4:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "8153:6:135"}, "scope": 87971, "stateMutability": "view", "virtual": false, "visibility": "public"}, {"id": 87392, "nodeType": "FunctionDefinition", "src": "8268:119:135", "nodes": [], "body": {"id": 87391, "nodeType": "Block", "src": "8335:52:135", "nodes": [], "statements": [{"expression": {"id": 87389, "name": "PROOF_MATURITY_DELAY_SECONDS", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87151, "src": "8352:28:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 87388, "id": 87390, "nodeType": "Return", "src": "8345:35:135"}]}, "documentation": {"id": 87384, "nodeType": "StructuredDocumentation", "src": "8215:48:135", "text": "@notice Getter for the proof maturity delay."}, "functionSelector": "bf653a5c", "implemented": true, "kind": "function", "modifiers": [], "name": "proofMaturityDelaySeconds", "nameLocation": "8277:25:135", "parameters": {"id": 87385, "nodeType": "ParameterList", "parameters": [], "src": "8302:2:135"}, "returnParameters": {"id": 87388, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 87387, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 87392, "src": "8326:7:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 87386, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "8326:7:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "8325:9:135"}, "scope": 87971, "stateMutability": "view", "virtual": false, "visibility": "public"}, {"id": 87401, "nodeType": "FunctionDefinition", "src": "8453:132:135", "nodes": [], "body": {"id": 87400, "nodeType": "Block", "src": "8526:59:135", "nodes": [], "statements": [{"expression": {"id": 87398, "name": "DISPUTE_GAME_FINALITY_DELAY_SECONDS", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87154, "src": "8543:35:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 87397, "id": 87399, "nodeType": "Return", "src": "8536:42:135"}]}, "documentation": {"id": 87393, "nodeType": "StructuredDocumentation", "src": "8393:55:135", "text": "@notice Getter for the dispute game finality delay."}, "functionSelector": "952b2797", "implemented": true, "kind": "function", "modifiers": [], "name": "disputeGameFinalityDelaySeconds", "nameLocation": "8462:31:135", "parameters": {"id": 87394, "nodeType": "ParameterList", "parameters": [], "src": "8493:2:135"}, "returnParameters": {"id": 87397, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 87396, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 87401, "src": "8517:7:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 87395, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "8517:7:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "8516:9:135"}, "scope": 87971, "stateMutability": "view", "virtual": false, "visibility": "public"}, {"id": 87416, "nodeType": "FunctionDefinition", "src": "9078:120:135", "nodes": [], "body": {"id": 87415, "nodeType": "Block", "src": "9151:47:135", "nodes": [], "statements": [{"expression": {"commonType": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "id": 87413, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "id": 87411, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 87409, "name": "_byteCount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87404, "src": "9168:10:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "BinaryOperation", "operator": "*", "rightExpression": {"hexValue": "3136", "id": 87410, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "9181:2:135", "typeDescriptions": {"typeIdentifier": "t_rational_16_by_1", "typeString": "int_const 16"}, "value": "16"}, "src": "9168:15:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"hexValue": "3231303030", "id": 87412, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "9186:5:135", "typeDescriptions": {"typeIdentifier": "t_rational_21000_by_1", "typeString": "int_const 21000"}, "value": "21000"}, "src": "9168:23:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "functionReturnParameters": 87408, "id": 87414, "nodeType": "Return", "src": "9161:30:135"}]}, "documentation": {"id": 87402, "nodeType": "StructuredDocumentation", "src": "8591:482:135", "text": "@notice Computes the minimum gas limit for a deposit.\n         The minimum gas limit linearly increases based on the size of the calldata.\n         This is to prevent users from creating L2 resource usage without paying for it.\n         This function can be used when interacting with the portal to ensure forwards\n         compatibility.\n @param _byteCount Number of bytes in the calldata.\n @return The minimum gas limit for a deposit."}, "functionSelector": "a35d99df", "implemented": true, "kind": "function", "modifiers": [], "name": "minimumGasLimit", "nameLocation": "9087:15:135", "parameters": {"id": 87405, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 87404, "mutability": "mutable", "name": "_byteCount", "nameLocation": "9110:10:135", "nodeType": "VariableDeclaration", "scope": 87416, "src": "9103:17:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "typeName": {"id": 87403, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "9103:6:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "visibility": "internal"}], "src": "9102:19:135"}, "returnParameters": {"id": 87408, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 87407, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 87416, "src": "9143:6:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "typeName": {"id": 87406, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "9143:6:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "visibility": "internal"}], "src": "9142:8:135"}, "scope": 87971, "stateMutability": "pure", "virtual": false, "visibility": "public"}, {"id": 87434, "nodeType": "FunctionDefinition", "src": "9577:130:135", "nodes": [], "body": {"id": 87433, "nodeType": "Block", "src": "9604:103:135", "nodes": [], "statements": [{"expression": {"arguments": [{"expression": {"id": 87421, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "9633:3:135", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 87422, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "src": "9633:10:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"expression": {"id": 87423, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "9645:3:135", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 87424, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "value", "nodeType": "MemberAccess", "src": "9645:9:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 87425, "name": "RECEIVE_DEFAULT_GAS_LIMIT", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87162, "src": "9656:25:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, {"hexValue": "66616c7365", "id": 87426, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "9683:5:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}, {"arguments": [{"hexValue": "", "id": 87429, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9696:2:135", "typeDescriptions": {"typeIdentifier": "t_stringliteral_c5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470", "typeString": "literal_string \"\""}, "value": ""}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_c5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470", "typeString": "literal_string \"\""}], "id": 87428, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "9690:5:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes_storage_ptr_$", "typeString": "type(bytes storage pointer)"}, "typeName": {"id": 87427, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "9690:5:135", "typeDescriptions": {}}}, "id": 87430, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "9690:9:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint64", "typeString": "uint64"}, {"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 87420, "name": "depositTransaction", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87785, "src": "9614:18:135", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$_t_uint256_$_t_uint64_$_t_bool_$_t_bytes_memory_ptr_$returns$__$", "typeString": "function (address,uint256,uint64,bool,bytes memory)"}}, "id": 87431, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "9614:86:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87432, "nodeType": "ExpressionStatement", "src": "9614:86:135"}]}, "documentation": {"id": 87417, "nodeType": "StructuredDocumentation", "src": "9204:368:135", "text": "@notice Accepts value so that users can send ETH directly to this contract and have the\n         funds be deposited to their address on L2. This is intended as a convenience\n         function for EOAs. Contracts should call the depositTransaction() function directly\n         otherwise any deposited funds will be lost due to address aliasing."}, "implemented": true, "kind": "receive", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "parameters": {"id": 87418, "nodeType": "ParameterList", "parameters": [], "src": "9584:2:135"}, "returnParameters": {"id": 87419, "nodeType": "ParameterList", "parameters": [], "src": "9604:0:135"}, "scope": 87971, "stateMutability": "payable", "virtual": false, "visibility": "external"}, {"id": 87439, "nodeType": "FunctionDefinition", "src": "9921:77:135", "nodes": [], "body": {"id": 87438, "nodeType": "Block", "src": "9959:39:135", "nodes": [], "statements": []}, "documentation": {"id": 87435, "nodeType": "StructuredDocumentation", "src": "9713:203:135", "text": "@notice Accepts ETH value without triggering a deposit to L2.\n         This function mainly exists for the sake of the migration between the legacy\n         Optimism system and Bedrock."}, "functionSelector": "8b4c40b0", "implemented": true, "kind": "function", "modifiers": [], "name": "donateETH", "nameLocation": "9930:9:135", "parameters": {"id": 87436, "nodeType": "ParameterList", "parameters": [], "src": "9939:2:135"}, "returnParameters": {"id": 87437, "nodeType": "ParameterList", "parameters": [], "src": "9959:0:135"}, "scope": 87971, "stateMutability": "payable", "virtual": false, "visibility": "external"}, {"id": 87452, "nodeType": "FunctionDefinition", "src": "10247:152:135", "nodes": [], "body": {"id": 87451, "nodeType": "Block", "src": "10346:53:135", "nodes": [], "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 87447, "name": "systemConfig", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87187, "src": "10363:12:135", "typeDescriptions": {"typeIdentifier": "t_contract$_SystemConfig_$89607", "typeString": "contract SystemConfig"}}, "id": 87448, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "resourceConfig", "nodeType": "MemberAccess", "referencedDeclaration": 89527, "src": "10363:27:135", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_struct$_ResourceConfig_$88258_memory_ptr_$", "typeString": "function () view external returns (struct ResourceMetering.ResourceConfig memory)"}}, "id": 87449, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "10363:29:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ResourceConfig_$88258_memory_ptr", "typeString": "struct ResourceMetering.ResourceConfig memory"}}, "functionReturnParameters": 87446, "id": 87450, "nodeType": "Return", "src": "10356:36:135"}]}, "baseFunctions": [88555], "documentation": {"id": 87440, "nodeType": "StructuredDocumentation", "src": "10004:238:135", "text": "@notice Getter for the resource config.\n         Used internally by the ResourceMetering contract.\n         The SystemConfig is the source of truth for the resource config.\n @return ResourceMetering ResourceConfig"}, "implemented": true, "kind": "function", "modifiers": [], "name": "_resourceConfig", "nameLocation": "10256:15:135", "overrides": {"id": 87442, "nodeType": "OverrideSpecifier", "overrides": [], "src": "10288:8:135"}, "parameters": {"id": 87441, "nodeType": "ParameterList", "parameters": [], "src": "10271:2:135"}, "returnParameters": {"id": 87446, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 87445, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 87452, "src": "10306:38:135", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_ResourceConfig_$88258_memory_ptr", "typeString": "struct ResourceMetering.ResourceConfig"}, "typeName": {"id": 87444, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 87443, "name": "ResourceMetering.ResourceConfig", "nodeType": "IdentifierPath", "referencedDeclaration": 88258, "src": "10306:31:135"}, "referencedDeclaration": 88258, "src": "10306:31:135", "typeDescriptions": {"typeIdentifier": "t_struct$_ResourceConfig_$88258_storage_ptr", "typeString": "struct ResourceMetering.ResourceConfig"}}, "visibility": "internal"}], "src": "10305:40:135"}, "scope": 87971, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 87599, "nodeType": "FunctionDefinition", "src": "10816:3564:135", "nodes": [], "body": {"id": 87598, "nodeType": "Block", "src": "11084:3296:135", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 87476, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 87470, "name": "_tx", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87456, "src": "11329:3:135", "typeDescriptions": {"typeIdentifier": "t_struct$_WithdrawalTransaction_$104348_memory_ptr", "typeString": "struct Types.WithdrawalTransaction memory"}}, "id": 87471, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberName": "target", "nodeType": "MemberAccess", "referencedDeclaration": 104341, "src": "11329:10:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"arguments": [{"id": 87474, "name": "this", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -28, "src": "11351:4:135", "typeDescriptions": {"typeIdentifier": "t_contract$_OptimismPortal2_$87971", "typeString": "contract OptimismPortal2"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_OptimismPortal2_$87971", "typeString": "contract OptimismPortal2"}], "id": 87473, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "11343:7:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 87472, "name": "address", "nodeType": "ElementaryTypeName", "src": "11343:7:135", "typeDescriptions": {}}}, "id": 87475, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "11343:13:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "11329:27:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4f7074696d69736d506f7274616c3a20796f752063616e6e6f742073656e64206d6573736167657320746f2074686520706f7274616c20636f6e7472616374", "id": 87477, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11358:65:135", "typeDescriptions": {"typeIdentifier": "t_stringliteral_57e41062e2e7b97ddf730827f5249d28f602a3846dfe107ce36292fb1c029eb8", "typeString": "literal_string \"OptimismPortal: you cannot send messages to the portal contract\""}, "value": "OptimismPortal: you cannot send messages to the portal contract"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_57e41062e2e7b97ddf730827f5249d28f602a3846dfe107ce36292fb1c029eb8", "typeString": "literal_string \"OptimismPortal: you cannot send messages to the portal contract\""}], "id": 87469, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "11321:7:135", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 87478, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "11321:103:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87479, "nodeType": "ExpressionStatement", "src": "11321:103:135"}, {"assignments": [87482, null, 87485], "declarations": [{"constant": false, "id": 87482, "mutability": "mutable", "name": "gameType", "nameLocation": "11525:8:135", "nodeType": "VariableDeclaration", "scope": 87598, "src": "11516:17:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_GameType_$103271", "typeString": "GameType"}, "typeName": {"id": 87481, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 87480, "name": "GameType", "nodeType": "IdentifierPath", "referencedDeclaration": 103271, "src": "11516:8:135"}, "referencedDeclaration": 103271, "src": "11516:8:135", "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_GameType_$103271", "typeString": "GameType"}}, "visibility": "internal"}, null, {"constant": false, "id": 87485, "mutability": "mutable", "name": "gameProxy", "nameLocation": "11549:9:135", "nodeType": "VariableDeclaration", "scope": 87598, "src": "11536:22:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_IDisputeGame_$100327", "typeString": "contract IDisputeGame"}, "typeName": {"id": 87484, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 87483, "name": "IDisputeGame", "nodeType": "IdentifierPath", "referencedDeclaration": 100327, "src": "11536:12:135"}, "referencedDeclaration": 100327, "src": "11536:12:135", "typeDescriptions": {"typeIdentifier": "t_contract$_IDisputeGame_$100327", "typeString": "contract IDisputeGame"}}, "visibility": "internal"}], "id": 87490, "initialValue": {"arguments": [{"id": 87488, "name": "_disputeGameIndex", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87458, "src": "11593:17:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 87486, "name": "disputeGameFactory", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87191, "src": "11562:18:135", "typeDescriptions": {"typeIdentifier": "t_contract$_DisputeGameFactory_$97682", "typeString": "contract DisputeGameFactory"}}, "id": 87487, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "gameAtIndex", "nodeType": "MemberAccess", "referencedDeclaration": 97346, "src": "11562:30:135", "typeDescriptions": {"typeIdentifier": "t_function_external_view$_t_uint256_$returns$_t_userDefinedValueType$_GameType_$103271_$_t_userDefinedValueType$_Timestamp_$103261_$_t_contract$_IDisputeGame_$100327_$", "typeString": "function (uint256) view external returns (GameType,Timestamp,contract IDisputeGame)"}}, "id": 87489, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "11562:49:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$_t_userDefinedValueType$_GameType_$103271_$_t_userDefinedValueType$_Timestamp_$103261_$_t_contract$_IDisputeGame_$100327_$", "typeString": "tuple(GameType,Timestamp,contract IDisputeGame)"}}, "nodeType": "VariableDeclarationStatement", "src": "11515:96:135"}, {"assignments": [87493], "declarations": [{"constant": false, "id": 87493, "mutability": "mutable", "name": "outputRoot", "nameLocation": "11627:10:135", "nodeType": "VariableDeclaration", "scope": 87598, "src": "11621:16:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_Claim_$103255", "typeString": "<PERSON><PERSON><PERSON>"}, "typeName": {"id": 87492, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 87491, "name": "<PERSON><PERSON><PERSON>", "nodeType": "IdentifierPath", "referencedDeclaration": 103255, "src": "11621:5:135"}, "referencedDeclaration": 103255, "src": "11621:5:135", "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_Claim_$103255", "typeString": "<PERSON><PERSON><PERSON>"}}, "visibility": "internal"}], "id": 87497, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 87494, "name": "gameProxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87485, "src": "11640:9:135", "typeDescriptions": {"typeIdentifier": "t_contract$_IDisputeGame_$100327", "typeString": "contract IDisputeGame"}}, "id": 87495, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "rootClaim", "nodeType": "MemberAccess", "referencedDeclaration": 100294, "src": "11640:19:135", "typeDescriptions": {"typeIdentifier": "t_function_external_pure$__$returns$_t_userDefinedValueType$_Claim_$103255_$", "typeString": "function () pure external returns (Claim)"}}, "id": 87496, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "11640:21:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_Claim_$103255", "typeString": "<PERSON><PERSON><PERSON>"}}, "nodeType": "VariableDeclarationStatement", "src": "11621:40:135"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint32", "typeString": "uint32"}, "id": 87505, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 87499, "name": "gameType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87482, "src": "11758:8:135", "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_GameType_$103271", "typeString": "GameType"}}, "id": 87500, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "raw", "nodeType": "MemberAccess", "referencedDeclaration": 101150, "src": "11758:12:135", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_userDefinedValueType$_GameType_$103271_$returns$_t_uint32_$bound_to$_t_userDefinedValueType$_GameType_$103271_$", "typeString": "function (GameType) pure returns (uint32)"}}, "id": 87501, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "11758:14:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 87502, "name": "respectedGameType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87209, "src": "11776:17:135", "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_GameType_$103271", "typeString": "GameType"}}, "id": 87503, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "raw", "nodeType": "MemberAccess", "referencedDeclaration": 101150, "src": "11776:21:135", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_userDefinedValueType$_GameType_$103271_$returns$_t_uint32_$bound_to$_t_userDefinedValueType$_GameType_$103271_$", "typeString": "function (GameType) pure returns (uint32)"}}, "id": 87504, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "11776:23:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "src": "11758:41:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4f7074696d69736d506f7274616c3a20696e76616c69642067616d652074797065", "id": 87506, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "11801:35:135", "typeDescriptions": {"typeIdentifier": "t_stringliteral_ea6e52a7a06be8d460d58a9fb591f5b7ad20643cdd834b0004aaeaa0647b1d4b", "typeString": "literal_string \"OptimismPortal: invalid game type\""}, "value": "OptimismPortal: invalid game type"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_ea6e52a7a06be8d460d58a9fb591f5b7ad20643cdd834b0004aaeaa0647b1d4b", "typeString": "literal_string \"OptimismPortal: invalid game type\""}], "id": 87498, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "11750:7:135", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 87507, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "11750:87:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87508, "nodeType": "ExpressionStatement", "src": "11750:87:135"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "id": 87517, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 87510, "name": "outputRoot", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87493, "src": "11957:10:135", "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_Claim_$103255", "typeString": "<PERSON><PERSON><PERSON>"}}, "id": 87511, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "raw", "nodeType": "MemberAccess", "referencedDeclaration": 101085, "src": "11957:14:135", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_userDefinedValueType$_Claim_$103255_$returns$_t_bytes32_$bound_to$_t_userDefinedValueType$_Claim_$103255_$", "typeString": "function (Claim) pure returns (bytes32)"}}, "id": 87512, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "11957:16:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"id": 87515, "name": "_outputRootProof", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87461, "src": "12005:16:135", "typeDescriptions": {"typeIdentifier": "t_struct$_OutputRootProof_$104316_calldata_ptr", "typeString": "struct Types.OutputRootProof calldata"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_OutputRootProof_$104316_calldata_ptr", "typeString": "struct Types.OutputRootProof calldata"}], "expression": {"id": 87513, "name": "Hashing", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103936, "src": "11977:7:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_Hashing_$103936_$", "typeString": "type(library Hashing)"}}, "id": 87514, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "hashOutputRootProof", "nodeType": "MemberAccess", "referencedDeclaration": 103935, "src": "11977:27:135", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_struct$_OutputRootProof_$104316_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (struct Types.OutputRootProof memory) pure returns (bytes32)"}}, "id": 87516, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "11977:45:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "src": "11957:65:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4f7074696d69736d506f7274616c3a20696e76616c6964206f757470757420726f6f742070726f6f66", "id": 87518, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12036:43:135", "typeDescriptions": {"typeIdentifier": "t_stringliteral_490ec653897228799e7e4c4af8b1fd3b4a0688df98d026b46afa352ce9876996", "typeString": "literal_string \"OptimismPortal: invalid output root proof\""}, "value": "OptimismPortal: invalid output root proof"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_490ec653897228799e7e4c4af8b1fd3b4a0688df98d026b46afa352ce9876996", "typeString": "literal_string \"OptimismPortal: invalid output root proof\""}], "id": 87509, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "11936:7:135", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 87519, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "11936:153:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87520, "nodeType": "ExpressionStatement", "src": "11936:153:135"}, {"assignments": [87522], "declarations": [{"constant": false, "id": 87522, "mutability": "mutable", "name": "withdrawalHash", "nameLocation": "12208:14:135", "nodeType": "VariableDeclaration", "scope": 87598, "src": "12200:22:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 87521, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "12200:7:135", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "id": 87527, "initialValue": {"arguments": [{"id": 87525, "name": "_tx", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87456, "src": "12248:3:135", "typeDescriptions": {"typeIdentifier": "t_struct$_WithdrawalTransaction_$104348_memory_ptr", "typeString": "struct Types.WithdrawalTransaction memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_WithdrawalTransaction_$104348_memory_ptr", "typeString": "struct Types.WithdrawalTransaction memory"}], "expression": {"id": 87523, "name": "Hashing", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103936, "src": "12225:7:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_Hashing_$103936_$", "typeString": "type(library Hashing)"}}, "id": 87524, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType": "MemberAccess", "referencedDeclaration": 103911, "src": "12225:22:135", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_struct$_WithdrawalTransaction_$104348_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (struct Types.WithdrawalTransaction memory) pure returns (bytes32)"}}, "id": 87526, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "12225:27:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "VariableDeclarationStatement", "src": "12200:52:135"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_enum$_GameStatus_$103277", "typeString": "enum GameStatus"}, "id": 87534, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 87529, "name": "gameProxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87485, "src": "12424:9:135", "typeDescriptions": {"typeIdentifier": "t_contract$_IDisputeGame_$100327", "typeString": "contract IDisputeGame"}}, "id": 87530, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "status", "nodeType": "MemberAccess", "referencedDeclaration": 100274, "src": "12424:16:135", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_enum$_GameStatus_$103277_$", "typeString": "function () view external returns (enum GameStatus)"}}, "id": 87531, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "12424:18:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_enum$_GameStatus_$103277", "typeString": "enum GameStatus"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"expression": {"id": 87532, "name": "GameStatus", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103277, "src": "12446:10:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_enum$_GameStatus_$103277_$", "typeString": "type(enum GameStatus)"}}, "id": 87533, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberName": "CHALLENGER_WINS", "nodeType": "MemberAccess", "referencedDeclaration": 103275, "src": "12446:26:135", "typeDescriptions": {"typeIdentifier": "t_enum$_GameStatus_$103277", "typeString": "enum GameStatus"}}, "src": "12424:48:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4f7074696d69736d506f7274616c3a2063616e6e6f742070726f766520616761696e737420696e76616c696420646973707574652067616d6573", "id": 87535, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "12486:60:135", "typeDescriptions": {"typeIdentifier": "t_stringliteral_69fd02e8f1261d2d4a8ae7fdb140ea99e9eb488a3b5b9ae3c51756d573f7f1f7", "typeString": "literal_string \"OptimismPortal: cannot prove against invalid dispute games\""}, "value": "OptimismPortal: cannot prove against invalid dispute games"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_69fd02e8f1261d2d4a8ae7fdb140ea99e9eb488a3b5b9ae3c51756d573f7f1f7", "typeString": "literal_string \"OptimismPortal: cannot prove against invalid dispute games\""}], "id": 87528, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "12403:7:135", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 87536, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "12403:153:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87537, "nodeType": "ExpressionStatement", "src": "12403:153:135"}, {"assignments": [87539], "declarations": [{"constant": false, "id": 87539, "mutability": "mutable", "name": "storageKey", "nameLocation": "12800:10:135", "nodeType": "VariableDeclaration", "scope": 87598, "src": "12792:18:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 87538, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "12792:7:135", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "id": 87550, "initialValue": {"arguments": [{"arguments": [{"id": 87543, "name": "withdrawalHash", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87522, "src": "12864:14:135", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"arguments": [{"hexValue": "30", "id": 87546, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "12904:1:135", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 87545, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "12896:7:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 87544, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "12896:7:135", "typeDescriptions": {}}}, "id": 87547, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "12896:10:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 87541, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "12836:3:135", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 87542, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberName": "encode", "nodeType": "MemberAccess", "src": "12836:10:135", "typeDescriptions": {"typeIdentifier": "t_function_abiencode_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 87548, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "12836:147:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 87540, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "12813:9:135", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 87549, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "12813:180:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "VariableDeclarationStatement", "src": "12792:201:135"}, {"expression": {"arguments": [{"arguments": [{"arguments": [{"id": 87556, "name": "storageKey", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87539, "src": "13419:10:135", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "expression": {"id": 87554, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "13408:3:135", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 87555, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberName": "encode", "nodeType": "MemberAccess", "src": "13408:10:135", "typeDescriptions": {"typeIdentifier": "t_function_abiencode_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 87557, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "13408:22:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, {"hexValue": "01", "id": 87558, "isConstant": false, "isLValue": false, "isPure": true, "kind": "hexString", "lValueRequested": false, "nodeType": "Literal", "src": "13456:7:135", "typeDescriptions": {"typeIdentifier": "t_stringliteral_5fe7f977e71dba2ea1a68e21057beebb9be2ac30c6410aa38d4f3fbe41dcffd2", "typeString": "literal_string hex\"01\""}, "value": "\u0001"}, {"id": 87559, "name": "_withdrawalProof", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87464, "src": "13489:16:135", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_calldata_ptr_$dyn_calldata_ptr", "typeString": "bytes calldata[] calldata"}}, {"expression": {"id": 87560, "name": "_outputRootProof", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87461, "src": "13530:16:135", "typeDescriptions": {"typeIdentifier": "t_struct$_OutputRootProof_$104316_calldata_ptr", "typeString": "struct Types.OutputRootProof calldata"}}, "id": 87561, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "messagePasserStorageRoot", "nodeType": "MemberAccess", "referencedDeclaration": 104313, "src": "13530:41:135", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}, {"typeIdentifier": "t_stringliteral_5fe7f977e71dba2ea1a68e21057beebb9be2ac30c6410aa38d4f3fbe41dcffd2", "typeString": "literal_string hex\"01\""}, {"typeIdentifier": "t_array$_t_bytes_calldata_ptr_$dyn_calldata_ptr", "typeString": "bytes calldata[] calldata"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "expression": {"id": 87552, "name": "SecureMerkleTrie", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 106033, "src": "13346:16:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_SecureMerkleTrie_$106033_$", "typeString": "type(library SecureMerkleTrie)"}}, "id": 87553, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "verifyInclusionProof", "nodeType": "MemberAccess", "referencedDeclaration": 105985, "src": "13346:37:135", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_bytes_memory_ptr_$_t_bytes_memory_ptr_$_t_array$_t_bytes_memory_ptr_$dyn_memory_ptr_$_t_bytes32_$returns$_t_bool_$", "typeString": "function (bytes memory,bytes memory,bytes memory[] memory,bytes32) pure returns (bool)"}}, "id": 87562, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": ["_key", "_value", "_proof", "_root"], "nodeType": "FunctionCall", "src": "13346:240:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4f7074696d69736d506f7274616c3a20696e76616c6964207769746864726177616c20696e636c7573696f6e2070726f6f66", "id": 87563, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "13600:52:135", "typeDescriptions": {"typeIdentifier": "t_stringliteral_11b666636981dad70da1c1a9e87589eb7d9c042eacd4d25e887aac557f6cd6b9", "typeString": "literal_string \"OptimismPortal: invalid withdrawal inclusion proof\""}, "value": "OptimismPortal: invalid withdrawal inclusion proof"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_11b666636981dad70da1c1a9e87589eb7d9c042eacd4d25e887aac557f6cd6b9", "typeString": "literal_string \"OptimismPortal: invalid withdrawal inclusion proof\""}], "id": 87551, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "13325:7:135", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 87564, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "13325:337:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87565, "nodeType": "ExpressionStatement", "src": "13325:337:135"}, {"expression": {"id": 87580, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"baseExpression": {"id": 87566, "name": "provenWithdra<PERSON>s", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87199, "src": "13960:17:135", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_mapping$_t_address_$_t_struct$_ProvenWithdrawal_$87148_storage_$_$", "typeString": "mapping(bytes32 => mapping(address => struct OptimismPortal2.ProvenWithdrawal storage ref))"}}, "id": 87570, "indexExpression": {"id": 87567, "name": "withdrawalHash", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87522, "src": "13978:14:135", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "13960:33:135", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_struct$_ProvenWithdrawal_$87148_storage_$", "typeString": "mapping(address => struct OptimismPortal2.ProvenWithdrawal storage ref)"}}, "id": 87571, "indexExpression": {"expression": {"id": 87568, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "13994:3:135", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 87569, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "src": "13994:10:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "13960:45:135", "typeDescriptions": {"typeIdentifier": "t_struct$_ProvenWithdrawal_$87148_storage", "typeString": "struct OptimismPortal2.ProvenWithdrawal storage ref"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 87573, "name": "gameProxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87485, "src": "14057:9:135", "typeDescriptions": {"typeIdentifier": "t_contract$_IDisputeGame_$100327", "typeString": "contract IDisputeGame"}}, {"arguments": [{"expression": {"id": 87576, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "14086:5:135", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 87577, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "timestamp", "nodeType": "MemberAccess", "src": "14086:15:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 87575, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "14079:6:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint64_$", "typeString": "type(uint64)"}, "typeName": {"id": 87574, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "14079:6:135", "typeDescriptions": {}}}, "id": 87578, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "14079:23:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_IDisputeGame_$100327", "typeString": "contract IDisputeGame"}, {"typeIdentifier": "t_uint64", "typeString": "uint64"}], "id": 87572, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87148, "src": "14020:16:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ProvenWithdrawal_$87148_storage_ptr_$", "typeString": "type(struct OptimismPortal2.ProvenWithdrawal storage pointer)"}}, "id": 87579, "isConstant": false, "isLValue": false, "isPure": false, "kind": "structConstructorCall", "lValueRequested": false, "names": ["disputeGameProxy", "timestamp"], "nodeType": "FunctionCall", "src": "14020:85:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ProvenWithdrawal_$87148_memory_ptr", "typeString": "struct OptimismPortal2.<PERSON>venWithdrawal memory"}}, "src": "13960:145:135", "typeDescriptions": {"typeIdentifier": "t_struct$_ProvenWithdrawal_$87148_storage", "typeString": "struct OptimismPortal2.ProvenWithdrawal storage ref"}}, "id": 87581, "nodeType": "ExpressionStatement", "src": "13960:145:135"}, {"eventCall": {"arguments": [{"id": 87583, "name": "withdrawalHash", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87522, "src": "14182:14:135", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"expression": {"id": 87584, "name": "_tx", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87456, "src": "14198:3:135", "typeDescriptions": {"typeIdentifier": "t_struct$_WithdrawalTransaction_$104348_memory_ptr", "typeString": "struct Types.WithdrawalTransaction memory"}}, "id": 87585, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": 104339, "src": "14198:10:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"expression": {"id": 87586, "name": "_tx", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87456, "src": "14210:3:135", "typeDescriptions": {"typeIdentifier": "t_struct$_WithdrawalTransaction_$104348_memory_ptr", "typeString": "struct Types.WithdrawalTransaction memory"}}, "id": 87587, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberName": "target", "nodeType": "MemberAccess", "referencedDeclaration": 104341, "src": "14210:10:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 87582, "name": "WithdrawalProven", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87238, "src": "14165:16:135", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_bytes32_$_t_address_$_t_address_$returns$__$", "typeString": "function (bytes32,address,address)"}}, "id": 87588, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "14165:56:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87589, "nodeType": "EmitStatement", "src": "14160:61:135"}, {"expression": {"arguments": [{"expression": {"id": 87594, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "14362:3:135", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 87595, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "src": "14362:10:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"baseExpression": {"id": 87590, "name": "proofSubmitters", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87218, "src": "14325:15:135", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_array$_t_address_$dyn_storage_$", "typeString": "mapping(bytes32 => address[] storage ref)"}}, "id": 87592, "indexExpression": {"id": 87591, "name": "withdrawalHash", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87522, "src": "14341:14:135", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "14325:31:135", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage", "typeString": "address[] storage ref"}}, "id": 87593, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "push", "nodeType": "MemberAccess", "src": "14325:36:135", "typeDescriptions": {"typeIdentifier": "t_function_arraypush_nonpayable$_t_array$_t_address_$dyn_storage_ptr_$_t_address_$returns$__$bound_to$_t_array$_t_address_$dyn_storage_ptr_$", "typeString": "function (address[] storage pointer,address)"}}, "id": 87596, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "14325:48:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87597, "nodeType": "ExpressionStatement", "src": "14325:48:135"}]}, "documentation": {"id": 87453, "nodeType": "StructuredDocumentation", "src": "10405:406:135", "text": "@notice Proves a withdrawal transaction.\n @param _tx               Withdrawal transaction to finalize.\n @param _disputeGameIndex Index of the dispute game to prove the withdrawal against.\n @param _outputRootProof  Inclusion proof of the L2ToL1MessagePasser contract's storage root.\n @param _withdrawalProof  Inclusion proof of the withdrawal in L2ToL1MessagePasser contract."}, "functionSelector": "4870496f", "implemented": true, "kind": "function", "modifiers": [{"id": 87467, "kind": "modifierInvocation", "modifierName": {"id": 87466, "name": "whenNotPaused", "nodeType": "IdentifierPath", "referencedDeclaration": 87256, "src": "11066:13:135"}, "nodeType": "ModifierInvocation", "src": "11066:13:135"}], "name": "proveWithdrawalTransaction", "nameLocation": "10825:26:135", "parameters": {"id": 87465, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 87456, "mutability": "mutable", "name": "_tx", "nameLocation": "10896:3:135", "nodeType": "VariableDeclaration", "scope": 87599, "src": "10861:38:135", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_WithdrawalTransaction_$104348_memory_ptr", "typeString": "struct Types.WithdrawalTransaction"}, "typeName": {"id": 87455, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 87454, "name": "Types.WithdrawalTransaction", "nodeType": "IdentifierPath", "referencedDeclaration": 104348, "src": "10861:27:135"}, "referencedDeclaration": 104348, "src": "10861:27:135", "typeDescriptions": {"typeIdentifier": "t_struct$_WithdrawalTransaction_$104348_storage_ptr", "typeString": "struct Types.WithdrawalTransaction"}}, "visibility": "internal"}, {"constant": false, "id": 87458, "mutability": "mutable", "name": "_disputeGameIndex", "nameLocation": "10917:17:135", "nodeType": "VariableDeclaration", "scope": 87599, "src": "10909:25:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 87457, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "10909:7:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 87461, "mutability": "mutable", "name": "_outputRootProof", "nameLocation": "10975:16:135", "nodeType": "VariableDeclaration", "scope": 87599, "src": "10944:47:135", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_struct$_OutputRootProof_$104316_calldata_ptr", "typeString": "struct Types.OutputRootProof"}, "typeName": {"id": 87460, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 87459, "name": "Types.OutputRootProof", "nodeType": "IdentifierPath", "referencedDeclaration": 104316, "src": "10944:21:135"}, "referencedDeclaration": 104316, "src": "10944:21:135", "typeDescriptions": {"typeIdentifier": "t_struct$_OutputRootProof_$104316_storage_ptr", "typeString": "struct Types.OutputRootProof"}}, "visibility": "internal"}, {"constant": false, "id": 87464, "mutability": "mutable", "name": "_withdrawalProof", "nameLocation": "11018:16:135", "nodeType": "VariableDeclaration", "scope": 87599, "src": "11001:33:135", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_calldata_ptr_$dyn_calldata_ptr", "typeString": "bytes[]"}, "typeName": {"baseType": {"id": 87462, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "11001:5:135", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "id": 87463, "nodeType": "ArrayTypeName", "src": "11001:7:135", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes_storage_$dyn_storage_ptr", "typeString": "bytes[]"}}, "visibility": "internal"}], "src": "10851:189:135"}, "returnParameters": {"id": 87468, "nodeType": "ParameterList", "parameters": [], "src": "11084:0:135"}, "scope": 87971, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 87615, "nodeType": "FunctionDefinition", "src": "14493:178:135", "nodes": [], "body": {"id": 87614, "nodeType": "Block", "src": "14595:76:135", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 87609, "name": "_tx", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87603, "src": "14648:3:135", "typeDescriptions": {"typeIdentifier": "t_struct$_WithdrawalTransaction_$104348_memory_ptr", "typeString": "struct Types.WithdrawalTransaction memory"}}, {"expression": {"id": 87610, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "14653:3:135", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 87611, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "src": "14653:10:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_WithdrawalTransaction_$104348_memory_ptr", "typeString": "struct Types.WithdrawalTransaction memory"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 87608, "name": "finalizeWithdrawalTransactionExternalProof", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87695, "src": "14605:42:135", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_struct$_WithdrawalTransaction_$104348_memory_ptr_$_t_address_$returns$__$", "typeString": "function (struct Types.WithdrawalTransaction memory,address)"}}, "id": 87612, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "14605:59:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87613, "nodeType": "ExpressionStatement", "src": "14605:59:135"}]}, "documentation": {"id": 87600, "nodeType": "StructuredDocumentation", "src": "14386:102:135", "text": "@notice Finalizes a withdrawal transaction.\n @param _tx Withdrawal transaction to finalize."}, "functionSelector": "8c3152e9", "implemented": true, "kind": "function", "modifiers": [{"id": 87606, "kind": "modifierInvocation", "modifierName": {"id": 87605, "name": "whenNotPaused", "nodeType": "IdentifierPath", "referencedDeclaration": 87256, "src": "14581:13:135"}, "nodeType": "ModifierInvocation", "src": "14581:13:135"}], "name": "finalizeWithdrawalTransaction", "nameLocation": "14502:29:135", "parameters": {"id": 87604, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 87603, "mutability": "mutable", "name": "_tx", "nameLocation": "14567:3:135", "nodeType": "VariableDeclaration", "scope": 87615, "src": "14532:38:135", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_WithdrawalTransaction_$104348_memory_ptr", "typeString": "struct Types.WithdrawalTransaction"}, "typeName": {"id": 87602, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 87601, "name": "Types.WithdrawalTransaction", "nodeType": "IdentifierPath", "referencedDeclaration": 104348, "src": "14532:27:135"}, "referencedDeclaration": 104348, "src": "14532:27:135", "typeDescriptions": {"typeIdentifier": "t_struct$_WithdrawalTransaction_$104348_storage_ptr", "typeString": "struct Types.WithdrawalTransaction"}}, "visibility": "internal"}], "src": "14531:40:135"}, "returnParameters": {"id": 87607, "nodeType": "ParameterList", "parameters": [], "src": "14595:0:135"}, "scope": 87971, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 87695, "nodeType": "FunctionDefinition", "src": "14882:2403:135", "nodes": [], "body": {"id": 87694, "nodeType": "Block", "src": "15062:2223:135", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 87630, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 87627, "name": "l2Sender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87165, "src": "15328:8:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"id": 87628, "name": "Constants", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103096, "src": "15340:9:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_Constants_$103096_$", "typeString": "type(library Constants)"}}, "id": 87629, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "DEFAULT_L2_SENDER", "nodeType": "MemberAccess", "referencedDeclaration": 103058, "src": "15340:27:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "15328:39:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4f7074696d69736d506f7274616c3a2063616e206f6e6c792074726967676572206f6e65207769746864726177616c20706572207472616e73616374696f6e", "id": 87631, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "15369:65:135", "typeDescriptions": {"typeIdentifier": "t_stringliteral_452e6500a4013b85635a7a9b231d68a5197c7f7579d0b96d0b2f2e5fe6b5995b", "typeString": "literal_string \"OptimismPortal: can only trigger one withdrawal per transaction\""}, "value": "OptimismPortal: can only trigger one withdrawal per transaction"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_452e6500a4013b85635a7a9b231d68a5197c7f7579d0b96d0b2f2e5fe6b5995b", "typeString": "literal_string \"OptimismPortal: can only trigger one withdrawal per transaction\""}], "id": 87626, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "15307:7:135", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 87632, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "15307:137:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87633, "nodeType": "ExpressionStatement", "src": "15307:137:135"}, {"assignments": [87635], "declarations": [{"constant": false, "id": 87635, "mutability": "mutable", "name": "withdrawalHash", "nameLocation": "15503:14:135", "nodeType": "VariableDeclaration", "scope": 87694, "src": "15495:22:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 87634, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "15495:7:135", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "id": 87640, "initialValue": {"arguments": [{"id": 87638, "name": "_tx", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87619, "src": "15543:3:135", "typeDescriptions": {"typeIdentifier": "t_struct$_WithdrawalTransaction_$104348_memory_ptr", "typeString": "struct Types.WithdrawalTransaction memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_WithdrawalTransaction_$104348_memory_ptr", "typeString": "struct Types.WithdrawalTransaction memory"}], "expression": {"id": 87636, "name": "Hashing", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103936, "src": "15520:7:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_Hashing_$103936_$", "typeString": "type(library Hashing)"}}, "id": 87637, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType": "MemberAccess", "referencedDeclaration": 103911, "src": "15520:22:135", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_struct$_WithdrawalTransaction_$104348_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (struct Types.WithdrawalTransaction memory) pure returns (bytes32)"}}, "id": 87639, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "15520:27:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "VariableDeclarationStatement", "src": "15495:52:135"}, {"expression": {"arguments": [{"id": 87642, "name": "withdrawalHash", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87635, "src": "15629:14:135", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 87643, "name": "_proofSubmitter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87621, "src": "15645:15:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 87641, "name": "checkWithdrawal", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87956, "src": "15613:15:135", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_bytes32_$_t_address_$returns$__$", "typeString": "function (bytes32,address) view"}}, "id": 87644, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "15613:48:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87645, "nodeType": "ExpressionStatement", "src": "15613:48:135"}, {"expression": {"id": 87650, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 87646, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87170, "src": "15741:20:135", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_bool_$", "typeString": "mapping(bytes32 => bool)"}}, "id": 87648, "indexExpression": {"id": 87647, "name": "withdrawalHash", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87635, "src": "15762:14:135", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "15741:36:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 87649, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "15780:4:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "15741:43:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 87651, "nodeType": "ExpressionStatement", "src": "15741:43:135"}, {"expression": {"id": 87655, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 87652, "name": "l2Sender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87165, "src": "15878:8:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"expression": {"id": 87653, "name": "_tx", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87619, "src": "15889:3:135", "typeDescriptions": {"typeIdentifier": "t_struct$_WithdrawalTransaction_$104348_memory_ptr", "typeString": "struct Types.WithdrawalTransaction memory"}}, "id": 87654, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": 104339, "src": "15889:10:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "15878:21:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 87656, "nodeType": "ExpressionStatement", "src": "15878:21:135"}, {"assignments": [87658], "declarations": [{"constant": false, "id": 87658, "mutability": "mutable", "name": "success", "nameLocation": "16524:7:135", "nodeType": "VariableDeclaration", "scope": 87694, "src": "16519:12:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 87657, "name": "bool", "nodeType": "ElementaryTypeName", "src": "16519:4:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "id": 87670, "initialValue": {"arguments": [{"expression": {"id": 87661, "name": "_tx", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87619, "src": "16558:3:135", "typeDescriptions": {"typeIdentifier": "t_struct$_WithdrawalTransaction_$104348_memory_ptr", "typeString": "struct Types.WithdrawalTransaction memory"}}, "id": 87662, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberName": "target", "nodeType": "MemberAccess", "referencedDeclaration": 104341, "src": "16558:10:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"expression": {"id": 87663, "name": "_tx", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87619, "src": "16570:3:135", "typeDescriptions": {"typeIdentifier": "t_struct$_WithdrawalTransaction_$104348_memory_ptr", "typeString": "struct Types.WithdrawalTransaction memory"}}, "id": 87664, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberName": "gasLimit", "nodeType": "MemberAccess", "referencedDeclaration": 104345, "src": "16570:12:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 87665, "name": "_tx", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87619, "src": "16584:3:135", "typeDescriptions": {"typeIdentifier": "t_struct$_WithdrawalTransaction_$104348_memory_ptr", "typeString": "struct Types.WithdrawalTransaction memory"}}, "id": 87666, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberName": "value", "nodeType": "MemberAccess", "referencedDeclaration": 104343, "src": "16584:9:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 87667, "name": "_tx", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87619, "src": "16595:3:135", "typeDescriptions": {"typeIdentifier": "t_struct$_WithdrawalTransaction_$104348_memory_ptr", "typeString": "struct Types.WithdrawalTransaction memory"}}, "id": 87668, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberName": "data", "nodeType": "MemberAccess", "referencedDeclaration": 104347, "src": "16595:8:135", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 87659, "name": "SafeCall", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 104213, "src": "16534:8:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_SafeCall_$104213_$", "typeString": "type(library SafeCall)"}}, "id": 87660, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "callWithMinGas", "nodeType": "MemberAccess", "referencedDeclaration": 104212, "src": "16534:23:135", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$_t_uint256_$_t_uint256_$_t_bytes_memory_ptr_$returns$_t_bool_$", "typeString": "function (address,uint256,uint256,bytes memory) returns (bool)"}}, "id": 87669, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "16534:70:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "VariableDeclarationStatement", "src": "16519:85:135"}, {"expression": {"id": 87674, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 87671, "name": "l2Sender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87165, "src": "16672:8:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"expression": {"id": 87672, "name": "Constants", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103096, "src": "16683:9:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_Constants_$103096_$", "typeString": "type(library Constants)"}}, "id": 87673, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "DEFAULT_L2_SENDER", "nodeType": "MemberAccess", "referencedDeclaration": 103058, "src": "16683:27:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "16672:38:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 87675, "nodeType": "ExpressionStatement", "src": "16672:38:135"}, {"eventCall": {"arguments": [{"id": 87677, "name": "withdrawalHash", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87635, "src": "16889:14:135", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 87678, "name": "success", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87658, "src": "16905:7:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 87676, "name": "WithdrawalFinalized", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87245, "src": "16869:19:135", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_bytes32_$_t_bool_$returns$__$", "typeString": "function (bytes32,bool)"}}, "id": 87679, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "16869:44:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87680, "nodeType": "EmitStatement", "src": "16864:49:135"}, {"condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 87688, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 87682, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "17177:8:135", "subExpression": {"id": 87681, "name": "success", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87658, "src": "17178:7:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 87687, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 87683, "name": "tx", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -26, "src": "17189:2:135", "typeDescriptions": {"typeIdentifier": "t_magic_transaction", "typeString": "tx"}}, "id": 87684, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "origin", "nodeType": "MemberAccess", "src": "17189:9:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"id": 87685, "name": "Constants", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103096, "src": "17202:9:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_Constants_$103096_$", "typeString": "type(library Constants)"}}, "id": 87686, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "ESTIMATION_ADDRESS", "nodeType": "MemberAccess", "referencedDeclaration": 103054, "src": "17202:28:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "17189:41:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "17177:53:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 87693, "nodeType": "IfStatement", "src": "17173:106:135", "trueBody": {"id": 87692, "nodeType": "Block", "src": "17232:47:135", "statements": [{"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 87689, "name": "GasEstimation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103993, "src": "17253:13:135", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 87690, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "17253:15:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87691, "nodeType": "RevertStatement", "src": "17246:22:135"}]}}]}, "documentation": {"id": 87616, "nodeType": "StructuredDocumentation", "src": "14677:200:135", "text": "@notice Finalizes a withdrawal transaction, using an external proof submitter.\n @param _tx Withdrawal transaction to finalize.\n @param _proofSubmitter Address of the proof submitter."}, "functionSelector": "43ca1c50", "implemented": true, "kind": "function", "modifiers": [{"id": 87624, "kind": "modifierInvocation", "modifierName": {"id": 87623, "name": "whenNotPaused", "nodeType": "IdentifierPath", "referencedDeclaration": 87256, "src": "15044:13:135"}, "nodeType": "ModifierInvocation", "src": "15044:13:135"}], "name": "finalizeWithdrawalTransactionExternalProof", "nameLocation": "14891:42:135", "parameters": {"id": 87622, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 87619, "mutability": "mutable", "name": "_tx", "nameLocation": "14978:3:135", "nodeType": "VariableDeclaration", "scope": 87695, "src": "14943:38:135", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_WithdrawalTransaction_$104348_memory_ptr", "typeString": "struct Types.WithdrawalTransaction"}, "typeName": {"id": 87618, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 87617, "name": "Types.WithdrawalTransaction", "nodeType": "IdentifierPath", "referencedDeclaration": 104348, "src": "14943:27:135"}, "referencedDeclaration": 104348, "src": "14943:27:135", "typeDescriptions": {"typeIdentifier": "t_struct$_WithdrawalTransaction_$104348_storage_ptr", "typeString": "struct Types.WithdrawalTransaction"}}, "visibility": "internal"}, {"constant": false, "id": 87621, "mutability": "mutable", "name": "_proofSubmitter", "nameLocation": "14999:15:135", "nodeType": "VariableDeclaration", "scope": 87695, "src": "14991:23:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 87620, "name": "address", "nodeType": "ElementaryTypeName", "src": "14991:7:135", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "14933:87:135"}, "returnParameters": {"id": 87625, "nodeType": "ParameterList", "parameters": [], "src": "15062:0:135"}, "scope": 87971, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 87785, "nodeType": "FunctionDefinition", "src": "18015:1855:135", "nodes": [], "body": {"id": 87784, "nodeType": "Block", "src": "18236:1634:135", "nodes": [], "statements": [{"condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 87719, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 87712, "name": "_isCreation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87704, "src": "18375:11:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 87718, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 87713, "name": "_to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87698, "src": "18390:3:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"arguments": [{"hexValue": "30", "id": 87716, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "18405:1:135", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 87715, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "18397:7:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 87714, "name": "address", "nodeType": "ElementaryTypeName", "src": "18397:7:135", "typeDescriptions": {}}}, "id": 87717, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "18397:10:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "18390:17:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "18375:32:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 87723, "nodeType": "IfStatement", "src": "18371:56:135", "trueBody": {"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 87720, "name": "<PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103969, "src": "18416:9:135", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 87721, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "18416:11:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87722, "nodeType": "RevertStatement", "src": "18409:18:135"}}, {"condition": {"commonType": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "id": 87732, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 87724, "name": "_gasLimit", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87702, "src": "18579:9:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"arguments": [{"arguments": [{"expression": {"id": 87728, "name": "_data", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87706, "src": "18614:5:135", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 87729, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "length", "nodeType": "MemberAccess", "src": "18614:12:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 87727, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "18607:6:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint64_$", "typeString": "type(uint64)"}, "typeName": {"id": 87726, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "18607:6:135", "typeDescriptions": {}}}, "id": 87730, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "18607:20:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint64", "typeString": "uint64"}], "id": 87725, "name": "minimumGasLimit", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87416, "src": "18591:15:135", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint64_$returns$_t_uint64_$", "typeString": "function (uint64) pure returns (uint64)"}}, "id": 87731, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "18591:37:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "src": "18579:49:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 87736, "nodeType": "IfStatement", "src": "18575:77:135", "trueBody": {"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 87733, "name": "SmallGasLimit", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103975, "src": "18637:13:135", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 87734, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "18637:15:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87735, "nodeType": "RevertStatement", "src": "18630:22:135"}}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 87740, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 87737, "name": "_data", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87706, "src": "19027:5:135", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 87738, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "length", "nodeType": "MemberAccess", "src": "19027:12:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "3132305f303030", "id": 87739, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "19042:7:135", "typeDescriptions": {"typeIdentifier": "t_rational_120000_by_1", "typeString": "int_const 120000"}, "value": "120_000"}, "src": "19027:22:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 87744, "nodeType": "IfStatement", "src": "19023:50:135", "trueBody": {"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 87741, "name": "LargeCalldata", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103972, "src": "19058:13:135", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 87742, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "19058:15:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87743, "nodeType": "RevertStatement", "src": "19051:22:135"}}, {"assignments": [87746], "declarations": [{"constant": false, "id": 87746, "mutability": "mutable", "name": "from", "nameLocation": "19172:4:135", "nodeType": "VariableDeclaration", "scope": 87784, "src": "19164:12:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 87745, "name": "address", "nodeType": "ElementaryTypeName", "src": "19164:7:135", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "id": 87749, "initialValue": {"expression": {"id": 87747, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "19179:3:135", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 87748, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "src": "19179:10:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "VariableDeclarationStatement", "src": "19164:25:135"}, {"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 87754, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 87750, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "19203:3:135", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 87751, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "src": "19203:10:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"expression": {"id": 87752, "name": "tx", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -26, "src": "19217:2:135", "typeDescriptions": {"typeIdentifier": "t_magic_transaction", "typeString": "tx"}}, "id": 87753, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "origin", "nodeType": "MemberAccess", "src": "19217:9:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "19203:23:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 87764, "nodeType": "IfStatement", "src": "19199:108:135", "trueBody": {"id": 87763, "nodeType": "Block", "src": "19228:79:135", "statements": [{"expression": {"id": 87761, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 87755, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87746, "src": "19242:4:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"expression": {"id": 87758, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "19285:3:135", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 87759, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "src": "19285:10:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 87756, "name": "AddressAliasHelper", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 111913, "src": "19249:18:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_AddressAliasHelper_$111913_$", "typeString": "type(library AddressAliasHelper)"}}, "id": 87757, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "applyL1ToL2Alias", "nodeType": "MemberAccess", "referencedDeclaration": 111890, "src": "19249:35:135", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_address_$returns$_t_address_$", "typeString": "function (address) pure returns (address)"}}, "id": 87760, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "19249:47:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "19242:54:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 87762, "nodeType": "ExpressionStatement", "src": "19242:54:135"}]}}, {"assignments": [87766], "declarations": [{"constant": false, "id": 87766, "mutability": "mutable", "name": "opaqueData", "nameLocation": "19577:10:135", "nodeType": "VariableDeclaration", "scope": 87784, "src": "19564:23:135", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 87765, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "19564:5:135", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "id": 87776, "initialValue": {"arguments": [{"expression": {"id": 87769, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "19607:3:135", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 87770, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "value", "nodeType": "MemberAccess", "src": "19607:9:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 87771, "name": "_value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87700, "src": "19618:6:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 87772, "name": "_gasLimit", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87702, "src": "19626:9:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, {"id": 87773, "name": "_isCreation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87704, "src": "19637:11:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"id": 87774, "name": "_data", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87706, "src": "19650:5:135", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint64", "typeString": "uint64"}, {"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 87767, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "19590:3:135", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 87768, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberName": "encodePacked", "nodeType": "MemberAccess", "src": "19590:16:135", "typeDescriptions": {"typeIdentifier": "t_function_abiencodepacked_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 87775, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "19590:66:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "nodeType": "VariableDeclarationStatement", "src": "19564:92:135"}, {"eventCall": {"arguments": [{"id": 87778, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87746, "src": "19824:4:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 87779, "name": "_to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87698, "src": "19830:3:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 87780, "name": "DEPOSIT_VERSION", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87158, "src": "19835:15:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 87781, "name": "opaqueData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87766, "src": "19852:10:135", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 87777, "name": "TransactionDeposited", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87229, "src": "19803:20:135", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$_t_bytes_memory_ptr_$returns$__$", "typeString": "function (address,address,uint256,bytes memory)"}}, "id": 87782, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "19803:60:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87783, "nodeType": "EmitStatement", "src": "19798:65:135"}]}, "documentation": {"id": 87696, "nodeType": "StructuredDocumentation", "src": "17291:719:135", "text": "@notice Accepts deposits of ETH and data, and emits a TransactionDeposited event for use in\n         deriving deposit transactions. Note that if a deposit is made by a contract, its\n         address will be aliased when retrieved using `tx.origin` or `msg.sender`. Consider\n         using the CrossDomainMessenger contracts for a simpler developer experience.\n @param _to         Target address on L2.\n @param _value      ETH value to send to the recipient.\n @param _gasLimit   Amount of L2 gas to purchase by burning gas on L1.\n @param _isCreation Whether or not the transaction is a contract creation.\n @param _data       Data to trigger the recipient with."}, "functionSelector": "e9e05c42", "implemented": true, "kind": "function", "modifiers": [{"arguments": [{"id": 87709, "name": "_gasLimit", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87702, "src": "18221:9:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}], "id": 87710, "kind": "modifierInvocation", "modifierName": {"id": 87708, "name": "metered", "nodeType": "IdentifierPath", "referencedDeclaration": 88284, "src": "18213:7:135"}, "nodeType": "ModifierInvocation", "src": "18213:18:135"}], "name": "depositTransaction", "nameLocation": "18024:18:135", "parameters": {"id": 87707, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 87698, "mutability": "mutable", "name": "_to", "nameLocation": "18060:3:135", "nodeType": "VariableDeclaration", "scope": 87785, "src": "18052:11:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 87697, "name": "address", "nodeType": "ElementaryTypeName", "src": "18052:7:135", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 87700, "mutability": "mutable", "name": "_value", "nameLocation": "18081:6:135", "nodeType": "VariableDeclaration", "scope": 87785, "src": "18073:14:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 87699, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "18073:7:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 87702, "mutability": "mutable", "name": "_gasLimit", "nameLocation": "18104:9:135", "nodeType": "VariableDeclaration", "scope": 87785, "src": "18097:16:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "typeName": {"id": 87701, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "18097:6:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "visibility": "internal"}, {"constant": false, "id": 87704, "mutability": "mutable", "name": "_isCreation", "nameLocation": "18128:11:135", "nodeType": "VariableDeclaration", "scope": 87785, "src": "18123:16:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 87703, "name": "bool", "nodeType": "ElementaryTypeName", "src": "18123:4:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 87706, "mutability": "mutable", "name": "_data", "nameLocation": "18162:5:135", "nodeType": "VariableDeclaration", "scope": 87785, "src": "18149:18:135", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 87705, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "18149:5:135", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "18042:131:135"}, "returnParameters": {"id": 87711, "nodeType": "ParameterList", "parameters": [], "src": "18236:0:135"}, "scope": 87971, "stateMutability": "payable", "virtual": false, "visibility": "public"}, {"id": 87808, "nodeType": "FunctionDefinition", "src": "20049:185:135", "nodes": [], "body": {"id": 87807, "nodeType": "Block", "src": "20115:119:135", "nodes": [], "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 87796, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 87792, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "20129:3:135", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 87793, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "src": "20129:10:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "id": 87794, "name": "guardian", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87372, "src": "20143:8:135", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 87795, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "20143:10:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "20129:24:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 87800, "nodeType": "IfStatement", "src": "20125:51:135", "trueBody": {"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 87797, "name": "Unauthorized", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103987, "src": "20162:12:135", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 87798, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "20162:14:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87799, "nodeType": "RevertStatement", "src": "20155:21:135"}}, {"expression": {"id": 87805, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 87801, "name": "disputeGameBlacklist", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87205, "src": "20186:20:135", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_contract$_IDisputeGame_$100327_$_t_bool_$", "typeString": "mapping(contract IDisputeGame => bool)"}}, "id": 87803, "indexExpression": {"id": 87802, "name": "_disputeGame", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87789, "src": "20207:12:135", "typeDescriptions": {"typeIdentifier": "t_contract$_IDisputeGame_$100327", "typeString": "contract IDisputeGame"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "20186:34:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 87804, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "20223:4:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "20186:41:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 87806, "nodeType": "ExpressionStatement", "src": "20186:41:135"}]}, "documentation": {"id": 87786, "nodeType": "StructuredDocumentation", "src": "19876:168:135", "text": "@notice Blacklists a dispute game. Should only be used in the event that a dispute game resolves incorrectly.\n @param _disputeGame Dispute game to blacklist."}, "functionSelector": "7d6be8dc", "implemented": true, "kind": "function", "modifiers": [], "name": "blacklistDisputeGame", "nameLocation": "20058:20:135", "parameters": {"id": 87790, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 87789, "mutability": "mutable", "name": "_disputeGame", "nameLocation": "20092:12:135", "nodeType": "VariableDeclaration", "scope": 87808, "src": "20079:25:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_IDisputeGame_$100327", "typeString": "contract IDisputeGame"}, "typeName": {"id": 87788, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 87787, "name": "IDisputeGame", "nodeType": "IdentifierPath", "referencedDeclaration": 100327, "src": "20079:12:135"}, "referencedDeclaration": 100327, "src": "20079:12:135", "typeDescriptions": {"typeIdentifier": "t_contract$_IDisputeGame_$100327", "typeString": "contract IDisputeGame"}}, "visibility": "internal"}], "src": "20078:27:135"}, "returnParameters": {"id": 87791, "nodeType": "ParameterList", "parameters": [], "src": "20115:0:135"}, "scope": 87971, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 87837, "nodeType": "FunctionDefinition", "src": "20481:228:135", "nodes": [], "body": {"id": 87836, "nodeType": "Block", "src": "20540:169:135", "nodes": [], "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 87819, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 87815, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "20554:3:135", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 87816, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "src": "20554:10:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "id": 87817, "name": "guardian", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87372, "src": "20568:8:135", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 87818, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "20568:10:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "20554:24:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 87823, "nodeType": "IfStatement", "src": "20550:51:135", "trueBody": {"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 87820, "name": "Unauthorized", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103987, "src": "20587:12:135", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 87821, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "20587:14:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87822, "nodeType": "RevertStatement", "src": "20580:21:135"}}, {"expression": {"id": 87826, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 87824, "name": "respectedGameType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87209, "src": "20611:17:135", "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_GameType_$103271", "typeString": "GameType"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 87825, "name": "_gameType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87812, "src": "20631:9:135", "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_GameType_$103271", "typeString": "GameType"}}, "src": "20611:29:135", "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_GameType_$103271", "typeString": "GameType"}}, "id": 87827, "nodeType": "ExpressionStatement", "src": "20611:29:135"}, {"expression": {"id": 87834, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 87828, "name": "respectedGameTypeUpdatedAt", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87212, "src": "20650:26:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"expression": {"id": 87831, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "20686:5:135", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 87832, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "timestamp", "nodeType": "MemberAccess", "src": "20686:15:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 87830, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "20679:6:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint64_$", "typeString": "type(uint64)"}, "typeName": {"id": 87829, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "20679:6:135", "typeDescriptions": {}}}, "id": 87833, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "20679:23:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "src": "20650:52:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "id": 87835, "nodeType": "ExpressionStatement", "src": "20650:52:135"}]}, "documentation": {"id": 87809, "nodeType": "StructuredDocumentation", "src": "20240:236:135", "text": "@notice Sets the respected game type. Changing this value can alter the security properties of the system,\n         depending on the new game's behavior.\n @param _gameType The game type to consult for output proposals."}, "functionSelector": "7fc48504", "implemented": true, "kind": "function", "modifiers": [], "name": "setRespectedGameType", "nameLocation": "20490:20:135", "parameters": {"id": 87813, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 87812, "mutability": "mutable", "name": "_gameType", "nameLocation": "20520:9:135", "nodeType": "VariableDeclaration", "scope": 87837, "src": "20511:18:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_GameType_$103271", "typeString": "GameType"}, "typeName": {"id": 87811, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 87810, "name": "GameType", "nodeType": "IdentifierPath", "referencedDeclaration": 103271, "src": "20511:8:135"}, "referencedDeclaration": 103271, "src": "20511:8:135", "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_GameType_$103271", "typeString": "GameType"}}, "visibility": "internal"}], "src": "20510:20:135"}, "returnParameters": {"id": 87814, "nodeType": "ParameterList", "parameters": [], "src": "20540:0:135"}, "scope": 87971, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 87956, "nodeType": "FunctionDefinition", "src": "21034:3510:135", "nodes": [], "body": {"id": 87955, "nodeType": "Block", "src": "21121:3423:135", "nodes": [], "statements": [{"assignments": [87847], "declarations": [{"constant": false, "id": 87847, "mutability": "mutable", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameLocation": "21155:16:135", "nodeType": "VariableDeclaration", "scope": 87955, "src": "21131:40:135", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_ProvenWithdrawal_$87148_memory_ptr", "typeString": "struct OptimismPortal2.<PERSON><PERSON><PERSON><PERSON>dra<PERSON>"}, "typeName": {"id": 87846, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 87845, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType": "IdentifierPath", "referencedDeclaration": 87148, "src": "21131:16:135"}, "referencedDeclaration": 87148, "src": "21131:16:135", "typeDescriptions": {"typeIdentifier": "t_struct$_ProvenWithdrawal_$87148_storage_ptr", "typeString": "struct OptimismPortal2.<PERSON><PERSON><PERSON><PERSON>dra<PERSON>"}}, "visibility": "internal"}], "id": 87853, "initialValue": {"baseExpression": {"baseExpression": {"id": 87848, "name": "provenWithdra<PERSON>s", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87199, "src": "21174:17:135", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_mapping$_t_address_$_t_struct$_ProvenWithdrawal_$87148_storage_$_$", "typeString": "mapping(bytes32 => mapping(address => struct OptimismPortal2.ProvenWithdrawal storage ref))"}}, "id": 87850, "indexExpression": {"id": 87849, "name": "_withdrawalHash", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87840, "src": "21192:15:135", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "21174:34:135", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_struct$_ProvenWithdrawal_$87148_storage_$", "typeString": "mapping(address => struct OptimismPortal2.ProvenWithdrawal storage ref)"}}, "id": 87852, "indexExpression": {"id": 87851, "name": "_proofSubmitter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87842, "src": "21209:15:135", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "21174:51:135", "typeDescriptions": {"typeIdentifier": "t_struct$_ProvenWithdrawal_$87148_storage", "typeString": "struct OptimismPortal2.ProvenWithdrawal storage ref"}}, "nodeType": "VariableDeclarationStatement", "src": "21131:94:135"}, {"assignments": [87856], "declarations": [{"constant": false, "id": 87856, "mutability": "mutable", "name": "disputeGameProxy", "nameLocation": "21248:16:135", "nodeType": "VariableDeclaration", "scope": 87955, "src": "21235:29:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_IDisputeGame_$100327", "typeString": "contract IDisputeGame"}, "typeName": {"id": 87855, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 87854, "name": "IDisputeGame", "nodeType": "IdentifierPath", "referencedDeclaration": 100327, "src": "21235:12:135"}, "referencedDeclaration": 100327, "src": "21235:12:135", "typeDescriptions": {"typeIdentifier": "t_contract$_IDisputeGame_$100327", "typeString": "contract IDisputeGame"}}, "visibility": "internal"}], "id": 87859, "initialValue": {"expression": {"id": 87857, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87847, "src": "21267:16:135", "typeDescriptions": {"typeIdentifier": "t_struct$_ProvenWithdrawal_$87148_memory_ptr", "typeString": "struct OptimismPortal2.<PERSON>venWithdrawal memory"}}, "id": 87858, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberName": "disputeGameProxy", "nodeType": "MemberAccess", "referencedDeclaration": 87145, "src": "21267:33:135", "typeDescriptions": {"typeIdentifier": "t_contract$_IDisputeGame_$100327", "typeString": "contract IDisputeGame"}}, "nodeType": "VariableDeclarationStatement", "src": "21235:65:135"}, {"expression": {"arguments": [{"id": 87864, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "21372:39:135", "subExpression": {"baseExpression": {"id": 87861, "name": "disputeGameBlacklist", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87205, "src": "21373:20:135", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_contract$_IDisputeGame_$100327_$_t_bool_$", "typeString": "mapping(contract IDisputeGame => bool)"}}, "id": 87863, "indexExpression": {"id": 87862, "name": "disputeGameProxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87856, "src": "21394:16:135", "typeDescriptions": {"typeIdentifier": "t_contract$_IDisputeGame_$100327", "typeString": "contract IDisputeGame"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "21373:38:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4f7074696d69736d506f7274616c3a20646973707574652067616d6520686173206265656e20626c61636b6c6973746564", "id": 87865, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "21413:51:135", "typeDescriptions": {"typeIdentifier": "t_stringliteral_73f1817c6693b1e67cebb729644f638bfff163fd990e09b18d9a753bee9d3156", "typeString": "literal_string \"OptimismPortal: dispute game has been blacklisted\""}, "value": "OptimismPortal: dispute game has been blacklisted"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_73f1817c6693b1e67cebb729644f638bfff163fd990e09b18d9a753bee9d3156", "typeString": "literal_string \"OptimismPortal: dispute game has been blacklisted\""}], "id": 87860, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "21364:7:135", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 87866, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "21364:101:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87867, "nodeType": "ExpressionStatement", "src": "21364:101:135"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "id": 87872, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 87869, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87847, "src": "21728:16:135", "typeDescriptions": {"typeIdentifier": "t_struct$_ProvenWithdrawal_$87148_memory_ptr", "typeString": "struct OptimismPortal2.<PERSON>venWithdrawal memory"}}, "id": 87870, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberName": "timestamp", "nodeType": "MemberAccess", "referencedDeclaration": 87147, "src": "21728:26:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"hexValue": "30", "id": 87871, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "21758:1:135", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "21728:31:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4f7074696d69736d506f7274616c3a207769746864726177616c20686173206e6f74206265656e2070726f76656e2062792070726f6f66207375626d6974746572206164647265737320796574", "id": 87873, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "21773:79:135", "typeDescriptions": {"typeIdentifier": "t_stringliteral_dff7e2322b891da5e795cf007265ba6491e079cdcc6285755ab2ef47d12c1b3e", "typeString": "literal_string \"OptimismPortal: withdrawal has not been proven by proof submitter address yet\""}, "value": "OptimismPortal: withdrawal has not been proven by proof submitter address yet"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_dff7e2322b891da5e795cf007265ba6491e079cdcc6285755ab2ef47d12c1b3e", "typeString": "literal_string \"OptimismPortal: withdrawal has not been proven by proof submitter address yet\""}], "id": 87868, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "21707:7:135", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 87874, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "21707:155:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87875, "nodeType": "ExpressionStatement", "src": "21707:155:135"}, {"assignments": [87877], "declarations": [{"constant": false, "id": 87877, "mutability": "mutable", "name": "createdAt", "nameLocation": "21880:9:135", "nodeType": "VariableDeclaration", "scope": 87955, "src": "21873:16:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "typeName": {"id": 87876, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "21873:6:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "visibility": "internal"}], "id": 87883, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 87878, "name": "disputeGameProxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87856, "src": "21892:16:135", "typeDescriptions": {"typeIdentifier": "t_contract$_IDisputeGame_$100327", "typeString": "contract IDisputeGame"}}, "id": 87879, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "createdAt", "nodeType": "MemberAccess", "referencedDeclaration": 100260, "src": "21892:26:135", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_userDefinedValueType$_Timestamp_$103261_$", "typeString": "function () view external returns (Timestamp)"}}, "id": 87880, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "21892:28:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_Timestamp_$103261", "typeString": "Timestamp"}}, "id": 87881, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "raw", "nodeType": "MemberAccess", "referencedDeclaration": 101124, "src": "21892:32:135", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_userDefinedValueType$_Timestamp_$103261_$returns$_t_uint64_$bound_to$_t_userDefinedValueType$_Timestamp_$103261_$", "typeString": "function (Timestamp) pure returns (uint64)"}}, "id": 87882, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "21892:34:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "VariableDeclarationStatement", "src": "21873:53:135"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "id": 87888, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 87885, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87847, "src": "22211:16:135", "typeDescriptions": {"typeIdentifier": "t_struct$_ProvenWithdrawal_$87148_memory_ptr", "typeString": "struct OptimismPortal2.<PERSON>venWithdrawal memory"}}, "id": 87886, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberName": "timestamp", "nodeType": "MemberAccess", "referencedDeclaration": 87147, "src": "22211:26:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"id": 87887, "name": "createdAt", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87877, "src": "22240:9:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "src": "22211:38:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4f7074696d69736d506f7274616c3a207769746864726177616c2074696d657374616d70206c657373207468616e20646973707574652067616d65206372656174696f6e2074696d657374616d70", "id": 87889, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "22263:80:135", "typeDescriptions": {"typeIdentifier": "t_stringliteral_0ad74f1e06ee42b3b76dc1e11cd4cd398b1f9faab8a48965612e5077366f3ac5", "typeString": "literal_string \"OptimismPortal: withdrawal timestamp less than dispute game creation timestamp\""}, "value": "OptimismPortal: withdrawal timestamp less than dispute game creation timestamp"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_0ad74f1e06ee42b3b76dc1e11cd4cd398b1f9faab8a48965612e5077366f3ac5", "typeString": "literal_string \"OptimismPortal: withdrawal timestamp less than dispute game creation timestamp\""}], "id": 87884, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "22190:7:135", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 87890, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "22190:163:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87891, "nodeType": "ExpressionStatement", "src": "22190:163:135"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 87899, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 87897, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 87893, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "22485:5:135", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 87894, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "timestamp", "nodeType": "MemberAccess", "src": "22485:15:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"expression": {"id": 87895, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87847, "src": "22503:16:135", "typeDescriptions": {"typeIdentifier": "t_struct$_ProvenWithdrawal_$87148_memory_ptr", "typeString": "struct OptimismPortal2.<PERSON>venWithdrawal memory"}}, "id": 87896, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberName": "timestamp", "nodeType": "MemberAccess", "referencedDeclaration": 87147, "src": "22503:26:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "src": "22485:44:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"id": 87898, "name": "PROOF_MATURITY_DELAY_SECONDS", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87151, "src": "22532:28:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "22485:75:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4f7074696d69736d506f7274616c3a2070726f76656e207769746864726177616c20686173206e6f74206d61747572656420796574", "id": 87900, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "22574:55:135", "typeDescriptions": {"typeIdentifier": "t_stringliteral_76db07ababbe7ead3930082886fa1efd5937fe1ef0c82ee1c6b5f5e6f3c5b440", "typeString": "literal_string \"OptimismPortal: proven withdrawal has not matured yet\""}, "value": "OptimismPortal: proven withdrawal has not matured yet"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_76db07ababbe7ead3930082886fa1efd5937fe1ef0c82ee1c6b5f5e6f3c5b440", "typeString": "literal_string \"OptimismPortal: proven withdrawal has not matured yet\""}], "id": 87892, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "22464:7:135", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 87901, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "22464:175:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87902, "nodeType": "ExpressionStatement", "src": "22464:175:135"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_enum$_GameStatus_$103277", "typeString": "enum GameStatus"}, "id": 87909, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 87904, "name": "disputeGameProxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87856, "src": "22943:16:135", "typeDescriptions": {"typeIdentifier": "t_contract$_IDisputeGame_$100327", "typeString": "contract IDisputeGame"}}, "id": 87905, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "status", "nodeType": "MemberAccess", "referencedDeclaration": 100274, "src": "22943:23:135", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_enum$_GameStatus_$103277_$", "typeString": "function () view external returns (enum GameStatus)"}}, "id": 87906, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "22943:25:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_enum$_GameStatus_$103277", "typeString": "enum GameStatus"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"id": 87907, "name": "GameStatus", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103277, "src": "22972:10:135", "typeDescriptions": {"typeIdentifier": "t_type$_t_enum$_GameStatus_$103277_$", "typeString": "type(enum GameStatus)"}}, "id": 87908, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberName": "DEFENDER_WINS", "nodeType": "MemberAccess", "referencedDeclaration": 103276, "src": "22972:24:135", "typeDescriptions": {"typeIdentifier": "t_enum$_GameStatus_$103277", "typeString": "enum GameStatus"}}, "src": "22943:53:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4f7074696d69736d506f7274616c3a206f75747075742070726f706f73616c20686173206e6f74206265656e2076616c696461746564", "id": 87910, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "23010:56:135", "typeDescriptions": {"typeIdentifier": "t_stringliteral_6a59e1f27f0a2f1f7f0bcad40a1f45d3cc032caa0d85e86ecaf6cb415c3f90fc", "typeString": "literal_string \"OptimismPortal: output proposal has not been validated\""}, "value": "OptimismPortal: output proposal has not been validated"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_6a59e1f27f0a2f1f7f0bcad40a1f45d3cc032caa0d85e86ecaf6cb415c3f90fc", "typeString": "literal_string \"OptimismPortal: output proposal has not been validated\""}], "id": 87903, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "22922:7:135", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 87911, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "22922:154:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87912, "nodeType": "ExpressionStatement", "src": "22922:154:135"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint32", "typeString": "uint32"}, "id": 87922, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 87914, "name": "disputeGameProxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87856, "src": "23349:16:135", "typeDescriptions": {"typeIdentifier": "t_contract$_IDisputeGame_$100327", "typeString": "contract IDisputeGame"}}, "id": 87915, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "gameType", "nodeType": "MemberAccess", "referencedDeclaration": 100281, "src": "23349:25:135", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_userDefinedValueType$_GameType_$103271_$", "typeString": "function () view external returns (GameType)"}}, "id": 87916, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "23349:27:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_GameType_$103271", "typeString": "GameType"}}, "id": 87917, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "raw", "nodeType": "MemberAccess", "referencedDeclaration": 101150, "src": "23349:31:135", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_userDefinedValueType$_GameType_$103271_$returns$_t_uint32_$bound_to$_t_userDefinedValueType$_GameType_$103271_$", "typeString": "function (GameType) pure returns (uint32)"}}, "id": 87918, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "23349:33:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 87919, "name": "respectedGameType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87209, "src": "23386:17:135", "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_GameType_$103271", "typeString": "GameType"}}, "id": 87920, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "raw", "nodeType": "MemberAccess", "referencedDeclaration": 101150, "src": "23386:21:135", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_userDefinedValueType$_GameType_$103271_$returns$_t_uint32_$bound_to$_t_userDefinedValueType$_GameType_$103271_$", "typeString": "function (GameType) pure returns (uint32)"}}, "id": 87921, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "23386:23:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "src": "23349:60:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4f7074696d69736d506f7274616c3a20696e76616c69642067616d652074797065", "id": 87923, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "23411:35:135", "typeDescriptions": {"typeIdentifier": "t_stringliteral_ea6e52a7a06be8d460d58a9fb591f5b7ad20643cdd834b0004aaeaa0647b1d4b", "typeString": "literal_string \"OptimismPortal: invalid game type\""}, "value": "OptimismPortal: invalid game type"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_ea6e52a7a06be8d460d58a9fb591f5b7ad20643cdd834b0004aaeaa0647b1d4b", "typeString": "literal_string \"OptimismPortal: invalid game type\""}], "id": 87913, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "23341:7:135", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 87924, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "23341:106:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87925, "nodeType": "ExpressionStatement", "src": "23341:106:135"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "id": 87929, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 87927, "name": "createdAt", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87877, "src": "23709:9:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"id": 87928, "name": "respectedGameTypeUpdatedAt", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87212, "src": "23722:26:135", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "src": "23709:39:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4f7074696d69736d506f7274616c3a20646973707574652067616d652063726561746564206265666f7265207265737065637465642067616d652074797065207761732075706461746564", "id": 87930, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "23762:77:135", "typeDescriptions": {"typeIdentifier": "t_stringliteral_eb316f1f3803f121f540c3c08dac6b170256917a9481e6e8393a29885b3a291f", "typeString": "literal_string \"OptimismPortal: dispute game created before respected game type was updated\""}, "value": "OptimismPortal: dispute game created before respected game type was updated"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_eb316f1f3803f121f540c3c08dac6b170256917a9481e6e8393a29885b3a291f", "typeString": "literal_string \"OptimismPortal: dispute game created before respected game type was updated\""}], "id": 87926, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "23688:7:135", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 87931, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "23688:161:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87932, "nodeType": "ExpressionStatement", "src": "23688:161:135"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 87943, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 87941, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 87934, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "24166:5:135", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 87935, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "timestamp", "nodeType": "MemberAccess", "src": "24166:15:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 87936, "name": "disputeGameProxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87856, "src": "24184:16:135", "typeDescriptions": {"typeIdentifier": "t_contract$_IDisputeGame_$100327", "typeString": "contract IDisputeGame"}}, "id": 87937, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "resolvedAt", "nodeType": "MemberAccess", "referencedDeclaration": 100267, "src": "24184:27:135", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_userDefinedValueType$_Timestamp_$103261_$", "typeString": "function () view external returns (Timestamp)"}}, "id": 87938, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "24184:29:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_userDefinedValueType$_Timestamp_$103261", "typeString": "Timestamp"}}, "id": 87939, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "raw", "nodeType": "MemberAccess", "referencedDeclaration": 101124, "src": "24184:33:135", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_userDefinedValueType$_Timestamp_$103261_$returns$_t_uint64_$bound_to$_t_userDefinedValueType$_Timestamp_$103261_$", "typeString": "function (Timestamp) pure returns (uint64)"}}, "id": 87940, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "24184:35:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "src": "24166:53:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"id": 87942, "name": "DISPUTE_GAME_FINALITY_DELAY_SECONDS", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87154, "src": "24222:35:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "24166:91:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4f7074696d69736d506f7274616c3a206f75747075742070726f706f73616c20696e206169722d676170", "id": 87944, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "24271:44:135", "typeDescriptions": {"typeIdentifier": "t_stringliteral_2a9b71e2152e178b3e39fef8c45fff793ac6b1f468eb7fbc612e0d564625c10f", "typeString": "literal_string \"OptimismPortal: output proposal in air-gap\""}, "value": "OptimismPortal: output proposal in air-gap"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_2a9b71e2152e178b3e39fef8c45fff793ac6b1f468eb7fbc612e0d564625c10f", "typeString": "literal_string \"OptimismPortal: output proposal in air-gap\""}], "id": 87933, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "24145:7:135", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 87945, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "24145:180:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87946, "nodeType": "ExpressionStatement", "src": "24145:180:135"}, {"expression": {"arguments": [{"id": 87951, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "24441:38:135", "subExpression": {"baseExpression": {"id": 87948, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87170, "src": "24442:20:135", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_bool_$", "typeString": "mapping(bytes32 => bool)"}}, "id": 87950, "indexExpression": {"id": 87949, "name": "_withdrawalHash", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87840, "src": "24463:15:135", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "24442:37:135", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4f7074696d69736d506f7274616c3a207769746864726177616c2068617320616c7265616479206265656e2066696e616c697a6564", "id": 87952, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "24481:55:135", "typeDescriptions": {"typeIdentifier": "t_stringliteral_2a1157cbf4171a399f26106a5211324151853c78d2faca1fb1d3acbf755aa485", "typeString": "literal_string \"OptimismPortal: withdrawal has already been finalized\""}, "value": "OptimismPortal: withdrawal has already been finalized"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_2a1157cbf4171a399f26106a5211324151853c78d2faca1fb1d3acbf755aa485", "typeString": "literal_string \"OptimismPortal: withdrawal has already been finalized\""}], "id": 87947, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "24433:7:135", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 87953, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "24433:104:135", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87954, "nodeType": "ExpressionStatement", "src": "24433:104:135"}]}, "documentation": {"id": 87838, "nodeType": "StructuredDocumentation", "src": "20715:314:135", "text": "@notice Checks if a withdrawal can be finalized. This function will revert if the withdrawal cannot be\n         finalized, and otherwise has no side-effects.\n @param _withdrawalHash Hash of the withdrawal to check.\n @param _proofSubmitter The submitter of the proof for the withdrawal hash"}, "functionSelector": "71c1566e", "implemented": true, "kind": "function", "modifiers": [], "name": "checkWithdrawal", "nameLocation": "21043:15:135", "parameters": {"id": 87843, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 87840, "mutability": "mutable", "name": "_withdrawalHash", "nameLocation": "21067:15:135", "nodeType": "VariableDeclaration", "scope": 87956, "src": "21059:23:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 87839, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "21059:7:135", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 87842, "mutability": "mutable", "name": "_proofSubmitter", "nameLocation": "21092:15:135", "nodeType": "VariableDeclaration", "scope": 87956, "src": "21084:23:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 87841, "name": "address", "nodeType": "ElementaryTypeName", "src": "21084:7:135", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "21058:50:135"}, "returnParameters": {"id": 87844, "nodeType": "ParameterList", "parameters": [], "src": "21121:0:135"}, "scope": 87971, "stateMutability": "view", "virtual": false, "visibility": "public"}, {"id": 87970, "nodeType": "FunctionDefinition", "src": "24767:148:135", "nodes": [], "body": {"id": 87969, "nodeType": "Block", "src": "24852:63:135", "nodes": [], "statements": [{"expression": {"expression": {"baseExpression": {"id": 87964, "name": "proofSubmitters", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87218, "src": "24869:15:135", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_array$_t_address_$dyn_storage_$", "typeString": "mapping(bytes32 => address[] storage ref)"}}, "id": 87966, "indexExpression": {"id": 87965, "name": "_withdrawalHash", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87959, "src": "24885:15:135", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "24869:32:135", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage", "typeString": "address[] storage ref"}}, "id": 87967, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "length", "nodeType": "MemberAccess", "src": "24869:39:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 87963, "id": 87968, "nodeType": "Return", "src": "24862:46:135"}]}, "documentation": {"id": 87957, "nodeType": "StructuredDocumentation", "src": "24550:212:135", "text": "@notice External getter for the number of proof submitters for a withdrawal hash.\n @param _withdrawalHash Hash of the withdrawal.\n @return The number of proof submitters for the withdrawal hash."}, "functionSelector": "513747ab", "implemented": true, "kind": "function", "modifiers": [], "name": "numProofSubmitters", "nameLocation": "24776:18:135", "parameters": {"id": 87960, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 87959, "mutability": "mutable", "name": "_withdrawalHash", "nameLocation": "24803:15:135", "nodeType": "VariableDeclaration", "scope": 87970, "src": "24795:23:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 87958, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "24795:7:135", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "24794:25:135"}, "returnParameters": {"id": 87963, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 87962, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 87970, "src": "24843:7:135", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 87961, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "24843:7:135", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "24842:9:135"}, "scope": 87971, "stateMutability": "view", "virtual": false, "visibility": "external"}], "abstract": false, "baseContracts": [{"baseName": {"id": 87137, "name": "Initializable", "nodeType": "IdentifierPath", "referencedDeclaration": 49678, "src": "1338:13:135"}, "id": 87138, "nodeType": "InheritanceSpecifier", "src": "1338:13:135"}, {"baseName": {"id": 87139, "name": "ResourceMetering", "nodeType": "IdentifierPath", "referencedDeclaration": 88581, "src": "1353:16:135"}, "id": 87140, "nodeType": "InheritanceSpecifier", "src": "1353:16:135"}, {"baseName": {"id": 87141, "name": "ISemver", "nodeType": "IdentifierPath", "referencedDeclaration": 109417, "src": "1371:7:135"}, "id": 87142, "nodeType": "InheritanceSpecifier", "src": "1371:7:135"}], "canonicalName": "OptimismPortal2", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 87136, "nodeType": "StructuredDocumentation", "src": "971:339:135", "text": "@custom:proxied\n @title OptimismPortal2\n @notice The OptimismPortal is a low-level contract responsible for passing messages between L1\n         and L2. Messages sent directly to the OptimismPortal have no form of replayability.\n         Users are encouraged to use the L1CrossDomainMessenger for a higher-level interface."}, "fullyImplemented": true, "linearizedBaseContracts": [87971, 109417, 88581, 49678], "name": "OptimismPortal2", "nameLocation": "1319:15:135", "scope": 87972, "usedErrors": [88238, 103969, 103972, 103975, 103987, 103990, 103993]}], "license": "MIT"}, "id": 135}