{"abi": [{"type": "constructor", "inputs": [{"name": "_owner", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "addressManager", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract AddressManager"}], "stateMutability": "view"}, {"type": "function", "name": "changeProxyAdmin", "inputs": [{"name": "_proxy", "type": "address", "internalType": "address payable"}, {"name": "_newAdmin", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getProxyAdmin", "inputs": [{"name": "_proxy", "type": "address", "internalType": "address payable"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getProxyImplementation", "inputs": [{"name": "_proxy", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "implementationName", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "isUpgrading", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "proxyType", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint8", "internalType": "enum ProxyAdmin.ProxyType"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "_name", "type": "string", "internalType": "string"}, {"name": "_address", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setAddressManager", "inputs": [{"name": "_address", "type": "address", "internalType": "contract AddressManager"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setImplementationName", "inputs": [{"name": "_address", "type": "address", "internalType": "address"}, {"name": "_name", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setProxyType", "inputs": [{"name": "_address", "type": "address", "internalType": "address"}, {"name": "_type", "type": "uint8", "internalType": "enum ProxyAdmin.ProxyType"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setUpgrading", "inputs": [{"name": "_upgrading", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgrade", "inputs": [{"name": "_proxy", "type": "address", "internalType": "address payable"}, {"name": "_implementation", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeAndCall", "inputs": [{"name": "_proxy", "type": "address", "internalType": "address payable"}, {"name": "_implementation", "type": "address", "internalType": "address"}, {"name": "_data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}], "bytecode": {"object": "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", "sourceMap": "1241:8036:234:-:0;;;2494:81;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;936:32:40;719:10:60;936:18:40;:32::i;:::-;2542:26:234::1;2561:6:::0;2542:18:::1;:26::i;:::-;2494:81:::0;1241:8036;;2433:187:40;2506:16;2525:6;;-1:-1:-1;;;;;2541:17:40;;;-1:-1:-1;;;;;;2541:17:40;;;;;;2573:40;;2525:6;;;;;;;2573:40;;2506:16;2573:40;2496:124;2433:187;:::o;14:290:357:-;84:6;137:2;125:9;116:7;112:23;108:32;105:52;;;153:1;150;143:12;105:52;179:16;;-1:-1:-1;;;;;224:31:357;;214:42;;204:70;;270:1;267;260:12;204:70;293:5;14:290;-1:-1:-1;;;14:290:357:o;:::-;1241:8036:234;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "1241:8036:234:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3571:113;;;;;;;;;;-1:-1:-1;3571:113:234;;;;;:::i;:::-;;:::i;:::-;;4430:97;;;;;;;;;;-1:-1:-1;4430:97:234;;;;;:::i;:::-;;:::i;5236:569::-;;;;;;;;;;-1:-1:-1;5236:569:234;;;;;:::i;:::-;;:::i;:::-;;;1204:42:357;1192:55;;;1174:74;;1162:2;1147:18;5236:569:234;;;;;;;;2087:52;;;;;;;;;;-1:-1:-1;2087:52:234;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;2273:36::-;;;;;;;;;;-1:-1:-1;2273:36:234;;;;;;;;1760:46;;;;;;;;;;-1:-1:-1;1760:46:234;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;:::i;1831:101:40:-;;;;;;;;;;;;;:::i;6689:531:234:-;;;;;;;;;;-1:-1:-1;6689:531:234;;;;;:::i;:::-;;:::i;3219:142::-;;;;;;;;;;-1:-1:-1;3219:142:234;;;;;:::i;:::-;;:::i;2796:120::-;;;;;;;;;;-1:-1:-1;2796:120:234;;;;;:::i;:::-;;:::i;1201:85:40:-;;;;;;;;;;-1:-1:-1;1247:7:40;1273:6;;;1201:85;;8644:631:234;;;;;;:::i;:::-;;:::i;7423:816::-;;;;;;;;;;-1:-1:-1;7423:816:234;;;;;:::i;:::-;;:::i;4126:137::-;;;;;;;;;;-1:-1:-1;4126:137:234;;;;;:::i;:::-;;:::i;4941:85::-;;;;;;;;;;-1:-1:-1;5010:9:234;;;;;;;4941:85;;7028:14:357;;7021:22;7003:41;;6991:2;6976:18;4941:85:234;6863:187:357;2081:198:40;;;;;;;;;;-1:-1:-1;2081:198:40;;;;;:::i;:::-;;:::i;5988:519:234:-;;;;;;;;;;-1:-1:-1;5988:519:234;;;;;:::i;:::-;;:::i;3571:113::-;1094:13:40;:11;:13::i;:::-;3652:14:234::1;:25:::0;;;::::1;;::::0;;;::::1;::::0;;;::::1;::::0;;3571:113::o;4430:97::-;1094:13:40;:11;:13::i;:::-;4498:9:234::1;:22:::0;;;::::1;;::::0;::::1;::::0;;;::::1;::::0;;;::::1;::::0;;4430:97::o;5236:569::-;5344:17;;;5307:7;5344:17;;;:9;:17;;;;;;;;5307:7;5375:5;:26;;;;;;;;:::i;:::-;;5371:428;;5444:6;5424:42;;;:44;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5417:51;5236:569;-1:-1:-1;;;5236:569:234:o;5371:428::-;5498:20;5489:5;:29;;;;;;;;:::i;:::-;;5485:314;;5566:6;5541:50;;;:52;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5485:314;5623:18;5614:5;:27;;;;;;;;:::i;:::-;;5610:189;;5664:14;;;5690:26;;;5664:14;5690:26;;;:18;:26;;;;;;;5664:53;;;;;:14;;;;;:25;;:53;;5690:26;5664:53;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;5610:189;5748:40;;;;;9399:2:357;5748:40:234;;;9381:21:357;9438:2;9418:18;;;9411:30;9477:32;9457:18;;;9450:60;9527:18;;5748:40:234;;;;;;;;5610:189;5316:489;5236:569;;;:::o;2087:52::-;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;1831:101:40:-;1094:13;:11;:13::i;:::-;1895:30:::1;1922:1;1895:18;:30::i;:::-;1831:101::o:0;6689:531:234:-;1094:13:40;:11;:13::i;:::-;6805:17:234::1;::::0;::::1;6787:15;6805:17:::0;;;:9:::1;:17;::::0;;;;;::::1;;::::0;6836:5:::1;:26;;;;;;;;:::i;:::-;::::0;6832:382:::1;;6878:36;::::0;;;;:25:::1;1192:55:357::0;;;6878:36:234::1;::::0;::::1;1174:74:357::0;6878:25:234;::::1;::::0;::::1;::::0;1147:18:357;;6878:36:234::1;;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;::::0;::::1;;;;;;;;;6777:443;6689:531:::0;;:::o;6832:382::-:1;6944:20;6935:5;:29;;;;;;;;:::i;:::-;::::0;6931:283:::1;;6980:45;::::0;;;;:34:::1;1192:55:357::0;;;6980:45:234::1;::::0;::::1;1174:74:357::0;6980:34:234;::::1;::::0;::::1;::::0;1147:18:357;;6980:45:234::1;1028:226:357::0;6931:283:234::1;7055:18;7046:5;:27;;;;;;;;:::i;:::-;::::0;7042:172:::1;;7089:14;::::0;:43:::1;::::0;;;;:14:::1;1192:55:357::0;;;7089:43:234::1;::::0;::::1;1174:74:357::0;7089:14:234;;::::1;::::0;:32:::1;::::0;1147:18:357;;7089:43:234::1;1028:226:357::0;7042:172:234::1;6777:443;6689:531:::0;;:::o;3219:142::-;1094:13:40;:11;:13::i;:::-;3318:28:234::1;::::0;::::1;;::::0;;;:18:::1;:28;::::0;;;;:36:::1;3349:5:::0;3318:28;:36:::1;:::i;2796:120::-:0;1094:13:40;:11;:13::i;:::-;2882:19:234::1;::::0;::::1;;::::0;;;:9:::1;:19;::::0;;;;;;;:27;;2904:5;;2882:27;;;::::1;::::0;2904:5;2882:27:::1;::::0;::::1;;;;;;:::i;:::-;;;;;;2796:120:::0;;:::o;8644:631::-;1094:13:40;:11;:13::i;:::-;8850:17:234::1;::::0;::::1;8832:15;8850:17:::0;;;:9:::1;:17;::::0;;;;;::::1;;::::0;8881:5:::1;:26;;;;;;;;:::i;:::-;::::0;8877:392:::1;;8923:74;::::0;;;;:30:::1;::::0;::::1;::::0;::::1;::::0;8962:9:::1;::::0;8923:74:::1;::::0;8974:15;;8991:5;;8923:74:::1;;;:::i;:::-;;;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;::::0;;::::1;::::0;::::1;::::0;::::1;;::::0;::::1;::::0;;;::::1;::::0;::::1;:::i;:::-;;8877:392;;;9076:32;9084:6;9092:15;9076:7;:32::i;:::-;9123:12;9140:6;:11;;9160:9;9172:5;9140:38;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;9122:56;;;9200:7;9192:66;;;::::0;::::1;::::0;;13277:2:357;9192:66:234::1;::::0;::::1;13259:21:357::0;13316:2;13296:18;;;13289:30;13355:34;13335:18;;;13328:62;13426:16;13406:18;;;13399:44;13460:19;;9192:66:234::1;13075:410:357::0;9192:66:234::1;9014:255;8877:392;8822:453;8644:631:::0;;;:::o;7423:816::-;1094:13:40;:11;:13::i;:::-;7534:17:234::1;::::0;::::1;7516:15;7534:17:::0;;;:9:::1;:17;::::0;;;;;::::1;;::::0;7565:5:::1;:26;;;;;;;;:::i;:::-;::::0;7561:672:::1;;7607:40;::::0;;;;:23:::1;1192:55:357::0;;;7607:40:234::1;::::0;::::1;1174:74:357::0;7607:23:234;::::1;::::0;::::1;::::0;1147:18:357;;7607:40:234::1;1028:226:357::0;7561:672:234::1;7677:20;7668:5;:29;;;;;;;;:::i;:::-;::::0;7664:569:::1;;7713:150;::::0;;;;1614:66:192::1;7713:150:234;::::0;::::1;13664:25:357::0;7713:36:234::1;7815:33:::0;;::::1;13705:18:357::0;;;13698:34;7713:36:234;::::1;::::0;::::1;::::0;13637:18:357;;7713:150:234::1;13490:248:357::0;7664:569:234::1;7893:18;7884:5;:27;;;;;;;;:::i;:::-;::::0;7880:353:::1;;7948:26;::::0;::::1;7927:18;7948:26:::0;;;:18:::1;:26;::::0;;;;7927:47;;::::1;::::0;::::1;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1::0;;7988:14:234::1;::::0;:48:::1;::::0;;;;7927:47;;-1:-1:-1;7988:14:234::1;;::::0;:25:::1;::::0;-1:-1:-1;7988:48:234::1;::::0;-1:-1:-1;7927:47:234;;-1:-1:-1;8020:15:234;;7988:48:::1;;;:::i;:::-;;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;::::0;::::1;;;;;;;;;7913:134;6777:443;6689:531:::0;;:::o;7880:353::-:1;8209:13;;:::i;4126:137::-:0;1094:13:40;:11;:13::i;:::-;4214:14:234::1;::::0;:42:::1;::::0;;;;:14:::1;::::0;;::::1;::::0;:25:::1;::::0;:42:::1;::::0;4240:5;;4247:8;;4214:42:::1;;;:::i;:::-;;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;::::0;::::1;;;;;;;;;4126:137:::0;;:::o;2081:198:40:-;1094:13;:11;:13::i;:::-;2169:22:::1;::::0;::::1;2161:73;;;::::0;::::1;::::0;;14479:2:357;2161:73:40::1;::::0;::::1;14461:21:357::0;14518:2;14498:18;;;14491:30;14557:34;14537:18;;;14530:62;14628:8;14608:18;;;14601:36;14654:19;;2161:73:40::1;14277:402:357::0;2161:73:40::1;2244:28;2263:8;2244:18;:28::i;:::-;2081:198:::0;:::o;5988:519:234:-;6095:17;;;6058:7;6095:17;;;:9;:17;;;;;;;;6058:7;6126:5;:26;;;;;;;;:::i;:::-;;6122:379;;6195:6;6175:33;;;:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6122:379;6240:20;6231:5;:29;;;;;;;;:::i;:::-;;6227:274;;6308:6;6283:41;;;:43;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6227:274;6356:18;6347:5;:27;;;;;;;;:::i;:::-;;6343:158;;6397:14;;;;;;;;;;;:20;;;:22;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1359:130:40;1247:7;1273:6;1422:23;1273:6;719:10:60;1422:23:40;1414:68;;;;;;;14886:2:357;1414:68:40;;;14868:21:357;;;14905:18;;;14898:30;14964:34;14944:18;;;14937:62;15016:18;;1414:68:40;14684:356:357;2433:187:40;2506:16;2525:6;;;2541:17;;;;;;;;;;2573:40;;2525:6;;;;;;;2573:40;;2506:16;2573:40;2496:124;2433:187;:::o;14:170:357:-;116:42;109:5;105:54;98:5;95:65;85:93;;174:1;171;164:12;189:288;273:6;326:2;314:9;305:7;301:23;297:32;294:52;;;342:1;339;332:12;294:52;381:9;368:23;400:47;441:5;400:47;:::i;482:273::-;538:6;591:2;579:9;570:7;566:23;562:32;559:52;;;607:1;604;597:12;559:52;646:9;633:23;699:5;692:13;685:21;678:5;675:32;665:60;;721:1;718;711:12;1259:258;1331:1;1341:113;1355:6;1352:1;1349:13;1341:113;;;1431:11;;;1425:18;1412:11;;;1405:39;1377:2;1370:10;1341:113;;;1472:6;1469:1;1466:13;1463:48;;;-1:-1:-1;;1507:1:357;1489:16;;1482:27;1259:258::o;1522:317::-;1564:3;1602:5;1596:12;1629:6;1624:3;1617:19;1645:63;1701:6;1694:4;1689:3;1685:14;1678:4;1671:5;1667:16;1645:63;:::i;:::-;1753:2;1741:15;1758:66;1737:88;1728:98;;;;1828:4;1724:109;;1522:317;-1:-1:-1;;1522:317:357:o;1844:220::-;1993:2;1982:9;1975:21;1956:4;2013:45;2054:2;2043:9;2039:18;2031:6;2013:45;:::i;2325:184::-;2377:77;2374:1;2367:88;2474:4;2471:1;2464:15;2498:4;2495:1;2488:15;2514:401;2662:2;2647:18;;2695:1;2684:13;;2674:201;;2731:77;2728:1;2721:88;2832:4;2829:1;2822:15;2860:4;2857:1;2850:15;2674:201;2884:25;;;2514:401;:::o;2920:428::-;2996:6;3004;3057:2;3045:9;3036:7;3032:23;3028:32;3025:52;;;3073:1;3070;3063:12;3025:52;3112:9;3099:23;3131:47;3172:5;3131:47;:::i;:::-;3197:5;-1:-1:-1;3254:2:357;3239:18;;3226:32;3267:49;3226:32;3267:49;:::i;:::-;3335:7;3325:17;;;2920:428;;;;;:::o;3353:184::-;3405:77;3402:1;3395:88;3502:4;3499:1;3492:15;3526:4;3523:1;3516:15;3542:334;3613:2;3607:9;3669:2;3659:13;;3674:66;3655:86;3643:99;;3772:18;3757:34;;3793:22;;;3754:62;3751:88;;;3819:18;;:::i;:::-;3855:2;3848:22;3542:334;;-1:-1:-1;3542:334:357:o;3881:246::-;3930:4;3963:18;3955:6;3952:30;3949:56;;;3985:18;;:::i;:::-;-1:-1:-1;4042:2:357;4030:15;4047:66;4026:88;4116:4;4022:99;;3881:246::o;4132:338::-;4197:5;4226:53;4242:36;4271:6;4242:36;:::i;:::-;4226:53;:::i;:::-;4217:62;;4302:6;4295:5;4288:21;4342:3;4333:6;4328:3;4324:16;4321:25;4318:45;;;4359:1;4356;4349:12;4318:45;4408:6;4403:3;4396:4;4389:5;4385:16;4372:43;4462:1;4455:4;4446:6;4439:5;4435:18;4431:29;4424:40;4132:338;;;;;:::o;4475:222::-;4518:5;4571:3;4564:4;4556:6;4552:17;4548:27;4538:55;;4589:1;4586;4579:12;4538:55;4611:80;4687:3;4678:6;4665:20;4658:4;4650:6;4646:17;4611:80;:::i;4702:473::-;4780:6;4788;4841:2;4829:9;4820:7;4816:23;4812:32;4809:52;;;4857:1;4854;4847:12;4809:52;4896:9;4883:23;4915:47;4956:5;4915:47;:::i;:::-;4981:5;-1:-1:-1;5037:2:357;5022:18;;5009:32;5064:18;5053:30;;5050:50;;;5096:1;5093;5086:12;5050:50;5119;5161:7;5152:6;5141:9;5137:22;5119:50;:::i;:::-;5109:60;;;4702:473;;;;;:::o;5180:429::-;5264:6;5272;5325:2;5313:9;5304:7;5300:23;5296:32;5293:52;;;5341:1;5338;5331:12;5293:52;5380:9;5367:23;5399:47;5440:5;5399:47;:::i;:::-;5465:5;-1:-1:-1;5522:2:357;5507:18;;5494:32;5557:1;5545:14;;5535:42;;5573:1;5570;5563:12;5614:766;5708:6;5716;5724;5777:2;5765:9;5756:7;5752:23;5748:32;5745:52;;;5793:1;5790;5783:12;5745:52;5832:9;5819:23;5851:47;5892:5;5851:47;:::i;:::-;5917:5;-1:-1:-1;5974:2:357;5959:18;;5946:32;5987:49;5946:32;5987:49;:::i;:::-;6055:7;-1:-1:-1;6113:2:357;6098:18;;6085:32;6140:18;6129:30;;6126:50;;;6172:1;6169;6162:12;6126:50;6195:22;;6248:4;6240:13;;6236:27;-1:-1:-1;6226:55:357;;6277:1;6274;6267:12;6226:55;6300:74;6366:7;6361:2;6348:16;6343:2;6339;6335:11;6300:74;:::i;:::-;6290:84;;;5614:766;;;;;:::o;6385:473::-;6463:6;6471;6524:2;6512:9;6503:7;6499:23;6495:32;6492:52;;;6540:1;6537;6530:12;6492:52;6580:9;6567:23;6613:18;6605:6;6602:30;6599:50;;;6645:1;6642;6635:12;6599:50;6668;6710:7;6701:6;6690:9;6686:22;6668:50;:::i;:::-;6658:60;;;6768:2;6757:9;6753:18;6740:32;6781:47;6822:5;6781:47;:::i;7331:267::-;7401:6;7454:2;7442:9;7433:7;7429:23;7425:32;7422:52;;;7470:1;7467;7460:12;7422:52;7502:9;7496:16;7521:47;7562:5;7521:47;:::i;7603:437::-;7682:1;7678:12;;;;7725;;;7746:61;;7800:4;7792:6;7788:17;7778:27;;7746:61;7853:2;7845:6;7842:14;7822:18;7819:38;7816:218;;7890:77;7887:1;7880:88;7991:4;7988:1;7981:15;8019:4;8016:1;8009:15;8171:1021;8280:4;8309:2;8338;8327:9;8320:21;8361:1;8394:6;8388:13;8424:36;8450:9;8424:36;:::i;:::-;8496:6;8491:2;8480:9;8476:18;8469:34;8522:2;8543:1;8575:2;8564:9;8560:18;8592:1;8587:216;;;;8817:1;8812:354;;;;8553:613;;8587:216;8650:66;8639:9;8635:82;8630:2;8619:9;8615:18;8608:110;8790:2;8778:6;8771:14;8764:22;8761:1;8757:30;8746:9;8742:46;8738:55;8731:62;;8587:216;;8812:354;8843:6;8840:1;8833:17;8891:2;8888:1;8878:16;8916:1;8930:180;8944:6;8941:1;8938:13;8930:180;;;9037:14;;9013:17;;;9009:26;;9002:50;9080:16;;;;8959:10;;8930:180;;;9134:17;;9130:26;;;-1:-1:-1;;8553:613:357;-1:-1:-1;9183:3:357;;8171:1021;-1:-1:-1;;;;;;;;8171:1021:357:o;9556:545::-;9658:2;9653:3;9650:11;9647:448;;;9694:1;9719:5;9715:2;9708:17;9764:4;9760:2;9750:19;9834:2;9822:10;9818:19;9815:1;9811:27;9805:4;9801:38;9870:4;9858:10;9855:20;9852:47;;;-1:-1:-1;9893:4:357;9852:47;9948:2;9943:3;9939:12;9936:1;9932:20;9926:4;9922:31;9912:41;;10003:82;10021:2;10014:5;10011:13;10003:82;;;10066:17;;;10047:1;10036:13;10003:82;;10337:1471;10463:3;10457:10;10490:18;10482:6;10479:30;10476:56;;;10512:18;;:::i;:::-;10541:97;10631:6;10591:38;10623:4;10617:11;10591:38;:::i;:::-;10585:4;10541:97;:::i;:::-;10693:4;;10757:2;10746:14;;10774:1;10769:782;;;;11595:1;11612:6;11609:89;;;-1:-1:-1;11664:19:357;;;11658:26;11609:89;10243:66;10234:1;10230:11;;;10226:84;10222:89;10212:100;10318:1;10314:11;;;10209:117;11711:81;;10739:1063;;10769:782;8118:1;8111:14;;;8155:4;8142:18;;10817:66;10805:79;;;10982:236;10996:7;10993:1;10990:14;10982:236;;;11085:19;;;11079:26;11064:42;;11177:27;;;;11145:1;11133:14;;;;11012:19;;10982:236;;;10986:3;11246:6;11237:7;11234:19;11231:261;;;11307:19;;;11301:26;11408:66;11390:1;11386:14;;;11402:3;11382:24;11378:97;11374:102;11359:118;11344:134;;11231:261;-1:-1:-1;;;;;11538:1:357;11522:14;;;11518:22;11505:36;;-1:-1:-1;10337:1471:357:o;11813:338::-;12000:42;11992:6;11988:55;11977:9;11970:74;12080:2;12075;12064:9;12060:18;12053:30;11951:4;12100:45;12141:2;12130:9;12126:18;12118:6;12100:45;:::i;:::-;12092:53;11813:338;-1:-1:-1;;;;11813:338:357:o;12156:635::-;12235:6;12288:2;12276:9;12267:7;12263:23;12259:32;12256:52;;;12304:1;12301;12294:12;12256:52;12337:9;12331:16;12370:18;12362:6;12359:30;12356:50;;;12402:1;12399;12392:12;12356:50;12425:22;;12478:4;12470:13;;12466:27;-1:-1:-1;12456:55:357;;12507:1;12504;12497:12;12456:55;12536:2;12530:9;12561:49;12577:32;12606:2;12577:32;:::i;12561:49::-;12633:2;12626:5;12619:17;12673:7;12668:2;12663;12659;12655:11;12651:20;12648:33;12645:53;;;12694:1;12691;12684:12;12645:53;12707:54;12758:2;12753;12746:5;12742:14;12737:2;12733;12729:11;12707:54;:::i;:::-;12780:5;12156:635;-1:-1:-1;;;;;12156:635:357:o;12796:274::-;12925:3;12963:6;12957:13;12979:53;13025:6;13020:3;13013:4;13005:6;13001:17;12979:53;:::i;:::-;13048:16;;;;;12796:274;-1:-1:-1;;12796:274:357:o;13743:340::-;13920:2;13909:9;13902:21;13883:4;13940:45;13981:2;13970:9;13966:18;13958:6;13940:45;:::i;:::-;13932:53;;14033:42;14025:6;14021:55;14016:2;14005:9;14001:18;13994:83;13743:340;;;;;:::o;14088:184::-;14140:77;14137:1;14130:88;14237:4;14234:1;14227:15;14261:4;14258:1;14251:15", "linkReferences": {}}, "methodIdentifiers": {"addressManager()": "3ab76e9f", "changeProxyAdmin(address,address)": "7eff275e", "getProxyAdmin(address)": "f3b7dead", "getProxyImplementation(address)": "204e1c7a", "implementationName(address)": "238181ae", "isUpgrading()": "b7947262", "owner()": "8da5cb5b", "proxyType(address)": "6bd9f516", "renounceOwnership()": "715018a6", "setAddress(string,address)": "9b2ea4bd", "setAddressManager(address)": "0652b57a", "setImplementationName(address,string)": "860f7cda", "setProxyType(address,uint8)": "8d52d4a0", "setUpgrading(bool)": "07c8f7b0", "transferOwnership(address)": "f2fde38b", "upgrade(address,address)": "99a88ec4", "upgradeAndCall(address,address,bytes)": "9623609d"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.15+commit.e14f2714\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_owner\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"addressManager\",\"outputs\":[{\"internalType\":\"contract AddressManager\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address payable\",\"name\":\"_proxy\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_newAdmin\",\"type\":\"address\"}],\"name\":\"changeProxyAdmin\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address payable\",\"name\":\"_proxy\",\"type\":\"address\"}],\"name\":\"getProxyAdmin\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_proxy\",\"type\":\"address\"}],\"name\":\"getProxyImplementation\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"implementationName\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"isUpgrading\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"proxyType\",\"outputs\":[{\"internalType\":\"enum ProxyAdmin.ProxyType\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"_name\",\"type\":\"string\"},{\"internalType\":\"address\",\"name\":\"_address\",\"type\":\"address\"}],\"name\":\"setAddress\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"contract AddressManager\",\"name\":\"_address\",\"type\":\"address\"}],\"name\":\"setAddressManager\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_address\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"_name\",\"type\":\"string\"}],\"name\":\"setImplementationName\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_address\",\"type\":\"address\"},{\"internalType\":\"enum ProxyAdmin.ProxyType\",\"name\":\"_type\",\"type\":\"uint8\"}],\"name\":\"setProxyType\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"_upgrading\",\"type\":\"bool\"}],\"name\":\"setUpgrading\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address payable\",\"name\":\"_proxy\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_implementation\",\"type\":\"address\"}],\"name\":\"upgrade\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address payable\",\"name\":\"_proxy\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_implementation\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"_data\",\"type\":\"bytes\"}],\"name\":\"upgradeAndCall\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"changeProxyAdmin(address,address)\":{\"params\":{\"_newAdmin\":\"Address of the new proxy admin.\",\"_proxy\":\"Address of the proxy to update.\"}},\"constructor\":{\"params\":{\"_owner\":\"Address of the initial owner of this contract.\"}},\"getProxyAdmin(address)\":{\"params\":{\"_proxy\":\"Address of the proxy to get the admin of.\"},\"returns\":{\"_0\":\"Address of the admin of the proxy.\"}},\"getProxyImplementation(address)\":{\"params\":{\"_proxy\":\"Address of the proxy to get the implementation of.\"},\"returns\":{\"_0\":\"Address of the implementation of the proxy.\"}},\"isUpgrading()\":{\"custom:legacy\":\"@notice Legacy function used to tell ChugSplashProxy contracts if an upgrade is happening.\",\"returns\":{\"_0\":\"Whether or not there is an upgrade going on. May not actually tell you whether an         upgrade is going on, since we don't currently plan to use this variable for anything         other than a legacy indicator to fix a UX bug in the ChugSplash proxy.\"}},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions anymore. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby removing any functionality that is only available to the owner.\"},\"setAddress(string,address)\":{\"custom:legacy\":\"@notice Set an address in the address manager. Since only the owner of the AddressManager         can directly modify addresses and the ProxyAdmin will own the AddressManager, this         gives the owner of the ProxyAdmin the ability to modify addresses directly.\",\"params\":{\"_address\":\"Address to attach to the given name.\",\"_name\":\"Name to set within the AddressManager.\"}},\"setAddressManager(address)\":{\"params\":{\"_address\":\"Address of the AddressManager.\"}},\"setImplementationName(address,string)\":{\"params\":{\"_address\":\"Address of the ResolvedDelegateProxy.\",\"_name\":\"Name of the implementation for the proxy.\"}},\"setProxyType(address,uint8)\":{\"params\":{\"_address\":\"Address of the proxy.\",\"_type\":\"Type of the proxy.\"}},\"setUpgrading(bool)\":{\"custom:legacy\":\"@notice Set the upgrading status for the Chugsplash proxy type.\",\"params\":{\"_upgrading\":\"Whether or not the system is upgrading.\"}},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"},\"upgrade(address,address)\":{\"params\":{\"_implementation\":\"Address of the new implementation address.\",\"_proxy\":\"Address of the proxy to upgrade.\"}},\"upgradeAndCall(address,address,bytes)\":{\"params\":{\"_data\":\"Data to trigger the new implementation with.\",\"_implementation\":\"Address of the new implementation address.\",\"_proxy\":\"Address of the proxy to upgrade.\"}}},\"title\":\"ProxyAdmin\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"addressManager()\":{\"notice\":\"The address of the address manager, this is required to manage the         ResolvedDelegateProxy type.\"},\"changeProxyAdmin(address,address)\":{\"notice\":\"Updates the admin of the given proxy address.\"},\"getProxyAdmin(address)\":{\"notice\":\"Returns the admin of the given proxy address.\"},\"getProxyImplementation(address)\":{\"notice\":\"Returns the implementation of the given proxy address.\"},\"implementationName(address)\":{\"notice\":\"A reverse mapping of addresses to names held in the AddressManager. This must be         manually kept up to date with changes in the AddressManager for this contract         to be able to work as an admin for the ResolvedDelegateProxy type.\"},\"proxyType(address)\":{\"notice\":\"A mapping of proxy types, used for backwards compatibility.\"},\"setAddressManager(address)\":{\"notice\":\"Set the address of the AddressManager. This is required to manage legacy         ResolvedDelegateProxy type proxy contracts.\"},\"setImplementationName(address,string)\":{\"notice\":\"Sets the implementation name for a given address. Only required for         ResolvedDelegateProxy type proxies that have an implementation name.\"},\"setProxyType(address,uint8)\":{\"notice\":\"Sets the proxy type for a given address. Only required for non-standard (legacy)         proxy types.\"},\"upgrade(address,address)\":{\"notice\":\"Changes a proxy's implementation contract.\"},\"upgradeAndCall(address,address,bytes)\":{\"notice\":\"Changes a proxy's implementation contract and delegatecalls the new implementation         with some given data. Useful for atomic upgrade-and-initialize calls.\"}},\"notice\":\"This is an auxiliary contract meant to be assigned as the admin of an ERC1967 Proxy,         based on the OpenZeppelin implementation. It has backwards compatibility logic to work         with the various types of proxies that have been deployed by Optimism in the past.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/universal/ProxyAdmin.sol\":\"ProxyAdmin\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":999999},\"remappings\":[\":@lib-keccak/=lib/lib-keccak/contracts/lib/\",\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":@rari-capital/solmate/=lib/solmate/\",\":@solady-test/=lib/lib-keccak/lib/solady/test/\",\":@solady/=lib/solady/src/\",\":ds-test/=lib/forge-std/lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":kontrol-cheatcodes/=lib/kontrol-cheatcodes/src/\",\":lib-keccak/=lib/lib-keccak/contracts/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":safe-contracts/=lib/safe-contracts/contracts/\",\":solady/=lib/solady/\",\":solmate/=lib/solmate/src/\"]},\"sources\":{\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xa94b34880e3c1b0b931662cb1c09e5dfa6662f31cba80e07c5ee71cd135c9673\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://40fb1b5102468f783961d0af743f91b9980cf66b50d1d12009f6bb1869cea4d2\",\"dweb:/ipfs/QmYqEbJML4jB1GHbzD4cUZDtJg5wVwNm3vDJq1GbyDus8y\"]},\"lib/openzeppelin-contracts/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0x2a21b14ff90012878752f230d3ffd5c3405e5938d06c97a7d89c0a64561d0d66\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3313a8f9bb1f9476857c9050067b31982bf2140b83d84f3bc0cec1f62bbe947f\",\"dweb:/ipfs/Qma17Pk8NRe7aB4UD3jjVxk7nSFaov3eQyv86hcyqkwJRV\"]},\"lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0xd6153ce99bcdcce22b124f755e72553295be6abcd63804cfdffceb188b8bef10\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://35c47bece3c03caaa07fab37dd2bb3413bfbca20db7bd9895024390e0a469487\",\"dweb:/ipfs/QmPGWT2x3QHcKxqe6gRmAkdakhbaRgx3DLzcakHz5M4eXG\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0xe2e337e6dde9ef6b680e07338c493ebea1b5fd09b43424112868e9cc1706bca7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6df0ddf21ce9f58271bdfaa85cde98b200ef242a05a3f85c2bc10a8294800a92\",\"dweb:/ipfs/QmRK2Y5Yc6BK7tGKkgsgn3aJEQGi5aakeSPZvS65PV8Xp3\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0xd15c3e400531f00203839159b2b8e7209c5158b35618f570c695b7e47f12e9f0\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b600b852e0597aa69989cc263111f02097e2827edc1bdc70306303e3af5e9929\",\"dweb:/ipfs/QmU4WfM28A1nDqghuuGeFmN3CnVrk6opWtiF65K4vhFPeC\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol\":{\"keccak256\":\"0xb3ebde1c8d27576db912d87c3560dab14adfb9cd001be95890ec4ba035e652e7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a709421c4f5d4677db8216055d2d4dac96a613efdb08178a9f7041f0c5cef689\",\"dweb:/ipfs/QmYs2rStvVLDnSJs8HgaMD1ABwoKKWdiVbQyNfLfFWTjTy\"]},\"lib/solmate/src/utils/FixedPointMathLib.sol\":{\"keccak256\":\"0x622fcd8a49e132df5ec7651cc6ae3aaf0cf59bdcd67a9a804a1b9e2485113b7d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af77088eb606427d4c55e578984a615779c86bc30646a20f7bb27299ba390f7c\",\"dweb:/ipfs/QmZGQdhdQDtHc7gZXWrKXgA3govc74X8U63BiWhPQK3mK8\"]},\"src/L1/ResourceMetering.sol\":{\"keccak256\":\"0xde3ac62c60f27a3f1ba06eec94f4eda45e7ec5544c6a5d6b79543a7184e44408\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://265a2845c4ff0d9076dd0505755cf2bdf799f4fdc09ef016865a26b51f5c3409\",\"dweb:/ipfs/QmRzSdBD8jmQf3U9u2ATRAzzuyo6c5ugz8VA5ZM4vzoGiM\"]},\"src/legacy/AddressManager.sol\":{\"keccak256\":\"0x1fcb990df6473f7fa360d5924d62d39ce2ca97d45668e3901e5405cfbe598b19\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9d08358b60dea54dbc32e988a1bb7ea909488063eaae3c5ae28a322f125c9b34\",\"dweb:/ipfs/QmZPQwdjLh9gaamNAoTUmWwwbRKj3yHovBYfnTPnfuKvUt\"]},\"src/legacy/L1ChugSplashProxy.sol\":{\"keccak256\":\"0xdde5626645fa217ad3a37805c4c3012e4251de01df868aae73b986f5d03cdb23\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a99fd0ec440c17c826465001dc88c5185dd41dc72396254fdd3cdfcc84aeae8c\",\"dweb:/ipfs/QmStHuecN89zBL8FH9SUK1TtkyYwfzMY2KkQaFJLHZLuyA\"]},\"src/libraries/Arithmetic.sol\":{\"keccak256\":\"0x91345e053584f82ad04d682ba821cf3ede808304f5b2a88116a894cf692c21db\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://005e3c42d2edfca0a506cbda94d3b0104eddf20c00bd1bd25272f53f2ef74c72\",\"dweb:/ipfs/QmdaW6Nge6NKoGvFqRpQjBpM2fXpc5y8WpZyBnDnKicdJq\"]},\"src/libraries/Burn.sol\":{\"keccak256\":\"0x90a795bcea3ef06d6d5011256c4bd63d1a4271f519246dbf1ee3e8f1c0e21010\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f60c3aa77cf0c484ddda4754157cff4dc0e2eace4bea67990daff4c0612ab5f\",\"dweb:/ipfs/QmSYGanMFve9uBC17X7hFneSFnwnJxz86Jgh6MX9BRMweb\"]},\"src/libraries/Constants.sol\":{\"keccak256\":\"0xe0aeec7d6e5d1e44a11405d3b5bfc384ea092c39bea0b763ab937a26fd427132\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://11aa3bff9da26ca2545132ec7994866690446a5321023811c254410d9593bd9b\",\"dweb:/ipfs/QmVxWqadxvdfkqdrhfWisDqeAthibn4HEE1P6o9aFxXLhp\"]},\"src/universal/Proxy.sol\":{\"keccak256\":\"0x4f6f02e154bbb37137bcedcc256bef1e647865c79ec694fcaf5b6968799d7ddc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://00df4d4c6f4813c883eb33e1ec812c953840e78237fecf09c5739389c0777223\",\"dweb:/ipfs/QmQ1D5j7EwxBPtbQju55hKFQuruAwm8gnPHUTSXtDFjHUe\"]},\"src/universal/ProxyAdmin.sol\":{\"keccak256\":\"0xd15267cf5ed8c24d5a0f2099b8d470178d7ad729db52be16232eb143620b8dcf\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9300ee0feb16fcf6c06ee541f2496eac533256bd97f79fe2128527d2f096894\",\"dweb:/ipfs/Qme3Md8pGSnjkG94WFXUdi5UF3a47BTQgKCdGmTKcMgcRa\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.15+commit.e14f2714"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "addressManager", "outputs": [{"internalType": "contract AddressManager", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address payable", "name": "_proxy", "type": "address"}, {"internalType": "address", "name": "_newAdmin", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "changeProxyAdmin"}, {"inputs": [{"internalType": "address payable", "name": "_proxy", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getProxyAdmin", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "_proxy", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getProxyImplementation", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "implementationName", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "isUpgrading", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "proxyType", "outputs": [{"internalType": "enum ProxyAdmin.ProxyType", "name": "", "type": "uint8"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [{"internalType": "string", "name": "_name", "type": "string"}, {"internalType": "address", "name": "_address", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"inputs": [{"internalType": "contract AddressManager", "name": "_address", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setAddressManager"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}, {"internalType": "string", "name": "_name", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "setImplementationName"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}, {"internalType": "enum ProxyAdmin.ProxyType", "name": "_type", "type": "uint8"}], "stateMutability": "nonpayable", "type": "function", "name": "setProxyType"}, {"inputs": [{"internalType": "bool", "name": "_upgrading", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setUpgrading"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [{"internalType": "address payable", "name": "_proxy", "type": "address"}, {"internalType": "address", "name": "_implementation", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "upgrade"}, {"inputs": [{"internalType": "address payable", "name": "_proxy", "type": "address"}, {"internalType": "address", "name": "_implementation", "type": "address"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "upgradeAndCall"}], "devdoc": {"kind": "dev", "methods": {"changeProxyAdmin(address,address)": {"params": {"_newAdmin": "Address of the new proxy admin.", "_proxy": "Address of the proxy to update."}}, "constructor": {"params": {"_owner": "Address of the initial owner of this contract."}}, "getProxyAdmin(address)": {"params": {"_proxy": "Address of the proxy to get the admin of."}, "returns": {"_0": "Address of the admin of the proxy."}}, "getProxyImplementation(address)": {"params": {"_proxy": "Address of the proxy to get the implementation of."}, "returns": {"_0": "Address of the implementation of the proxy."}}, "isUpgrading()": {"custom:legacy": "@notice Legacy function used to tell ChugSplashProxy contracts if an upgrade is happening.", "returns": {"_0": "Whether or not there is an upgrade going on. May not actually tell you whether an         upgrade is going on, since we don't currently plan to use this variable for anything         other than a legacy indicator to fix a UX bug in the ChugSplash proxy."}}, "owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions anymore. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby removing any functionality that is only available to the owner."}, "setAddress(string,address)": {"custom:legacy": "@notice Set an address in the address manager. Since only the owner of the AddressManager         can directly modify addresses and the ProxyAdmin will own the AddressManager, this         gives the owner of the ProxyAdmin the ability to modify addresses directly.", "params": {"_address": "Address to attach to the given name.", "_name": "Name to set within the AddressManager."}}, "setAddressManager(address)": {"params": {"_address": "Address of the AddressManager."}}, "setImplementationName(address,string)": {"params": {"_address": "Address of the ResolvedDelegateProxy.", "_name": "Name of the implementation for the proxy."}}, "setProxyType(address,uint8)": {"params": {"_address": "Address of the proxy.", "_type": "Type of the proxy."}}, "setUpgrading(bool)": {"custom:legacy": "@notice Set the upgrading status for the Chugsplash proxy type.", "params": {"_upgrading": "Whether or not the system is upgrading."}}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}, "upgrade(address,address)": {"params": {"_implementation": "Address of the new implementation address.", "_proxy": "Address of the proxy to upgrade."}}, "upgradeAndCall(address,address,bytes)": {"params": {"_data": "Data to trigger the new implementation with.", "_implementation": "Address of the new implementation address.", "_proxy": "Address of the proxy to upgrade."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"addressManager()": {"notice": "The address of the address manager, this is required to manage the         ResolvedDelegateProxy type."}, "changeProxyAdmin(address,address)": {"notice": "Updates the admin of the given proxy address."}, "getProxyAdmin(address)": {"notice": "Returns the admin of the given proxy address."}, "getProxyImplementation(address)": {"notice": "Returns the implementation of the given proxy address."}, "implementationName(address)": {"notice": "A reverse mapping of addresses to names held in the AddressManager. This must be         manually kept up to date with changes in the AddressManager for this contract         to be able to work as an admin for the ResolvedDelegateProxy type."}, "proxyType(address)": {"notice": "A mapping of proxy types, used for backwards compatibility."}, "setAddressManager(address)": {"notice": "Set the address of the AddressManager. This is required to manage legacy         ResolvedDelegateProxy type proxy contracts."}, "setImplementationName(address,string)": {"notice": "Sets the implementation name for a given address. Only required for         ResolvedDelegateProxy type proxies that have an implementation name."}, "setProxyType(address,uint8)": {"notice": "Sets the proxy type for a given address. Only required for non-standard (legacy)         proxy types."}, "upgrade(address,address)": {"notice": "Changes a proxy's implementation contract."}, "upgradeAndCall(address,address,bytes)": {"notice": "Changes a proxy's implementation contract and delegatecalls the new implementation         with some given data. Useful for atomic upgrade-and-initialize calls."}}, "version": 1}}, "settings": {"remappings": ["@lib-keccak/=lib/lib-keccak/contracts/lib/", "@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "@rari-capital/solmate/=lib/solmate/", "@solady-test/=lib/lib-keccak/lib/solady/test/", "@solady/=lib/solady/src/", "ds-test/=lib/forge-std/lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "kontrol-cheatcodes/=lib/kontrol-cheatcodes/src/", "lib-keccak/=lib/lib-keccak/contracts/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "safe-contracts/=lib/safe-contracts/contracts/", "solady/=lib/solady/", "solmate/=lib/solmate/src/"], "optimizer": {"enabled": true, "runs": 999999}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/universal/ProxyAdmin.sol": "ProxyAdmin"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xa94b34880e3c1b0b931662cb1c09e5dfa6662f31cba80e07c5ee71cd135c9673", "urls": ["bzz-raw://40fb1b5102468f783961d0af743f91b9980cf66b50d1d12009f6bb1869cea4d2", "dweb:/ipfs/QmYqEbJML4jB1GHbzD4cUZDtJg5wVwNm3vDJq1GbyDus8y"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/utils/Initializable.sol": {"keccak256": "0x2a21b14ff90012878752f230d3ffd5c3405e5938d06c97a7d89c0a64561d0d66", "urls": ["bzz-raw://3313a8f9bb1f9476857c9050067b31982bf2140b83d84f3bc0cec1f62bbe947f", "dweb:/ipfs/Qma17Pk8NRe7aB4UD3jjVxk7nSFaov3eQyv86hcyqkwJRV"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0xd6153ce99bcdcce22b124f755e72553295be6abcd63804cfdffceb188b8bef10", "urls": ["bzz-raw://35c47bece3c03caaa07fab37dd2bb3413bfbca20db7bd9895024390e0a469487", "dweb:/ipfs/QmPGWT2x3QHcKxqe6gRmAkdakhbaRgx3DLzcakHz5M4eXG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0xe2e337e6dde9ef6b680e07338c493ebea1b5fd09b43424112868e9cc1706bca7", "urls": ["bzz-raw://6df0ddf21ce9f58271bdfaa85cde98b200ef242a05a3f85c2bc10a8294800a92", "dweb:/ipfs/QmRK2Y5Yc6BK7tGKkgsgn3aJEQGi5aakeSPZvS65PV8Xp3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0xd15c3e400531f00203839159b2b8e7209c5158b35618f570c695b7e47f12e9f0", "urls": ["bzz-raw://b600b852e0597aa69989cc263111f02097e2827edc1bdc70306303e3af5e9929", "dweb:/ipfs/QmU4WfM28A1nDqghuuGeFmN3CnVrk6opWtiF65K4vhFPeC"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol": {"keccak256": "0xb3ebde1c8d27576db912d87c3560dab14adfb9cd001be95890ec4ba035e652e7", "urls": ["bzz-raw://a709421c4f5d4677db8216055d2d4dac96a613efdb08178a9f7041f0c5cef689", "dweb:/ipfs/QmYs2rStvVLDnSJs8HgaMD1ABwoKKWdiVbQyNfLfFWTjTy"], "license": "MIT"}, "lib/solmate/src/utils/FixedPointMathLib.sol": {"keccak256": "0x622fcd8a49e132df5ec7651cc6ae3aaf0cf59bdcd67a9a804a1b9e2485113b7d", "urls": ["bzz-raw://af77088eb606427d4c55e578984a615779c86bc30646a20f7bb27299ba390f7c", "dweb:/ipfs/QmZGQdhdQDtHc7gZXWrKXgA3govc74X8U63BiWhPQK3mK8"], "license": "MIT"}, "src/L1/ResourceMetering.sol": {"keccak256": "0xde3ac62c60f27a3f1ba06eec94f4eda45e7ec5544c6a5d6b79543a7184e44408", "urls": ["bzz-raw://265a2845c4ff0d9076dd0505755cf2bdf799f4fdc09ef016865a26b51f5c3409", "dweb:/ipfs/QmRzSdBD8jmQf3U9u2ATRAzzuyo6c5ugz8VA5ZM4vzoGiM"], "license": "MIT"}, "src/legacy/AddressManager.sol": {"keccak256": "0x1fcb990df6473f7fa360d5924d62d39ce2ca97d45668e3901e5405cfbe598b19", "urls": ["bzz-raw://9d08358b60dea54dbc32e988a1bb7ea909488063eaae3c5ae28a322f125c9b34", "dweb:/ipfs/QmZPQwdjLh9gaamNAoTUmWwwbRKj3yHovBYfnTPnfuKvUt"], "license": "MIT"}, "src/legacy/L1ChugSplashProxy.sol": {"keccak256": "0xdde5626645fa217ad3a37805c4c3012e4251de01df868aae73b986f5d03cdb23", "urls": ["bzz-raw://a99fd0ec440c17c826465001dc88c5185dd41dc72396254fdd3cdfcc84aeae8c", "dweb:/ipfs/QmStHuecN89zBL8FH9SUK1TtkyYwfzMY2KkQaFJLHZLuyA"], "license": "MIT"}, "src/libraries/Arithmetic.sol": {"keccak256": "0x91345e053584f82ad04d682ba821cf3ede808304f5b2a88116a894cf692c21db", "urls": ["bzz-raw://005e3c42d2edfca0a506cbda94d3b0104eddf20c00bd1bd25272f53f2ef74c72", "dweb:/ipfs/QmdaW6Nge6NKoGvFqRpQjBpM2fXpc5y8WpZyBnDnKicdJq"], "license": "MIT"}, "src/libraries/Burn.sol": {"keccak256": "0x90a795bcea3ef06d6d5011256c4bd63d1a4271f519246dbf1ee3e8f1c0e21010", "urls": ["bzz-raw://9f60c3aa77cf0c484ddda4754157cff4dc0e2eace4bea67990daff4c0612ab5f", "dweb:/ipfs/QmSYGanMFve9uBC17X7hFneSFnwnJxz86Jgh6MX9BRMweb"], "license": "MIT"}, "src/libraries/Constants.sol": {"keccak256": "0xe0aeec7d6e5d1e44a11405d3b5bfc384ea092c39bea0b763ab937a26fd427132", "urls": ["bzz-raw://11aa3bff9da26ca2545132ec7994866690446a5321023811c254410d9593bd9b", "dweb:/ipfs/QmVxWqadxvdfkqdrhfWisDqeAthibn4HEE1P6o9aFxXLhp"], "license": "MIT"}, "src/universal/Proxy.sol": {"keccak256": "0x4f6f02e154bbb37137bcedcc256bef1e647865c79ec694fcaf5b6968799d7ddc", "urls": ["bzz-raw://00df4d4c6f4813c883eb33e1ec812c953840e78237fecf09c5739389c0777223", "dweb:/ipfs/QmQ1D5j7EwxBPtbQju55hKFQuruAwm8gnPHUTSXtDFjHUe"], "license": "MIT"}, "src/universal/ProxyAdmin.sol": {"keccak256": "0xd15267cf5ed8c24d5a0f2099b8d470178d7ad729db52be16232eb143620b8dcf", "urls": ["bzz-raw://e9300ee0feb16fcf6c06ee541f2496eac533256bd97f79fe2128527d2f096894", "dweb:/ipfs/Qme3Md8pGSnjkG94WFXUdi5UF3a47BTQgKCdGmTKcMgcRa"], "license": "MIT"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 49330, "contract": "src/universal/ProxyAdmin.sol:ProxyAdmin", "label": "_owner", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 110483, "contract": "src/universal/ProxyAdmin.sol:ProxyAdmin", "label": "proxyType", "offset": 0, "slot": "1", "type": "t_mapping(t_address,t_enum(ProxyType)110477)"}, {"astId": 110488, "contract": "src/universal/ProxyAdmin.sol:ProxyAdmin", "label": "implementationName", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_string_storage)"}, {"astId": 110492, "contract": "src/universal/ProxyAdmin.sol:ProxyAdmin", "label": "addressManager", "offset": 0, "slot": "3", "type": "t_contract(AddressManager)102008"}, {"astId": 110495, "contract": "src/universal/ProxyAdmin.sol:ProxyAdmin", "label": "upgrading", "offset": 20, "slot": "3", "type": "t_bool"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_contract(AddressManager)102008": {"encoding": "inplace", "label": "contract AddressManager", "numberOfBytes": "20"}, "t_enum(ProxyType)110477": {"encoding": "inplace", "label": "enum ProxyAdmin.ProxyType", "numberOfBytes": "1"}, "t_mapping(t_address,t_enum(ProxyType)110477)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => enum ProxyAdmin.ProxyType)", "numberOfBytes": "32", "value": "t_enum(ProxyType)110477"}, "t_mapping(t_address,t_string_storage)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => string)", "numberOfBytes": "32", "value": "t_string_storage"}, "t_string_storage": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}}}, "userdoc": {"version": 1, "kind": "user", "methods": {"addressManager()": {"notice": "The address of the address manager, this is required to manage the         ResolvedDelegateProxy type."}, "changeProxyAdmin(address,address)": {"notice": "Updates the admin of the given proxy address."}, "getProxyAdmin(address)": {"notice": "Returns the admin of the given proxy address."}, "getProxyImplementation(address)": {"notice": "Returns the implementation of the given proxy address."}, "implementationName(address)": {"notice": "A reverse mapping of addresses to names held in the AddressManager. This must be         manually kept up to date with changes in the AddressManager for this contract         to be able to work as an admin for the ResolvedDelegateProxy type."}, "proxyType(address)": {"notice": "A mapping of proxy types, used for backwards compatibility."}, "setAddressManager(address)": {"notice": "Set the address of the AddressManager. This is required to manage legacy         ResolvedDelegateProxy type proxy contracts."}, "setImplementationName(address,string)": {"notice": "Sets the implementation name for a given address. Only required for         ResolvedDelegateProxy type proxies that have an implementation name."}, "setProxyType(address,uint8)": {"notice": "Sets the proxy type for a given address. Only required for non-standard (legacy)         proxy types."}, "upgrade(address,address)": {"notice": "Changes a proxy's implementation contract."}, "upgradeAndCall(address,address,bytes)": {"notice": "Changes a proxy's implementation contract and delegatecalls the new implementation         with some given data. Useful for atomic upgrade-and-initialize calls."}}, "notice": "This is an auxiliary contract meant to be assigned as the admin of an ERC1967 Proxy,         based on the OpenZeppelin implementation. It has backwards compatibility logic to work         with the various types of proxies that have been deployed by Optimism in the past."}, "devdoc": {"version": 1, "kind": "dev", "methods": {"changeProxyAdmin(address,address)": {"params": {"_newAdmin": "Address of the new proxy admin.", "_proxy": "Address of the proxy to update."}}, "constructor": {"params": {"_owner": "Address of the initial owner of this contract."}}, "getProxyAdmin(address)": {"params": {"_proxy": "Address of the proxy to get the admin of."}, "returns": {"_0": "Address of the admin of the proxy."}}, "getProxyImplementation(address)": {"params": {"_proxy": "Address of the proxy to get the implementation of."}, "returns": {"_0": "Address of the implementation of the proxy."}}, "isUpgrading()": {"returns": {"_0": "Whether or not there is an upgrade going on. May not actually tell you whether an         upgrade is going on, since we don't currently plan to use this variable for anything         other than a legacy indicator to fix a UX bug in the ChugSplash proxy."}}, "owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions anymore. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby removing any functionality that is only available to the owner."}, "setAddress(string,address)": {"params": {"_address": "Address to attach to the given name.", "_name": "Name to set within the AddressManager."}}, "setAddressManager(address)": {"params": {"_address": "Address of the AddressManager."}}, "setImplementationName(address,string)": {"params": {"_address": "Address of the ResolvedDelegateProxy.", "_name": "Name of the implementation for the proxy."}}, "setProxyType(address,uint8)": {"params": {"_address": "Address of the proxy.", "_type": "Type of the proxy."}}, "setUpgrading(bool)": {"params": {"_upgrading": "Whether or not the system is upgrading."}}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}, "upgrade(address,address)": {"params": {"_implementation": "Address of the new implementation address.", "_proxy": "Address of the proxy to upgrade."}}, "upgradeAndCall(address,address,bytes)": {"params": {"_data": "Data to trigger the new implementation with.", "_implementation": "Address of the new implementation address.", "_proxy": "Address of the proxy to upgrade."}}}, "title": "ProxyAdmin"}, "ast": {"absolutePath": "src/universal/ProxyAdmin.sol", "id": 110910, "exportedSymbols": {"AddressManager": [102008], "Constants": [103096], "IStaticERC1967Proxy": [110458], "IStaticL1ChugSplashProxy": [110470], "L1ChugSplashProxy": [102516], "Ownable": [49435], "Proxy": [110434], "ProxyAdmin": [110909]}, "nodeType": "SourceUnit", "src": "32:9246:234", "nodes": [{"id": 110436, "nodeType": "PragmaDirective", "src": "32:23:234", "nodes": [], "literals": ["solidity", "0.8", ".15"]}, {"id": 110438, "nodeType": "ImportDirective", "src": "57:69:234", "nodes": [], "absolutePath": "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "file": "@openzeppelin/contracts/access/Ownable.sol", "nameLocation": "-1:-1:-1", "scope": 110910, "sourceUnit": 49436, "symbolAliases": [{"foreign": {"id": 110437, "name": "Ownable", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 49435, "src": "66:7:234", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 110440, "nodeType": "ImportDirective", "src": "127:48:234", "nodes": [], "absolutePath": "src/universal/Proxy.sol", "file": "src/universal/Proxy.sol", "nameLocation": "-1:-1:-1", "scope": 110910, "sourceUnit": 110435, "symbolAliases": [{"foreign": {"id": 110439, "name": "Proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110434, "src": "136:5:234", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 110442, "nodeType": "ImportDirective", "src": "176:63:234", "nodes": [], "absolutePath": "src/legacy/AddressManager.sol", "file": "src/legacy/AddressManager.sol", "nameLocation": "-1:-1:-1", "scope": 110910, "sourceUnit": 102009, "symbolAliases": [{"foreign": {"id": 110441, "name": "AddressManager", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 102008, "src": "185:14:234", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 110444, "nodeType": "ImportDirective", "src": "240:69:234", "nodes": [], "absolutePath": "src/legacy/L1ChugSplashProxy.sol", "file": "src/legacy/L1ChugSplashProxy.sol", "nameLocation": "-1:-1:-1", "scope": 110910, "sourceUnit": 102517, "symbolAliases": [{"foreign": {"id": 110443, "name": "L1ChugSplashProxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 102516, "src": "249:17:234", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 110446, "nodeType": "ImportDirective", "src": "310:56:234", "nodes": [], "absolutePath": "src/libraries/Constants.sol", "file": "src/libraries/Constants.sol", "nameLocation": "-1:-1:-1", "scope": 110910, "sourceUnit": 103097, "symbolAliases": [{"foreign": {"id": 110445, "name": "Constants", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103096, "src": "319:9:234", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"id": 110458, "nodeType": "ContractDefinition", "src": "483:151:234", "nodes": [{"id": 110452, "nodeType": "FunctionDefinition", "src": "519:58:234", "nodes": [], "functionSelector": "5c60da1b", "implemented": false, "kind": "function", "modifiers": [], "name": "implementation", "nameLocation": "528:14:234", "parameters": {"id": 110448, "nodeType": "ParameterList", "parameters": [], "src": "542:2:234"}, "returnParameters": {"id": 110451, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 110450, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 110452, "src": "568:7:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 110449, "name": "address", "nodeType": "ElementaryTypeName", "src": "568:7:234", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "567:9:234"}, "scope": 110458, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 110457, "nodeType": "FunctionDefinition", "src": "583:49:234", "nodes": [], "functionSelector": "f851a440", "implemented": false, "kind": "function", "modifiers": [], "name": "admin", "nameLocation": "592:5:234", "parameters": {"id": 110453, "nodeType": "ParameterList", "parameters": [], "src": "597:2:234"}, "returnParameters": {"id": 110456, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 110455, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 110457, "src": "623:7:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 110454, "name": "address", "nodeType": "ElementaryTypeName", "src": "623:7:234", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "622:9:234"}, "scope": 110458, "stateMutability": "view", "virtual": false, "visibility": "external"}], "abstract": false, "baseContracts": [], "canonicalName": "IStaticERC1967Proxy", "contractDependencies": [], "contractKind": "interface", "documentation": {"id": 110447, "nodeType": "StructuredDocumentation", "src": "368:115:234", "text": "@title IStaticERC1967Proxy\n @notice IStaticERC1967Proxy is a static version of the ERC1967 proxy interface."}, "fullyImplemented": false, "linearizedBaseContracts": [110458], "name": "IStaticERC1967Proxy", "nameLocation": "493:19:234", "scope": 110910, "usedErrors": []}, {"id": 110470, "nodeType": "ContractDefinition", "src": "764:162:234", "nodes": [{"id": 110464, "nodeType": "FunctionDefinition", "src": "805:61:234", "nodes": [], "functionSelector": "aaf10f42", "implemented": false, "kind": "function", "modifiers": [], "name": "getImplementation", "nameLocation": "814:17:234", "parameters": {"id": 110460, "nodeType": "ParameterList", "parameters": [], "src": "831:2:234"}, "returnParameters": {"id": 110463, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 110462, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 110464, "src": "857:7:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 110461, "name": "address", "nodeType": "ElementaryTypeName", "src": "857:7:234", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "856:9:234"}, "scope": 110470, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 110469, "nodeType": "FunctionDefinition", "src": "872:52:234", "nodes": [], "functionSelector": "893d20e8", "implemented": false, "kind": "function", "modifiers": [], "name": "get<PERSON>wner", "nameLocation": "881:8:234", "parameters": {"id": 110465, "nodeType": "ParameterList", "parameters": [], "src": "889:2:234"}, "returnParameters": {"id": 110468, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 110467, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 110469, "src": "915:7:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 110466, "name": "address", "nodeType": "ElementaryTypeName", "src": "915:7:234", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "914:9:234"}, "scope": 110470, "stateMutability": "view", "virtual": false, "visibility": "external"}], "abstract": false, "baseContracts": [], "canonicalName": "IStaticL1ChugSplashProxy", "contractDependencies": [], "contractKind": "interface", "documentation": {"id": 110459, "nodeType": "StructuredDocumentation", "src": "636:128:234", "text": "@title IStaticL1ChugSplashProxy\n @notice IStaticL1ChugSplashProxy is a static version of the ChugSplash proxy interface."}, "fullyImplemented": false, "linearizedBaseContracts": [110470], "name": "IStaticL1ChugSplashProxy", "nameLocation": "774:24:234", "scope": 110910, "usedErrors": []}, {"id": 110909, "nodeType": "ContractDefinition", "src": "1241:8036:234", "nodes": [{"id": 110477, "nodeType": "EnumDefinition", "src": "1602:76:234", "nodes": [], "canonicalName": "ProxyAdmin.ProxyType", "members": [{"id": 110474, "name": "ERC1967", "nameLocation": "1627:7:234", "nodeType": "EnumValue", "src": "1627:7:234"}, {"id": 110475, "name": "CHUGSPLASH", "nameLocation": "1644:10:234", "nodeType": "EnumValue", "src": "1644:10:234"}, {"id": 110476, "name": "RESOLVED", "nameLocation": "1664:8:234", "nodeType": "EnumValue", "src": "1664:8:234"}], "name": "ProxyType", "nameLocation": "1607:9:234"}, {"id": 110483, "nodeType": "VariableDeclaration", "src": "1760:46:234", "nodes": [], "constant": false, "documentation": {"id": 110478, "nodeType": "StructuredDocumentation", "src": "1684:71:234", "text": "@notice A mapping of proxy types, used for backwards compatibility."}, "functionSelector": "6bd9f516", "mutability": "mutable", "name": "proxyType", "nameLocation": "1797:9:234", "scope": 110909, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_enum$_ProxyType_$110477_$", "typeString": "mapping(address => enum ProxyAdmin.ProxyType)"}, "typeName": {"id": 110482, "keyType": {"id": 110479, "name": "address", "nodeType": "ElementaryTypeName", "src": "1768:7:234", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "1760:29:234", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_enum$_ProxyType_$110477_$", "typeString": "mapping(address => enum ProxyAdmin.ProxyType)"}, "valueType": {"id": 110481, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 110480, "name": "ProxyType", "nodeType": "IdentifierPath", "referencedDeclaration": 110477, "src": "1779:9:234"}, "referencedDeclaration": 110477, "src": "1779:9:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}}, "visibility": "public"}, {"id": 110488, "nodeType": "VariableDeclaration", "src": "2087:52:234", "nodes": [], "constant": false, "documentation": {"id": 110484, "nodeType": "StructuredDocumentation", "src": "1813:269:234", "text": "@notice A reverse mapping of addresses to names held in the AddressManager. This must be\n         manually kept up to date with changes in the AddressManager for this contract\n         to be able to work as an admin for the ResolvedDelegateProxy type."}, "functionSelector": "238181ae", "mutability": "mutable", "name": "implementationName", "nameLocation": "2121:18:234", "scope": 110909, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_string_storage_$", "typeString": "mapping(address => string)"}, "typeName": {"id": 110487, "keyType": {"id": 110485, "name": "address", "nodeType": "ElementaryTypeName", "src": "2095:7:234", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "2087:26:234", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_string_storage_$", "typeString": "mapping(address => string)"}, "valueType": {"id": 110486, "name": "string", "nodeType": "ElementaryTypeName", "src": "2106:6:234", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}}, "visibility": "public"}, {"id": 110492, "nodeType": "VariableDeclaration", "src": "2273:36:234", "nodes": [], "constant": false, "documentation": {"id": 110489, "nodeType": "StructuredDocumentation", "src": "2146:122:234", "text": "@notice The address of the address manager, this is required to manage the\n         ResolvedDelegateProxy type."}, "functionSelector": "3ab76e9f", "mutability": "mutable", "name": "addressManager", "nameLocation": "2295:14:234", "scope": 110909, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_AddressManager_$102008", "typeString": "contract AddressManager"}, "typeName": {"id": 110491, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 110490, "name": "AddressManager", "nodeType": "IdentifierPath", "referencedDeclaration": 102008, "src": "2273:14:234"}, "referencedDeclaration": 102008, "src": "2273:14:234", "typeDescriptions": {"typeIdentifier": "t_contract$_AddressManager_$102008", "typeString": "contract AddressManager"}}, "visibility": "public"}, {"id": 110495, "nodeType": "VariableDeclaration", "src": "2395:23:234", "nodes": [], "constant": false, "documentation": {"id": 110493, "nodeType": "StructuredDocumentation", "src": "2316:74:234", "text": "@notice A legacy upgrading indicator used by the old Chugsplash Proxy."}, "mutability": "mutable", "name": "upgrading", "nameLocation": "2409:9:234", "scope": 110909, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 110494, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2395:4:234", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"id": 110508, "nodeType": "FunctionDefinition", "src": "2494:81:234", "nodes": [], "body": {"id": 110507, "nodeType": "Block", "src": "2532:43:234", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 110504, "name": "_owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110498, "src": "2561:6:234", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 110503, "name": "_transferOwnership", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 49434, "src": "2542:18:234", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 110505, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2542:26:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 110506, "nodeType": "ExpressionStatement", "src": "2542:26:234"}]}, "documentation": {"id": 110496, "nodeType": "StructuredDocumentation", "src": "2425:64:234", "text": "@param _owner Address of the initial owner of this contract."}, "implemented": true, "kind": "constructor", "modifiers": [{"arguments": [], "id": 110501, "kind": "baseConstructorSpecifier", "modifierName": {"id": 110500, "name": "Ownable", "nodeType": "IdentifierPath", "referencedDeclaration": 49435, "src": "2522:7:234"}, "nodeType": "ModifierInvocation", "src": "2522:9:234"}], "name": "", "nameLocation": "-1:-1:-1", "parameters": {"id": 110499, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 110498, "mutability": "mutable", "name": "_owner", "nameLocation": "2514:6:234", "nodeType": "VariableDeclaration", "scope": 110508, "src": "2506:14:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 110497, "name": "address", "nodeType": "ElementaryTypeName", "src": "2506:7:234", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2505:16:234"}, "returnParameters": {"id": 110502, "nodeType": "ParameterList", "parameters": [], "src": "2532:0:234"}, "scope": 110909, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 110526, "nodeType": "FunctionDefinition", "src": "2796:120:234", "nodes": [], "body": {"id": 110525, "nodeType": "Block", "src": "2872:44:234", "nodes": [], "statements": [{"expression": {"id": 110523, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 110519, "name": "proxyType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110483, "src": "2882:9:234", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_enum$_ProxyType_$110477_$", "typeString": "mapping(address => enum ProxyAdmin.ProxyType)"}}, "id": 110521, "indexExpression": {"id": 110520, "name": "_address", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110511, "src": "2892:8:234", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2882:19:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 110522, "name": "_type", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110514, "src": "2904:5:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "src": "2882:27:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "id": 110524, "nodeType": "ExpressionStatement", "src": "2882:27:234"}]}, "documentation": {"id": 110509, "nodeType": "StructuredDocumentation", "src": "2581:210:234", "text": "@notice Sets the proxy type for a given address. Only required for non-standard (legacy)\n         proxy types.\n @param _address Address of the proxy.\n @param _type    Type of the proxy."}, "functionSelector": "8d52d4a0", "implemented": true, "kind": "function", "modifiers": [{"id": 110517, "kind": "modifierInvocation", "modifierName": {"id": 110516, "name": "only<PERSON><PERSON>er", "nodeType": "IdentifierPath", "referencedDeclaration": 49354, "src": "2862:9:234"}, "nodeType": "ModifierInvocation", "src": "2862:9:234"}], "name": "setProxyType", "nameLocation": "2805:12:234", "parameters": {"id": 110515, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 110511, "mutability": "mutable", "name": "_address", "nameLocation": "2826:8:234", "nodeType": "VariableDeclaration", "scope": 110526, "src": "2818:16:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 110510, "name": "address", "nodeType": "ElementaryTypeName", "src": "2818:7:234", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 110514, "mutability": "mutable", "name": "_type", "nameLocation": "2846:5:234", "nodeType": "VariableDeclaration", "scope": 110526, "src": "2836:15:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}, "typeName": {"id": 110513, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 110512, "name": "ProxyType", "nodeType": "IdentifierPath", "referencedDeclaration": 110477, "src": "2836:9:234"}, "referencedDeclaration": 110477, "src": "2836:9:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "visibility": "internal"}], "src": "2817:35:234"}, "returnParameters": {"id": 110518, "nodeType": "ParameterList", "parameters": [], "src": "2872:0:234"}, "scope": 110909, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 110543, "nodeType": "FunctionDefinition", "src": "3219:142:234", "nodes": [], "body": {"id": 110542, "nodeType": "Block", "src": "3308:53:234", "nodes": [], "statements": [{"expression": {"id": 110540, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 110536, "name": "implementationName", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110488, "src": "3318:18:234", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_string_storage_$", "typeString": "mapping(address => string storage ref)"}}, "id": 110538, "indexExpression": {"id": 110537, "name": "_address", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110529, "src": "3337:8:234", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "3318:28:234", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 110539, "name": "_name", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110531, "src": "3349:5:234", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "src": "3318:36:234", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "id": 110541, "nodeType": "ExpressionStatement", "src": "3318:36:234"}]}, "documentation": {"id": 110527, "nodeType": "StructuredDocumentation", "src": "2922:292:234", "text": "@notice Sets the implementation name for a given address. Only required for\n         ResolvedDelegateProxy type proxies that have an implementation name.\n @param _address Address of the ResolvedDelegateProxy.\n @param _name    Name of the implementation for the proxy."}, "functionSelector": "860f7cda", "implemented": true, "kind": "function", "modifiers": [{"id": 110534, "kind": "modifierInvocation", "modifierName": {"id": 110533, "name": "only<PERSON><PERSON>er", "nodeType": "IdentifierPath", "referencedDeclaration": 49354, "src": "3298:9:234"}, "nodeType": "ModifierInvocation", "src": "3298:9:234"}], "name": "setImplementationName", "nameLocation": "3228:21:234", "parameters": {"id": 110532, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 110529, "mutability": "mutable", "name": "_address", "nameLocation": "3258:8:234", "nodeType": "VariableDeclaration", "scope": 110543, "src": "3250:16:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 110528, "name": "address", "nodeType": "ElementaryTypeName", "src": "3250:7:234", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 110531, "mutability": "mutable", "name": "_name", "nameLocation": "3282:5:234", "nodeType": "VariableDeclaration", "scope": 110543, "src": "3268:19:234", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 110530, "name": "string", "nodeType": "ElementaryTypeName", "src": "3268:6:234", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "3249:39:234"}, "returnParameters": {"id": 110535, "nodeType": "ParameterList", "parameters": [], "src": "3308:0:234"}, "scope": 110909, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 110557, "nodeType": "FunctionDefinition", "src": "3571:113:234", "nodes": [], "body": {"id": 110556, "nodeType": "Block", "src": "3642:42:234", "nodes": [], "statements": [{"expression": {"id": 110554, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 110552, "name": "addressManager", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110492, "src": "3652:14:234", "typeDescriptions": {"typeIdentifier": "t_contract$_AddressManager_$102008", "typeString": "contract AddressManager"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 110553, "name": "_address", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110547, "src": "3669:8:234", "typeDescriptions": {"typeIdentifier": "t_contract$_AddressManager_$102008", "typeString": "contract AddressManager"}}, "src": "3652:25:234", "typeDescriptions": {"typeIdentifier": "t_contract$_AddressManager_$102008", "typeString": "contract AddressManager"}}, "id": 110555, "nodeType": "ExpressionStatement", "src": "3652:25:234"}]}, "documentation": {"id": 110544, "nodeType": "StructuredDocumentation", "src": "3367:199:234", "text": "@notice Set the address of the AddressManager. This is required to manage legacy\n         ResolvedDelegateProxy type proxy contracts.\n @param _address Address of the AddressManager."}, "functionSelector": "0652b57a", "implemented": true, "kind": "function", "modifiers": [{"id": 110550, "kind": "modifierInvocation", "modifierName": {"id": 110549, "name": "only<PERSON><PERSON>er", "nodeType": "IdentifierPath", "referencedDeclaration": 49354, "src": "3632:9:234"}, "nodeType": "ModifierInvocation", "src": "3632:9:234"}], "name": "setAddressManager", "nameLocation": "3580:17:234", "parameters": {"id": 110548, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 110547, "mutability": "mutable", "name": "_address", "nameLocation": "3613:8:234", "nodeType": "VariableDeclaration", "scope": 110557, "src": "3598:23:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_AddressManager_$102008", "typeString": "contract AddressManager"}, "typeName": {"id": 110546, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 110545, "name": "AddressManager", "nodeType": "IdentifierPath", "referencedDeclaration": 102008, "src": "3598:14:234"}, "referencedDeclaration": 102008, "src": "3598:14:234", "typeDescriptions": {"typeIdentifier": "t_contract$_AddressManager_$102008", "typeString": "contract AddressManager"}}, "visibility": "internal"}], "src": "3597:25:234"}, "returnParameters": {"id": 110551, "nodeType": "ParameterList", "parameters": [], "src": "3642:0:234"}, "scope": 110909, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 110575, "nodeType": "FunctionDefinition", "src": "4126:137:234", "nodes": [], "body": {"id": 110574, "nodeType": "Block", "src": "4204:59:234", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 110570, "name": "_name", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110560, "src": "4240:5:234", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 110571, "name": "_address", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110562, "src": "4247:8:234", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 110567, "name": "addressManager", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110492, "src": "4214:14:234", "typeDescriptions": {"typeIdentifier": "t_contract$_AddressManager_$102008", "typeString": "contract AddressManager"}}, "id": 110569, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "<PERSON><PERSON><PERSON><PERSON>", "nodeType": "MemberAccess", "referencedDeclaration": 101976, "src": "4214:25:234", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_address_$returns$__$", "typeString": "function (string memory,address) external"}}, "id": 110572, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "4214:42:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 110573, "nodeType": "ExpressionStatement", "src": "4214:42:234"}]}, "documentation": {"id": 110558, "nodeType": "StructuredDocumentation", "src": "3690:431:234", "text": "@custom:legacy\n @notice Set an address in the address manager. Since only the owner of the AddressManager\n         can directly modify addresses and the ProxyAdmin will own the AddressManager, this\n         gives the owner of the ProxyAdmin the ability to modify addresses directly.\n @param _name    Name to set within the AddressManager.\n @param _address Address to attach to the given name."}, "functionSelector": "9b2ea4bd", "implemented": true, "kind": "function", "modifiers": [{"id": 110565, "kind": "modifierInvocation", "modifierName": {"id": 110564, "name": "only<PERSON><PERSON>er", "nodeType": "IdentifierPath", "referencedDeclaration": 49354, "src": "4194:9:234"}, "nodeType": "ModifierInvocation", "src": "4194:9:234"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "nameLocation": "4135:10:234", "parameters": {"id": 110563, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 110560, "mutability": "mutable", "name": "_name", "nameLocation": "4160:5:234", "nodeType": "VariableDeclaration", "scope": 110575, "src": "4146:19:234", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 110559, "name": "string", "nodeType": "ElementaryTypeName", "src": "4146:6:234", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 110562, "mutability": "mutable", "name": "_address", "nameLocation": "4175:8:234", "nodeType": "VariableDeclaration", "scope": 110575, "src": "4167:16:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 110561, "name": "address", "nodeType": "ElementaryTypeName", "src": "4167:7:234", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "4145:39:234"}, "returnParameters": {"id": 110566, "nodeType": "ParameterList", "parameters": [], "src": "4204:0:234"}, "scope": 110909, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 110588, "nodeType": "FunctionDefinition", "src": "4430:97:234", "nodes": [], "body": {"id": 110587, "nodeType": "Block", "src": "4488:39:234", "nodes": [], "statements": [{"expression": {"id": 110585, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 110583, "name": "upgrading", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110495, "src": "4498:9:234", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 110584, "name": "_upgrading", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110578, "src": "4510:10:234", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "4498:22:234", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 110586, "nodeType": "ExpressionStatement", "src": "4498:22:234"}]}, "documentation": {"id": 110576, "nodeType": "StructuredDocumentation", "src": "4269:156:234", "text": "@custom:legacy\n @notice Set the upgrading status for the Chugsplash proxy type.\n @param _upgrading Whether or not the system is upgrading."}, "functionSelector": "07c8f7b0", "implemented": true, "kind": "function", "modifiers": [{"id": 110581, "kind": "modifierInvocation", "modifierName": {"id": 110580, "name": "only<PERSON><PERSON>er", "nodeType": "IdentifierPath", "referencedDeclaration": 49354, "src": "4478:9:234"}, "nodeType": "ModifierInvocation", "src": "4478:9:234"}], "name": "setUpgrading", "nameLocation": "4439:12:234", "parameters": {"id": 110579, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 110578, "mutability": "mutable", "name": "_upgrading", "nameLocation": "4457:10:234", "nodeType": "VariableDeclaration", "scope": 110588, "src": "4452:15:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 110577, "name": "bool", "nodeType": "ElementaryTypeName", "src": "4452:4:234", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "4451:17:234"}, "returnParameters": {"id": 110582, "nodeType": "ParameterList", "parameters": [], "src": "4488:0:234"}, "scope": 110909, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 110597, "nodeType": "FunctionDefinition", "src": "4941:85:234", "nodes": [], "body": {"id": 110596, "nodeType": "Block", "src": "4993:33:234", "nodes": [], "statements": [{"expression": {"id": 110594, "name": "upgrading", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110495, "src": "5010:9:234", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 110593, "id": 110595, "nodeType": "Return", "src": "5003:16:234"}]}, "documentation": {"id": 110589, "nodeType": "StructuredDocumentation", "src": "4533:403:234", "text": "@custom:legacy\n @notice Legacy function used to tell ChugSplashProxy contracts if an upgrade is happening.\n @return Whether or not there is an upgrade going on. May not actually tell you whether an\n         upgrade is going on, since we don't currently plan to use this variable for anything\n         other than a legacy indicator to fix a UX bug in the ChugSplash proxy."}, "functionSelector": "b7947262", "implemented": true, "kind": "function", "modifiers": [], "name": "isUpgrading", "nameLocation": "4950:11:234", "parameters": {"id": 110590, "nodeType": "ParameterList", "parameters": [], "src": "4961:2:234"}, "returnParameters": {"id": 110593, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 110592, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 110597, "src": "4987:4:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 110591, "name": "bool", "nodeType": "ElementaryTypeName", "src": "4987:4:234", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "4986:6:234"}, "scope": 110909, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 110655, "nodeType": "FunctionDefinition", "src": "5236:569:234", "nodes": [], "body": {"id": 110654, "nodeType": "Block", "src": "5316:489:234", "nodes": [], "statements": [{"assignments": [110607], "declarations": [{"constant": false, "id": 110607, "mutability": "mutable", "name": "ptype", "nameLocation": "5336:5:234", "nodeType": "VariableDeclaration", "scope": 110654, "src": "5326:15:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}, "typeName": {"id": 110606, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 110605, "name": "ProxyType", "nodeType": "IdentifierPath", "referencedDeclaration": 110477, "src": "5326:9:234"}, "referencedDeclaration": 110477, "src": "5326:9:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "visibility": "internal"}], "id": 110611, "initialValue": {"baseExpression": {"id": 110608, "name": "proxyType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110483, "src": "5344:9:234", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_enum$_ProxyType_$110477_$", "typeString": "mapping(address => enum ProxyAdmin.ProxyType)"}}, "id": 110610, "indexExpression": {"id": 110609, "name": "_proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110600, "src": "5354:6:234", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5344:17:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "nodeType": "VariableDeclarationStatement", "src": "5326:35:234"}, {"condition": {"commonType": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}, "id": 110615, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 110612, "name": "ptype", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110607, "src": "5375:5:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"id": 110613, "name": "ProxyType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110477, "src": "5384:9:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_enum$_ProxyType_$110477_$", "typeString": "type(enum ProxyAdmin.ProxyType)"}}, "id": 110614, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberName": "ERC1967", "nodeType": "MemberAccess", "referencedDeclaration": 110474, "src": "5384:17:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "src": "5375:26:234", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}, "id": 110626, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 110623, "name": "ptype", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110607, "src": "5489:5:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"id": 110624, "name": "ProxyType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110477, "src": "5498:9:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_enum$_ProxyType_$110477_$", "typeString": "type(enum ProxyAdmin.ProxyType)"}}, "id": 110625, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberName": "CHUGSPLASH", "nodeType": "MemberAccess", "referencedDeclaration": 110475, "src": "5498:20:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "src": "5489:29:234", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}, "id": 110637, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 110634, "name": "ptype", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110607, "src": "5614:5:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"id": 110635, "name": "ProxyType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110477, "src": "5623:9:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_enum$_ProxyType_$110477_$", "typeString": "type(enum ProxyAdmin.ProxyType)"}}, "id": 110636, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberName": "RESOLVED", "nodeType": "MemberAccess", "referencedDeclaration": 110476, "src": "5623:18:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "src": "5614:27:234", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"id": 110650, "nodeType": "Block", "src": "5734:65:234", "statements": [{"expression": {"arguments": [{"hexValue": "50726f787941646d696e3a20756e6b6e6f776e2070726f78792074797065", "id": 110647, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5755:32:234", "typeDescriptions": {"typeIdentifier": "t_stringliteral_0d595a635c8f2d148646b25cd19d12c4c97462aeb17388cbeb2bf405cffe65f2", "typeString": "literal_string \"ProxyAdmin: unknown proxy type\""}, "value": "ProxyAdmin: unknown proxy type"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_0d595a635c8f2d148646b25cd19d12c4c97462aeb17388cbeb2bf405cffe65f2", "typeString": "literal_string \"ProxyAdmin: unknown proxy type\""}], "id": 110646, "name": "revert", "nodeType": "Identifier", "overloadedDeclarations": [-19, -19], "referencedDeclaration": -19, "src": "5748:6:234", "typeDescriptions": {"typeIdentifier": "t_function_revert_pure$_t_string_memory_ptr_$returns$__$", "typeString": "function (string memory) pure"}}, "id": 110648, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "5748:40:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 110649, "nodeType": "ExpressionStatement", "src": "5748:40:234"}]}, "id": 110651, "nodeType": "IfStatement", "src": "5610:189:234", "trueBody": {"id": 110645, "nodeType": "Block", "src": "5643:85:234", "statements": [{"expression": {"arguments": [{"baseExpression": {"id": 110640, "name": "implementationName", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110488, "src": "5690:18:234", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_string_storage_$", "typeString": "mapping(address => string storage ref)"}}, "id": 110642, "indexExpression": {"id": 110641, "name": "_proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110600, "src": "5709:6:234", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5690:26:234", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}], "expression": {"id": 110638, "name": "addressManager", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110492, "src": "5664:14:234", "typeDescriptions": {"typeIdentifier": "t_contract$_AddressManager_$102008", "typeString": "contract AddressManager"}}, "id": 110639, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "get<PERSON><PERSON><PERSON>", "nodeType": "MemberAccess", "referencedDeclaration": 101991, "src": "5664:25:234", "typeDescriptions": {"typeIdentifier": "t_function_external_view$_t_string_memory_ptr_$returns$_t_address_$", "typeString": "function (string memory) view external returns (address)"}}, "id": 110643, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "5664:53:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 110604, "id": 110644, "nodeType": "Return", "src": "5657:60:234"}]}}, "id": 110652, "nodeType": "IfStatement", "src": "5485:314:234", "trueBody": {"id": 110633, "nodeType": "Block", "src": "5520:84:234", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"arguments": [{"id": 110628, "name": "_proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110600, "src": "5566:6:234", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 110627, "name": "IStaticL1ChugSplashProxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110470, "src": "5541:24:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IStaticL1ChugSplashProxy_$110470_$", "typeString": "type(contract IStaticL1ChugSplashProxy)"}}, "id": 110629, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "5541:32:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IStaticL1ChugSplashProxy_$110470", "typeString": "contract IStaticL1ChugSplashProxy"}}, "id": 110630, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "getImplementation", "nodeType": "MemberAccess", "referencedDeclaration": 110464, "src": "5541:50:234", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_address_$", "typeString": "function () view external returns (address)"}}, "id": 110631, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "5541:52:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 110604, "id": 110632, "nodeType": "Return", "src": "5534:59:234"}]}}, "id": 110653, "nodeType": "IfStatement", "src": "5371:428:234", "trueBody": {"id": 110622, "nodeType": "Block", "src": "5403:76:234", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"arguments": [{"id": 110617, "name": "_proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110600, "src": "5444:6:234", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 110616, "name": "IStaticERC1967Proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110458, "src": "5424:19:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IStaticERC1967Proxy_$110458_$", "typeString": "type(contract IStaticERC1967Proxy)"}}, "id": 110618, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "5424:27:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IStaticERC1967Proxy_$110458", "typeString": "contract IStaticERC1967Proxy"}}, "id": 110619, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "implementation", "nodeType": "MemberAccess", "referencedDeclaration": 110452, "src": "5424:42:234", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_address_$", "typeString": "function () view external returns (address)"}}, "id": 110620, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "5424:44:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 110604, "id": 110621, "nodeType": "Return", "src": "5417:51:234"}]}}]}, "documentation": {"id": 110598, "nodeType": "StructuredDocumentation", "src": "5032:199:234", "text": "@notice Returns the implementation of the given proxy address.\n @param _proxy Address of the proxy to get the implementation of.\n @return Address of the implementation of the proxy."}, "functionSelector": "204e1c7a", "implemented": true, "kind": "function", "modifiers": [], "name": "getProxyImplementation", "nameLocation": "5245:22:234", "parameters": {"id": 110601, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 110600, "mutability": "mutable", "name": "_proxy", "nameLocation": "5276:6:234", "nodeType": "VariableDeclaration", "scope": 110655, "src": "5268:14:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 110599, "name": "address", "nodeType": "ElementaryTypeName", "src": "5268:7:234", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "5267:16:234"}, "returnParameters": {"id": 110604, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 110603, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 110655, "src": "5307:7:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 110602, "name": "address", "nodeType": "ElementaryTypeName", "src": "5307:7:234", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "5306:9:234"}, "scope": 110909, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 110710, "nodeType": "FunctionDefinition", "src": "5988:519:234", "nodes": [], "body": {"id": 110709, "nodeType": "Block", "src": "6067:440:234", "nodes": [], "statements": [{"assignments": [110665], "declarations": [{"constant": false, "id": 110665, "mutability": "mutable", "name": "ptype", "nameLocation": "6087:5:234", "nodeType": "VariableDeclaration", "scope": 110709, "src": "6077:15:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}, "typeName": {"id": 110664, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 110663, "name": "ProxyType", "nodeType": "IdentifierPath", "referencedDeclaration": 110477, "src": "6077:9:234"}, "referencedDeclaration": 110477, "src": "6077:9:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "visibility": "internal"}], "id": 110669, "initialValue": {"baseExpression": {"id": 110666, "name": "proxyType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110483, "src": "6095:9:234", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_enum$_ProxyType_$110477_$", "typeString": "mapping(address => enum ProxyAdmin.ProxyType)"}}, "id": 110668, "indexExpression": {"id": 110667, "name": "_proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110658, "src": "6105:6:234", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "6095:17:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "nodeType": "VariableDeclarationStatement", "src": "6077:35:234"}, {"condition": {"commonType": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}, "id": 110673, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 110670, "name": "ptype", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110665, "src": "6126:5:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"id": 110671, "name": "ProxyType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110477, "src": "6135:9:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_enum$_ProxyType_$110477_$", "typeString": "type(enum ProxyAdmin.ProxyType)"}}, "id": 110672, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberName": "ERC1967", "nodeType": "MemberAccess", "referencedDeclaration": 110474, "src": "6135:17:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "src": "6126:26:234", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}, "id": 110684, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 110681, "name": "ptype", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110665, "src": "6231:5:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"id": 110682, "name": "ProxyType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110477, "src": "6240:9:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_enum$_ProxyType_$110477_$", "typeString": "type(enum ProxyAdmin.ProxyType)"}}, "id": 110683, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberName": "CHUGSPLASH", "nodeType": "MemberAccess", "referencedDeclaration": 110475, "src": "6240:20:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "src": "6231:29:234", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}, "id": 110695, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 110692, "name": "ptype", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110665, "src": "6347:5:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"id": 110693, "name": "ProxyType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110477, "src": "6356:9:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_enum$_ProxyType_$110477_$", "typeString": "type(enum ProxyAdmin.ProxyType)"}}, "id": 110694, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberName": "RESOLVED", "nodeType": "MemberAccess", "referencedDeclaration": 110476, "src": "6356:18:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "src": "6347:27:234", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"id": 110705, "nodeType": "Block", "src": "6436:65:234", "statements": [{"expression": {"arguments": [{"hexValue": "50726f787941646d696e3a20756e6b6e6f776e2070726f78792074797065", "id": 110702, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "6457:32:234", "typeDescriptions": {"typeIdentifier": "t_stringliteral_0d595a635c8f2d148646b25cd19d12c4c97462aeb17388cbeb2bf405cffe65f2", "typeString": "literal_string \"ProxyAdmin: unknown proxy type\""}, "value": "ProxyAdmin: unknown proxy type"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_0d595a635c8f2d148646b25cd19d12c4c97462aeb17388cbeb2bf405cffe65f2", "typeString": "literal_string \"ProxyAdmin: unknown proxy type\""}], "id": 110701, "name": "revert", "nodeType": "Identifier", "overloadedDeclarations": [-19, -19], "referencedDeclaration": -19, "src": "6450:6:234", "typeDescriptions": {"typeIdentifier": "t_function_revert_pure$_t_string_memory_ptr_$returns$__$", "typeString": "function (string memory) pure"}}, "id": 110703, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "6450:40:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 110704, "nodeType": "ExpressionStatement", "src": "6450:40:234"}]}, "id": 110706, "nodeType": "IfStatement", "src": "6343:158:234", "trueBody": {"id": 110700, "nodeType": "Block", "src": "6376:54:234", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 110696, "name": "addressManager", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110492, "src": "6397:14:234", "typeDescriptions": {"typeIdentifier": "t_contract$_AddressManager_$102008", "typeString": "contract AddressManager"}}, "id": 110697, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "owner", "nodeType": "MemberAccess", "referencedDeclaration": 49363, "src": "6397:20:234", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_address_$", "typeString": "function () view external returns (address)"}}, "id": 110698, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "6397:22:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 110662, "id": 110699, "nodeType": "Return", "src": "6390:29:234"}]}}, "id": 110707, "nodeType": "IfStatement", "src": "6227:274:234", "trueBody": {"id": 110691, "nodeType": "Block", "src": "6262:75:234", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"arguments": [{"id": 110686, "name": "_proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110658, "src": "6308:6:234", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}], "id": 110685, "name": "IStaticL1ChugSplashProxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110470, "src": "6283:24:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IStaticL1ChugSplashProxy_$110470_$", "typeString": "type(contract IStaticL1ChugSplashProxy)"}}, "id": 110687, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "6283:32:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IStaticL1ChugSplashProxy_$110470", "typeString": "contract IStaticL1ChugSplashProxy"}}, "id": 110688, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "get<PERSON>wner", "nodeType": "MemberAccess", "referencedDeclaration": 110469, "src": "6283:41:234", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_address_$", "typeString": "function () view external returns (address)"}}, "id": 110689, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "6283:43:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 110662, "id": 110690, "nodeType": "Return", "src": "6276:50:234"}]}}, "id": 110708, "nodeType": "IfStatement", "src": "6122:379:234", "trueBody": {"id": 110680, "nodeType": "Block", "src": "6154:67:234", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"arguments": [{"id": 110675, "name": "_proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110658, "src": "6195:6:234", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}], "id": 110674, "name": "IStaticERC1967Proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110458, "src": "6175:19:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IStaticERC1967Proxy_$110458_$", "typeString": "type(contract IStaticERC1967Proxy)"}}, "id": 110676, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "6175:27:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IStaticERC1967Proxy_$110458", "typeString": "contract IStaticERC1967Proxy"}}, "id": 110677, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "admin", "nodeType": "MemberAccess", "referencedDeclaration": 110457, "src": "6175:33:234", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_address_$", "typeString": "function () view external returns (address)"}}, "id": 110678, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "6175:35:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 110662, "id": 110679, "nodeType": "Return", "src": "6168:42:234"}]}}]}, "documentation": {"id": 110656, "nodeType": "StructuredDocumentation", "src": "5811:172:234", "text": "@notice Returns the admin of the given proxy address.\n @param _proxy Address of the proxy to get the admin of.\n @return Address of the admin of the proxy."}, "functionSelector": "f3b7dead", "implemented": true, "kind": "function", "modifiers": [], "name": "getProxyAdmin", "nameLocation": "5997:13:234", "parameters": {"id": 110659, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 110658, "mutability": "mutable", "name": "_proxy", "nameLocation": "6027:6:234", "nodeType": "VariableDeclaration", "scope": 110710, "src": "6011:22:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}, "typeName": {"id": 110657, "name": "address", "nodeType": "ElementaryTypeName", "src": "6011:15:234", "stateMutability": "payable", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "visibility": "internal"}], "src": "6010:24:234"}, "returnParameters": {"id": 110662, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 110661, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 110710, "src": "6058:7:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 110660, "name": "address", "nodeType": "ElementaryTypeName", "src": "6058:7:234", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "6057:9:234"}, "scope": 110909, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 110771, "nodeType": "FunctionDefinition", "src": "6689:531:234", "nodes": [], "body": {"id": 110770, "nodeType": "Block", "src": "6777:443:234", "nodes": [], "statements": [{"assignments": [110722], "declarations": [{"constant": false, "id": 110722, "mutability": "mutable", "name": "ptype", "nameLocation": "6797:5:234", "nodeType": "VariableDeclaration", "scope": 110770, "src": "6787:15:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}, "typeName": {"id": 110721, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 110720, "name": "ProxyType", "nodeType": "IdentifierPath", "referencedDeclaration": 110477, "src": "6787:9:234"}, "referencedDeclaration": 110477, "src": "6787:9:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "visibility": "internal"}], "id": 110726, "initialValue": {"baseExpression": {"id": 110723, "name": "proxyType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110483, "src": "6805:9:234", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_enum$_ProxyType_$110477_$", "typeString": "mapping(address => enum ProxyAdmin.ProxyType)"}}, "id": 110725, "indexExpression": {"id": 110724, "name": "_proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110713, "src": "6815:6:234", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "6805:17:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "nodeType": "VariableDeclarationStatement", "src": "6787:35:234"}, {"condition": {"commonType": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}, "id": 110730, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 110727, "name": "ptype", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110722, "src": "6836:5:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"id": 110728, "name": "ProxyType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110477, "src": "6845:9:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_enum$_ProxyType_$110477_$", "typeString": "type(enum ProxyAdmin.ProxyType)"}}, "id": 110729, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberName": "ERC1967", "nodeType": "MemberAccess", "referencedDeclaration": 110474, "src": "6845:17:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "src": "6836:26:234", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}, "id": 110742, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 110739, "name": "ptype", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110722, "src": "6935:5:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"id": 110740, "name": "ProxyType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110477, "src": "6944:9:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_enum$_ProxyType_$110477_$", "typeString": "type(enum ProxyAdmin.ProxyType)"}}, "id": 110741, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberName": "CHUGSPLASH", "nodeType": "MemberAccess", "referencedDeclaration": 110475, "src": "6944:20:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "src": "6935:29:234", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}, "id": 110754, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 110751, "name": "ptype", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110722, "src": "7046:5:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"id": 110752, "name": "ProxyType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110477, "src": "7055:9:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_enum$_ProxyType_$110477_$", "typeString": "type(enum ProxyAdmin.ProxyType)"}}, "id": 110753, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberName": "RESOLVED", "nodeType": "MemberAccess", "referencedDeclaration": 110476, "src": "7055:18:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "src": "7046:27:234", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"id": 110766, "nodeType": "Block", "src": "7149:65:234", "statements": [{"expression": {"arguments": [{"hexValue": "50726f787941646d696e3a20756e6b6e6f776e2070726f78792074797065", "id": 110763, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "7170:32:234", "typeDescriptions": {"typeIdentifier": "t_stringliteral_0d595a635c8f2d148646b25cd19d12c4c97462aeb17388cbeb2bf405cffe65f2", "typeString": "literal_string \"ProxyAdmin: unknown proxy type\""}, "value": "ProxyAdmin: unknown proxy type"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_0d595a635c8f2d148646b25cd19d12c4c97462aeb17388cbeb2bf405cffe65f2", "typeString": "literal_string \"ProxyAdmin: unknown proxy type\""}], "id": 110762, "name": "revert", "nodeType": "Identifier", "overloadedDeclarations": [-19, -19], "referencedDeclaration": -19, "src": "7163:6:234", "typeDescriptions": {"typeIdentifier": "t_function_revert_pure$_t_string_memory_ptr_$returns$__$", "typeString": "function (string memory) pure"}}, "id": 110764, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "7163:40:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 110765, "nodeType": "ExpressionStatement", "src": "7163:40:234"}]}, "id": 110767, "nodeType": "IfStatement", "src": "7042:172:234", "trueBody": {"id": 110761, "nodeType": "Block", "src": "7075:68:234", "statements": [{"expression": {"arguments": [{"id": 110758, "name": "_newAdmin", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110715, "src": "7122:9:234", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 110755, "name": "addressManager", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110492, "src": "7089:14:234", "typeDescriptions": {"typeIdentifier": "t_contract$_AddressManager_$102008", "typeString": "contract AddressManager"}}, "id": 110757, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "transferOwnership", "nodeType": "MemberAccess", "referencedDeclaration": 49414, "src": "7089:32:234", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_address_$returns$__$", "typeString": "function (address) external"}}, "id": 110759, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "7089:43:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 110760, "nodeType": "ExpressionStatement", "src": "7089:43:234"}]}}, "id": 110768, "nodeType": "IfStatement", "src": "6931:283:234", "trueBody": {"id": 110750, "nodeType": "Block", "src": "6966:70:234", "statements": [{"expression": {"arguments": [{"id": 110747, "name": "_newAdmin", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110715, "src": "7015:9:234", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"arguments": [{"id": 110744, "name": "_proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110713, "src": "6998:6:234", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}], "id": 110743, "name": "L1ChugSplashProxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 102516, "src": "6980:17:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_L1ChugSplashProxy_$102516_$", "typeString": "type(contract L1ChugSplashProxy)"}}, "id": 110745, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "6980:25:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_L1ChugSplashProxy_$102516", "typeString": "contract L1ChugSplashProxy"}}, "id": 110746, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "<PERSON><PERSON><PERSON><PERSON>", "nodeType": "MemberAccess", "referencedDeclaration": 102391, "src": "6980:34:234", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_address_$returns$__$", "typeString": "function (address) external"}}, "id": 110748, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "6980:45:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 110749, "nodeType": "ExpressionStatement", "src": "6980:45:234"}]}}, "id": 110769, "nodeType": "IfStatement", "src": "6832:382:234", "trueBody": {"id": 110738, "nodeType": "Block", "src": "6864:61:234", "statements": [{"expression": {"arguments": [{"id": 110735, "name": "_newAdmin", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110715, "src": "6904:9:234", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"arguments": [{"id": 110732, "name": "_proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110713, "src": "6884:6:234", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}], "id": 110731, "name": "Proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110434, "src": "6878:5:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_Proxy_$110434_$", "typeString": "type(contract Proxy)"}}, "id": 110733, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "6878:13:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_Proxy_$110434", "typeString": "contract Proxy"}}, "id": 110734, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "changeAdmin", "nodeType": "MemberAccess", "referencedDeclaration": 110312, "src": "6878:25:234", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_address_$returns$__$", "typeString": "function (address) external"}}, "id": 110736, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "6878:36:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 110737, "nodeType": "ExpressionStatement", "src": "6878:36:234"}]}}]}, "documentation": {"id": 110711, "nodeType": "StructuredDocumentation", "src": "6513:171:234", "text": "@notice Updates the admin of the given proxy address.\n @param _proxy    Address of the proxy to update.\n @param _newAdmin Address of the new proxy admin."}, "functionSelector": "7eff275e", "implemented": true, "kind": "function", "modifiers": [{"id": 110718, "kind": "modifierInvocation", "modifierName": {"id": 110717, "name": "only<PERSON><PERSON>er", "nodeType": "IdentifierPath", "referencedDeclaration": 49354, "src": "6767:9:234"}, "nodeType": "ModifierInvocation", "src": "6767:9:234"}], "name": "changeProxyAdmin", "nameLocation": "6698:16:234", "parameters": {"id": 110716, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 110713, "mutability": "mutable", "name": "_proxy", "nameLocation": "6731:6:234", "nodeType": "VariableDeclaration", "scope": 110771, "src": "6715:22:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}, "typeName": {"id": 110712, "name": "address", "nodeType": "ElementaryTypeName", "src": "6715:15:234", "stateMutability": "payable", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "visibility": "internal"}, {"constant": false, "id": 110715, "mutability": "mutable", "name": "_newAdmin", "nameLocation": "6747:9:234", "nodeType": "VariableDeclaration", "scope": 110771, "src": "6739:17:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 110714, "name": "address", "nodeType": "ElementaryTypeName", "src": "6739:7:234", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "6714:43:234"}, "returnParameters": {"id": 110719, "nodeType": "ParameterList", "parameters": [], "src": "6777:0:234"}, "scope": 110909, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 110850, "nodeType": "FunctionDefinition", "src": "7423:816:234", "nodes": [], "body": {"id": 110849, "nodeType": "Block", "src": "7506:733:234", "nodes": [], "statements": [{"assignments": [110783], "declarations": [{"constant": false, "id": 110783, "mutability": "mutable", "name": "ptype", "nameLocation": "7526:5:234", "nodeType": "VariableDeclaration", "scope": 110849, "src": "7516:15:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}, "typeName": {"id": 110782, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 110781, "name": "ProxyType", "nodeType": "IdentifierPath", "referencedDeclaration": 110477, "src": "7516:9:234"}, "referencedDeclaration": 110477, "src": "7516:9:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "visibility": "internal"}], "id": 110787, "initialValue": {"baseExpression": {"id": 110784, "name": "proxyType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110483, "src": "7534:9:234", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_enum$_ProxyType_$110477_$", "typeString": "mapping(address => enum ProxyAdmin.ProxyType)"}}, "id": 110786, "indexExpression": {"id": 110785, "name": "_proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110774, "src": "7544:6:234", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "7534:17:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "nodeType": "VariableDeclarationStatement", "src": "7516:35:234"}, {"condition": {"commonType": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}, "id": 110791, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 110788, "name": "ptype", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110783, "src": "7565:5:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"id": 110789, "name": "ProxyType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110477, "src": "7574:9:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_enum$_ProxyType_$110477_$", "typeString": "type(enum ProxyAdmin.ProxyType)"}}, "id": 110790, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberName": "ERC1967", "nodeType": "MemberAccess", "referencedDeclaration": 110474, "src": "7574:17:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "src": "7565:26:234", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}, "id": 110803, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 110800, "name": "ptype", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110783, "src": "7668:5:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"id": 110801, "name": "ProxyType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110477, "src": "7677:9:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_enum$_ProxyType_$110477_$", "typeString": "type(enum ProxyAdmin.ProxyType)"}}, "id": 110802, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberName": "CHUGSPLASH", "nodeType": "MemberAccess", "referencedDeclaration": 110475, "src": "7677:20:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "src": "7668:29:234", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}, "id": 110826, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 110823, "name": "ptype", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110783, "src": "7884:5:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"id": 110824, "name": "ProxyType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110477, "src": "7893:9:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_enum$_ProxyType_$110477_$", "typeString": "type(enum ProxyAdmin.ProxyType)"}}, "id": 110825, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberName": "RESOLVED", "nodeType": "MemberAccess", "referencedDeclaration": 110476, "src": "7893:18:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "src": "7884:27:234", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"id": 110845, "nodeType": "Block", "src": "8053:180:234", "statements": [{"expression": {"arguments": [{"hexValue": "66616c7365", "id": 110842, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "8216:5:234", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 110841, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -3, "src": "8209:6:234", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 110843, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "8209:13:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 110844, "nodeType": "ExpressionStatement", "src": "8209:13:234"}]}, "id": 110846, "nodeType": "IfStatement", "src": "7880:353:234", "trueBody": {"id": 110840, "nodeType": "Block", "src": "7913:134:234", "statements": [{"assignments": [110828], "declarations": [{"constant": false, "id": 110828, "mutability": "mutable", "name": "name", "nameLocation": "7941:4:234", "nodeType": "VariableDeclaration", "scope": 110840, "src": "7927:18:234", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 110827, "name": "string", "nodeType": "ElementaryTypeName", "src": "7927:6:234", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "id": 110832, "initialValue": {"baseExpression": {"id": 110829, "name": "implementationName", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110488, "src": "7948:18:234", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_string_storage_$", "typeString": "mapping(address => string storage ref)"}}, "id": 110831, "indexExpression": {"id": 110830, "name": "_proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110774, "src": "7967:6:234", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "7948:26:234", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "nodeType": "VariableDeclarationStatement", "src": "7927:47:234"}, {"expression": {"arguments": [{"id": 110836, "name": "name", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110828, "src": "8014:4:234", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 110837, "name": "_implementation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110776, "src": "8020:15:234", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 110833, "name": "addressManager", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110492, "src": "7988:14:234", "typeDescriptions": {"typeIdentifier": "t_contract$_AddressManager_$102008", "typeString": "contract AddressManager"}}, "id": 110835, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "<PERSON><PERSON><PERSON><PERSON>", "nodeType": "MemberAccess", "referencedDeclaration": 101976, "src": "7988:25:234", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_string_memory_ptr_$_t_address_$returns$__$", "typeString": "function (string memory,address) external"}}, "id": 110838, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "7988:48:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 110839, "nodeType": "ExpressionStatement", "src": "7988:48:234"}]}}, "id": 110847, "nodeType": "IfStatement", "src": "7664:569:234", "trueBody": {"id": 110822, "nodeType": "Block", "src": "7699:175:234", "statements": [{"expression": {"arguments": [{"expression": {"id": 110808, "name": "Constants", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103096, "src": "7767:9:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_Constants_$103096_$", "typeString": "type(library Constants)"}}, "id": 110809, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "PROXY_IMPLEMENTATION_ADDRESS", "nodeType": "MemberAccess", "referencedDeclaration": 103062, "src": "7767:38:234", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"arguments": [{"arguments": [{"arguments": [{"id": 110816, "name": "_implementation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110776, "src": "7831:15:234", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 110815, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "7823:7:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint160_$", "typeString": "type(uint160)"}, "typeName": {"id": 110814, "name": "uint160", "nodeType": "ElementaryTypeName", "src": "7823:7:234", "typeDescriptions": {}}}, "id": 110817, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "7823:24:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint160", "typeString": "uint160"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint160", "typeString": "uint160"}], "id": 110813, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "7815:7:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 110812, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7815:7:234", "typeDescriptions": {}}}, "id": 110818, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "7815:33:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 110811, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "7807:7:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_bytes32_$", "typeString": "type(bytes32)"}, "typeName": {"id": 110810, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "7807:7:234", "typeDescriptions": {}}}, "id": 110819, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "7807:42:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "expression": {"arguments": [{"id": 110805, "name": "_proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110774, "src": "7731:6:234", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}], "id": 110804, "name": "L1ChugSplashProxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 102516, "src": "7713:17:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_L1ChugSplashProxy_$102516_$", "typeString": "type(contract L1ChugSplashProxy)"}}, "id": 110806, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "7713:25:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_L1ChugSplashProxy_$102516", "typeString": "contract L1ChugSplashProxy"}}, "id": 110807, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "setStorage", "nodeType": "MemberAccess", "referencedDeclaration": 102378, "src": "7713:36:234", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_bytes32_$_t_bytes32_$returns$__$", "typeString": "function (bytes32,bytes32) external"}}, "id": 110820, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "7713:150:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 110821, "nodeType": "ExpressionStatement", "src": "7713:150:234"}]}}, "id": 110848, "nodeType": "IfStatement", "src": "7561:672:234", "trueBody": {"id": 110799, "nodeType": "Block", "src": "7593:65:234", "statements": [{"expression": {"arguments": [{"id": 110796, "name": "_implementation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110776, "src": "7631:15:234", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"arguments": [{"id": 110793, "name": "_proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110774, "src": "7613:6:234", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}], "id": 110792, "name": "Proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110434, "src": "7607:5:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_Proxy_$110434_$", "typeString": "type(contract Proxy)"}}, "id": 110794, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "7607:13:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_Proxy_$110434", "typeString": "contract Proxy"}}, "id": 110795, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "upgradeTo", "nodeType": "MemberAccess", "referencedDeclaration": 110266, "src": "7607:23:234", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_address_$returns$__$", "typeString": "function (address) external"}}, "id": 110797, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "7607:40:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 110798, "nodeType": "ExpressionStatement", "src": "7607:40:234"}]}}]}, "documentation": {"id": 110772, "nodeType": "StructuredDocumentation", "src": "7226:192:234", "text": "@notice Changes a proxy's implementation contract.\n @param _proxy          Address of the proxy to upgrade.\n @param _implementation Address of the new implementation address."}, "functionSelector": "99a88ec4", "implemented": true, "kind": "function", "modifiers": [{"id": 110779, "kind": "modifierInvocation", "modifierName": {"id": 110778, "name": "only<PERSON><PERSON>er", "nodeType": "IdentifierPath", "referencedDeclaration": 49354, "src": "7496:9:234"}, "nodeType": "ModifierInvocation", "src": "7496:9:234"}], "name": "upgrade", "nameLocation": "7432:7:234", "parameters": {"id": 110777, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 110774, "mutability": "mutable", "name": "_proxy", "nameLocation": "7456:6:234", "nodeType": "VariableDeclaration", "scope": 110850, "src": "7440:22:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}, "typeName": {"id": 110773, "name": "address", "nodeType": "ElementaryTypeName", "src": "7440:15:234", "stateMutability": "payable", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "visibility": "internal"}, {"constant": false, "id": 110776, "mutability": "mutable", "name": "_implementation", "nameLocation": "7472:15:234", "nodeType": "VariableDeclaration", "scope": 110850, "src": "7464:23:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 110775, "name": "address", "nodeType": "ElementaryTypeName", "src": "7464:7:234", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "7439:49:234"}, "returnParameters": {"id": 110780, "nodeType": "ParameterList", "parameters": [], "src": "7506:0:234"}, "scope": 110909, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 110908, "nodeType": "FunctionDefinition", "src": "8644:631:234", "nodes": [], "body": {"id": 110907, "nodeType": "Block", "src": "8822:453:234", "nodes": [], "statements": [{"assignments": [110864], "declarations": [{"constant": false, "id": 110864, "mutability": "mutable", "name": "ptype", "nameLocation": "8842:5:234", "nodeType": "VariableDeclaration", "scope": 110907, "src": "8832:15:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}, "typeName": {"id": 110863, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 110862, "name": "ProxyType", "nodeType": "IdentifierPath", "referencedDeclaration": 110477, "src": "8832:9:234"}, "referencedDeclaration": 110477, "src": "8832:9:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "visibility": "internal"}], "id": 110868, "initialValue": {"baseExpression": {"id": 110865, "name": "proxyType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110483, "src": "8850:9:234", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_enum$_ProxyType_$110477_$", "typeString": "mapping(address => enum ProxyAdmin.ProxyType)"}}, "id": 110867, "indexExpression": {"id": 110866, "name": "_proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110853, "src": "8860:6:234", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "8850:17:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "nodeType": "VariableDeclarationStatement", "src": "8832:35:234"}, {"condition": {"commonType": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}, "id": 110872, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 110869, "name": "ptype", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110864, "src": "8881:5:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"id": 110870, "name": "ProxyType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110477, "src": "8890:9:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_enum$_ProxyType_$110477_$", "typeString": "type(enum ProxyAdmin.ProxyType)"}}, "id": 110871, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberName": "ERC1967", "nodeType": "MemberAccess", "referencedDeclaration": 110474, "src": "8890:17:234", "typeDescriptions": {"typeIdentifier": "t_enum$_ProxyType_$110477", "typeString": "enum ProxyAdmin.ProxyType"}}, "src": "8881:26:234", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"id": 110905, "nodeType": "Block", "src": "9014:255:234", "statements": [{"expression": {"arguments": [{"id": 110886, "name": "_proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110853, "src": "9084:6:234", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, {"id": 110887, "name": "_implementation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110855, "src": "9092:15:234", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 110885, "name": "upgrade", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110850, "src": "9076:7:234", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_payable_$_t_address_$returns$__$", "typeString": "function (address payable,address)"}}, "id": 110888, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "9076:32:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 110889, "nodeType": "ExpressionStatement", "src": "9076:32:234"}, {"assignments": [110891, null], "declarations": [{"constant": false, "id": 110891, "mutability": "mutable", "name": "success", "nameLocation": "9128:7:234", "nodeType": "VariableDeclaration", "scope": 110905, "src": "9123:12:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 110890, "name": "bool", "nodeType": "ElementaryTypeName", "src": "9123:4:234", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, null], "id": 110899, "initialValue": {"arguments": [{"id": 110897, "name": "_data", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110857, "src": "9172:5:234", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 110892, "name": "_proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110853, "src": "9140:6:234", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "id": 110893, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "call", "nodeType": "MemberAccess", "src": "9140:11:234", "typeDescriptions": {"typeIdentifier": "t_function_barecall_payable$_t_bytes_memory_ptr_$returns$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "function (bytes memory) payable returns (bool,bytes memory)"}}, "id": 110896, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "names": ["value"], "nodeType": "FunctionCallOptions", "options": [{"expression": {"id": 110894, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "9160:3:234", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 110895, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "value", "nodeType": "MemberAccess", "src": "9160:9:234", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "src": "9140:31:234", "typeDescriptions": {"typeIdentifier": "t_function_barecall_payable$_t_bytes_memory_ptr_$returns$_t_bool_$_t_bytes_memory_ptr_$value", "typeString": "function (bytes memory) payable returns (bool,bytes memory)"}}, "id": 110898, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "9140:38:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "tuple(bool,bytes memory)"}}, "nodeType": "VariableDeclarationStatement", "src": "9122:56:234"}, {"expression": {"arguments": [{"id": 110901, "name": "success", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110891, "src": "9200:7:234", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "50726f787941646d696e3a2063616c6c20746f2070726f78792061667465722075706772616465206661696c6564", "id": 110902, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "9209:48:234", "typeDescriptions": {"typeIdentifier": "t_stringliteral_9dbbe4927f0b34687229d178ecf6fef1e21d5f949373ef3cb14376a90927e2f4", "typeString": "literal_string \"ProxyAdmin: call to proxy after upgrade failed\""}, "value": "ProxyAdmin: call to proxy after upgrade failed"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_9dbbe4927f0b34687229d178ecf6fef1e21d5f949373ef3cb14376a90927e2f4", "typeString": "literal_string \"ProxyAdmin: call to proxy after upgrade failed\""}], "id": 110900, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "9192:7:234", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 110903, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "9192:66:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 110904, "nodeType": "ExpressionStatement", "src": "9192:66:234"}]}, "id": 110906, "nodeType": "IfStatement", "src": "8877:392:234", "trueBody": {"id": 110884, "nodeType": "Block", "src": "8909:99:234", "statements": [{"expression": {"arguments": [{"id": 110880, "name": "_implementation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110855, "src": "8974:15:234", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 110881, "name": "_data", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110857, "src": "8991:5:234", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"arguments": [{"id": 110874, "name": "_proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110853, "src": "8929:6:234", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}], "id": 110873, "name": "Proxy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 110434, "src": "8923:5:234", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_Proxy_$110434_$", "typeString": "type(contract Proxy)"}}, "id": 110875, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "8923:13:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_Proxy_$110434", "typeString": "contract Proxy"}}, "id": 110876, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "upgradeToAndCall", "nodeType": "MemberAccess", "referencedDeclaration": 110299, "src": "8923:30:234", "typeDescriptions": {"typeIdentifier": "t_function_external_payable$_t_address_$_t_bytes_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (address,bytes memory) payable external returns (bytes memory)"}}, "id": 110879, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "names": ["value"], "nodeType": "FunctionCallOptions", "options": [{"expression": {"id": 110877, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "8962:3:234", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 110878, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "value", "nodeType": "MemberAccess", "src": "8962:9:234", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "src": "8923:50:234", "typeDescriptions": {"typeIdentifier": "t_function_external_payable$_t_address_$_t_bytes_memory_ptr_$returns$_t_bytes_memory_ptr_$value", "typeString": "function (address,bytes memory) payable external returns (bytes memory)"}}, "id": 110882, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "8923:74:234", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 110883, "nodeType": "ExpressionStatement", "src": "8923:74:234"}]}}]}, "documentation": {"id": 110851, "nodeType": "StructuredDocumentation", "src": "8245:394:234", "text": "@notice Changes a proxy's implementation contract and delegatecalls the new implementation\n         with some given data. Useful for atomic upgrade-and-initialize calls.\n @param _proxy          Address of the proxy to upgrade.\n @param _implementation Address of the new implementation address.\n @param _data           Data to trigger the new implementation with."}, "functionSelector": "9623609d", "implemented": true, "kind": "function", "modifiers": [{"id": 110860, "kind": "modifierInvocation", "modifierName": {"id": 110859, "name": "only<PERSON><PERSON>er", "nodeType": "IdentifierPath", "referencedDeclaration": 49354, "src": "8808:9:234"}, "nodeType": "ModifierInvocation", "src": "8808:9:234"}], "name": "upgradeAndCall", "nameLocation": "8653:14:234", "parameters": {"id": 110858, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 110853, "mutability": "mutable", "name": "_proxy", "nameLocation": "8693:6:234", "nodeType": "VariableDeclaration", "scope": 110908, "src": "8677:22:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}, "typeName": {"id": 110852, "name": "address", "nodeType": "ElementaryTypeName", "src": "8677:15:234", "stateMutability": "payable", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "visibility": "internal"}, {"constant": false, "id": 110855, "mutability": "mutable", "name": "_implementation", "nameLocation": "8717:15:234", "nodeType": "VariableDeclaration", "scope": 110908, "src": "8709:23:234", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 110854, "name": "address", "nodeType": "ElementaryTypeName", "src": "8709:7:234", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 110857, "mutability": "mutable", "name": "_data", "nameLocation": "8755:5:234", "nodeType": "VariableDeclaration", "scope": 110908, "src": "8742:18:234", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 110856, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "8742:5:234", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "8667:99:234"}, "returnParameters": {"id": 110861, "nodeType": "ParameterList", "parameters": [], "src": "8822:0:234"}, "scope": 110909, "stateMutability": "payable", "virtual": false, "visibility": "external"}], "abstract": false, "baseContracts": [{"baseName": {"id": 110472, "name": "Ownable", "nodeType": "IdentifierPath", "referencedDeclaration": 49435, "src": "1264:7:234"}, "id": 110473, "nodeType": "InheritanceSpecifier", "src": "1264:7:234"}], "canonicalName": "ProxyAdmin", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 110471, "nodeType": "StructuredDocumentation", "src": "928:313:234", "text": "@title ProxyAdmin\n @notice This is an auxiliary contract meant to be assigned as the admin of an ERC1967 Proxy,\n         based on the OpenZeppelin implementation. It has backwards compatibility logic to work\n         with the various types of proxies that have been deployed by Optimism in the past."}, "fullyImplemented": true, "linearizedBaseContracts": [110909, 49435, 53291], "name": "ProxyAdmin", "nameLocation": "1250:10:234", "scope": 110910, "usedErrors": []}], "license": "MIT"}, "id": 234}