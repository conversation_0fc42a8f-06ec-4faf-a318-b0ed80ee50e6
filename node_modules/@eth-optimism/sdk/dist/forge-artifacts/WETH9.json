{"abi": [{"type": "fallback", "stateMutability": "payable"}, {"type": "function", "name": "allowance", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "guy", "type": "address", "internalType": "address"}, {"name": "wad", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "deposit", "inputs": [], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "dst", "type": "address", "internalType": "address"}, {"name": "wad", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "src", "type": "address", "internalType": "address"}, {"name": "dst", "type": "address", "internalType": "address"}, {"name": "wad", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdraw", "inputs": [{"name": "wad", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "Approval", "inputs": [{"name": "src", "type": "address", "indexed": true, "internalType": "address"}, {"name": "guy", "type": "address", "indexed": true, "internalType": "address"}, {"name": "wad", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "dst", "type": "address", "indexed": true, "internalType": "address"}, {"name": "wad", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "src", "type": "address", "indexed": true, "internalType": "address"}, {"name": "dst", "type": "address", "indexed": true, "internalType": "address"}, {"name": "wad", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "<PERSON><PERSON><PERSON>", "inputs": [{"name": "src", "type": "address", "indexed": true, "internalType": "address"}, {"name": "wad", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}], "bytecode": {"object": "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", "sourceMap": "739:40:0:-;718:1809;739:40;;718:1809;739:40;;;-1:-1:-1;;;739:40:0;;;;;;-1:-1:-1;;739:40:0;;:::i;:::-;-1:-1:-1;785:31:0;;;;;;;;;;;;;-1:-1:-1;;;785:31:0;;;;;;;;;;;;:::i;:::-;-1:-1:-1;822:27:0;;;-1:-1:-1;;822:27:0;847:2;822:27;;;718:1809;5:2:-1;;;;30:1;27;20:12;5:2;718:1809:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;718:1809:0;;;-1:-1:-1;718:1809:0;:::i;:::-;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "718:1809:0:-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1289:9;:7;:9::i;:::-;718:1809;739:40;;8:9:-1;5:2;;;30:1;27;20:12;5:2;739:40:0;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8:100:-1;33:3;30:1;27:10;8:100;;;90:11;;;84:18;71:11;;;64:39;52:2;45:10;8:100;;;12:14;739:40:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1755:177;;8:9:-1;5:2;;;30:1;27;20:12;5:2;1755:177:0;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;-1:-1;1755:177:0;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;1654:95;;8:9:-1;5:2;;;30:1;27;20:12;5:2;1654:95:0;;;:::i;:::-;;;;;;;;;;;;;;;;2065:460;;8:9:-1;5:2;;;30:1;27;20:12;5:2;2065:460:0;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;-1:-1;2065:460:0;;;;;;;;;;;;;;;;;;:::i;1445:203::-;;8:9:-1;5:2;;;30:1;27;20:12;5:2;1445:203:0;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;-1:-1;1445:203:0;;:::i;822:27::-;;8:9:-1;5:2;;;30:1;27;20:12;5:2;822:27:0;;;:::i;:::-;;;;;;;;;;;;;;;;;;;1108:65;;8:9:-1;5:2;;;30:1;27;20:12;5:2;1108:65:0;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;-1:-1;1108:65:0;;;;:::i;785:31::-;;8:9:-1;5:2;;;30:1;27;20:12;5:2;785:31:0;;;:::i;1938:121::-;;8:9:-1;5:2;;;30:1;27;20:12;5:2;1938:121:0;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;-1:-1;1938:121:0;;;;;;;;;:::i;1179:65::-;;8:9:-1;5:2;;;30:1;27;20:12;5:2;1179:65:0;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;-1:-1;1179:65:0;;;;;;;;;;;:::i;1310:130::-;1364:10;1354:21;;;;:9;:21;;;;;;;;;:34;;1379:9;1354:34;;;;;;1403:30;;;;;;;;;;;;;;;;;1310:130::o;739:40::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;1755:177::-;1837:10;1811:4;1827:21;;;:9;:21;;;;;;;;;:26;;;;;;;;;;;:32;;;1874:30;;;;;;;1811:4;;1827:26;;1837:10;;1874:30;;;;;;;;-1:-1:-1;1921:4:0;1755:177;;;;:::o;1654:95::-;1721:21;1654:95;:::o;2065:460::-;2183:14;;;2155:4;2183:14;;;:9;:14;;;;;;:21;-1:-1:-1;2183:21:0;2175:30;;;;;;2220:17;;;2227:10;2220:17;;;;:59;;-1:-1:-1;2241:14:0;;;;;;;:9;:14;;;;;;;;2256:10;2241:26;;;;;;;;2276:2;2241:38;;2220:59;2216:179;;;2303:14;;;;;;;:9;:14;;;;;;;;2318:10;2303:26;;;;;;;;:33;-1:-1:-1;2303:33:0;2295:42;;;;;;2351:14;;;;;;;:9;:14;;;;;;;;2366:10;2351:26;;;;;;;:33;;;;;;;2216:179;2405:14;;;;;;;;:9;:14;;;;;;;;:21;;;;;;;2436:14;;;;;;;;;;:21;;;;;;2473:23;;;;;;;2436:14;;2473:23;;;;;;;;;;;-1:-1:-1;2514:4:0;2065:460;;;;;:::o;1445:203::-;1508:10;1498:21;;;;:9;:21;;;;;;:28;-1:-1:-1;1498:28:0;1490:37;;;;;;1547:10;1537:21;;;;:9;:21;;;;;;:28;;;;;;;1575:24;;;;;;1562:3;;1575:24;;1537:21;1575:24;1562:3;1547:10;1575:24;;;;;;;;8:9:-1;5:2;;;45:16;42:1;39;24:38;77:16;74:1;67:27;5:2;-1:-1;1614:27:0;;;;;;;;1625:10;;1614:27;;;;;;;;;;1445:203;:::o;822:27::-;;;;;;:::o;1108:65::-;;;;;;;;;;;;;:::o;785:31::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1938:121;1995:4;2018:34;2031:10;2043:3;2048;2018:12;:34::i;:::-;2011:41;1938:121;-1:-1:-1;;;1938:121:0:o;1179:65::-;;;;;;;;;;;;;;;;;;;;;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "decimals()": "313ce567", "deposit()": "d0e30db0", "name()": "06fdde03", "symbol()": "95d89b41", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd", "withdraw(uint256)": "2e1a7d4d"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.5.17+commit.d19bba13\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"src\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"guy\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"wad\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"dst\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"wad\",\"type\":\"uint256\"}],\"name\":\"Deposit\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"src\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"dst\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"wad\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"src\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"wad\",\"type\":\"uint256\"}],\"name\":\"Withdrawal\",\"type\":\"event\"},{\"payable\":true,\"stateMutability\":\"payable\",\"type\":\"fallback\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"guy\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"wad\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"deposit\",\"outputs\":[],\"payable\":true,\"stateMutability\":\"payable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"dst\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"wad\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"src\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"dst\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"wad\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"wad\",\"type\":\"uint256\"}],\"name\":\"withdraw\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"methods\":{}},\"userdoc\":{\"methods\":{}}},\"settings\":{\"compilationTarget\":{\"src/vendor/WETH9.sol\":\"WETH9\"},\"evmVersion\":\"istanbul\",\"libraries\":{},\"optimizer\":{\"enabled\":true,\"runs\":999999},\"remappings\":[\":@lib-keccak/=lib/lib-keccak/contracts/lib/\",\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":@rari-capital/solmate/=lib/solmate/\",\":@solady-test/=lib/lib-keccak/lib/solady/test/\",\":@solady/=lib/solady/src/\",\":ds-test/=lib/forge-std/lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":kontrol-cheatcodes/=lib/kontrol-cheatcodes/src/\",\":lib-keccak/=lib/lib-keccak/contracts/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":safe-contracts/=lib/safe-contracts/contracts/\",\":solady/=lib/solady/\",\":solmate/=lib/solmate/src/\"]},\"sources\":{\"src/vendor/WETH9.sol\":{\"keccak256\":\"0x5cf72b1d2b0f0a758d5540c663352aa757c80a975c2e2d9b22cc6bc9f1ada1a1\",\"urls\":[\"bzz-raw://aba380794ab2878afe942d0c093cb41820afa854182668af91bffa3f26492244\",\"dweb:/ipfs/QmTW9EWupsNX3DgoJc13uu2V3P4UPRErvaVCPmLyCbDnM1\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.5.17+commit.d19bba13"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "src", "type": "address", "indexed": true}, {"internalType": "address", "name": "guy", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "wad", "type": "uint256", "indexed": false}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "dst", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "wad", "type": "uint256", "indexed": false}], "type": "event", "name": "<PERSON><PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "src", "type": "address", "indexed": true}, {"internalType": "address", "name": "dst", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "wad", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "src", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "wad", "type": "uint256", "indexed": false}], "type": "event", "name": "<PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [], "stateMutability": "payable", "type": "fallback"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "guy", "type": "address"}, {"internalType": "uint256", "name": "wad", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [], "stateMutability": "payable", "type": "function", "name": "deposit"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "dst", "type": "address"}, {"internalType": "uint256", "name": "wad", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "src", "type": "address"}, {"internalType": "address", "name": "dst", "type": "address"}, {"internalType": "uint256", "name": "wad", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "wad", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "withdraw"}], "devdoc": {"methods": {}}, "userdoc": {"methods": {}}}, "settings": {"remappings": ["@lib-keccak/=lib/lib-keccak/contracts/lib/", "@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "@rari-capital/solmate/=lib/solmate/", "@solady-test/=lib/lib-keccak/lib/solady/test/", "@solady/=lib/solady/src/", "ds-test/=lib/forge-std/lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "kontrol-cheatcodes/=lib/kontrol-cheatcodes/src/", "lib-keccak/=lib/lib-keccak/contracts/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "safe-contracts/=lib/safe-contracts/contracts/", "solady/=lib/solady/", "solmate/=lib/solmate/src/"], "optimizer": {"enabled": true, "runs": 999999}, "compilationTarget": {"src/vendor/WETH9.sol": "WETH9"}, "evmVersion": "istanbul", "libraries": {}}, "sources": {"src/vendor/WETH9.sol": {"keccak256": "0x5cf72b1d2b0f0a758d5540c663352aa757c80a975c2e2d9b22cc6bc9f1ada1a1", "urls": ["bzz-raw://aba380794ab2878afe942d0c093cb41820afa854182668af91bffa3f26492244", "dweb:/ipfs/QmTW9EWupsNX3DgoJc13uu2V3P4UPRErvaVCPmLyCbDnM1"], "license": null}}, "version": 1}, "storageLayout": {"storage": [{"astId": 4, "contract": "src/vendor/WETH9.sol:WETH9", "label": "name", "offset": 0, "slot": "0", "type": "t_string_storage"}, {"astId": 7, "contract": "src/vendor/WETH9.sol:WETH9", "label": "symbol", "offset": 0, "slot": "1", "type": "t_string_storage"}, {"astId": 10, "contract": "src/vendor/WETH9.sol:WETH9", "label": "decimals", "offset": 0, "slot": "2", "type": "t_uint8"}, {"astId": 42, "contract": "src/vendor/WETH9.sol:WETH9", "label": "balanceOf", "offset": 0, "slot": "3", "type": "t_mapping(t_address,t_uint256)"}, {"astId": 48, "contract": "src/vendor/WETH9.sol:WETH9", "label": "allowance", "offset": 0, "slot": "4", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32", "value": "t_mapping(t_address,t_uint256)"}, "t_mapping(t_address,t_uint256)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_string_storage": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"encoding": "inplace", "label": "uint8", "numberOfBytes": "1"}}}, "userdoc": {}, "devdoc": {}, "ast": {"absolutePath": "src/vendor/WETH9.sol", "id": 246, "exportedSymbols": {"WETH9": [245]}, "nodeType": "SourceUnit", "src": "686:36998:0", "nodes": [{"id": 1, "nodeType": "PragmaDirective", "src": "686:30:0", "nodes": [], "literals": ["solidity", ">=", "0.4", ".22", "<", "0.6"]}, {"id": 245, "nodeType": "ContractDefinition", "src": "718:1809:0", "nodes": [{"id": 4, "nodeType": "VariableDeclaration", "src": "739:40:0", "nodes": [], "constant": false, "name": "name", "scope": 245, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string"}, "typeName": {"id": 2, "name": "string", "nodeType": "ElementaryTypeName", "src": "739:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"argumentTypes": null, "hexValue": "57726170706564204574686572", "id": 3, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "764:15:0", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_00cd3d46df44f2cbb950cf84eb2e92aa2ddd23195b1a009173ea59a063357ed3", "typeString": "literal_string \"Wrapped Ether\""}, "value": "Wrapped Ether"}, "visibility": "public"}, {"id": 7, "nodeType": "VariableDeclaration", "src": "785:31:0", "nodes": [], "constant": false, "name": "symbol", "scope": 245, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string"}, "typeName": {"id": 5, "name": "string", "nodeType": "ElementaryTypeName", "src": "785:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"argumentTypes": null, "hexValue": "57455448", "id": 6, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "810:6:0", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_0f8a193ff464434486c0daf7db2a895884365d2bc84ba47a68fcf89c1b14b5b8", "typeString": "literal_string \"WETH\""}, "value": "WETH"}, "visibility": "public"}, {"id": 10, "nodeType": "VariableDeclaration", "src": "822:27:0", "nodes": [], "constant": false, "name": "decimals", "scope": 245, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 8, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "822:5:0", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "value": {"argumentTypes": null, "hexValue": "3138", "id": 9, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "847:2:0", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_18_by_1", "typeString": "int_const 18"}, "value": "18"}, "visibility": "public"}, {"id": 18, "nodeType": "EventDefinition", "src": "856:68:0", "nodes": [], "anonymous": false, "documentation": null, "name": "Approval", "parameters": {"id": 17, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 12, "indexed": true, "name": "src", "nodeType": "VariableDeclaration", "scope": 18, "src": "872:19:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 11, "name": "address", "nodeType": "ElementaryTypeName", "src": "872:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 14, "indexed": true, "name": "guy", "nodeType": "VariableDeclaration", "scope": 18, "src": "893:19:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 13, "name": "address", "nodeType": "ElementaryTypeName", "src": "893:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 16, "indexed": false, "name": "wad", "nodeType": "VariableDeclaration", "scope": 18, "src": "914:8:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 15, "name": "uint", "nodeType": "ElementaryTypeName", "src": "914:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "871:52:0"}}, {"id": 26, "nodeType": "EventDefinition", "src": "929:68:0", "nodes": [], "anonymous": false, "documentation": null, "name": "Transfer", "parameters": {"id": 25, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 20, "indexed": true, "name": "src", "nodeType": "VariableDeclaration", "scope": 26, "src": "945:19:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 19, "name": "address", "nodeType": "ElementaryTypeName", "src": "945:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 22, "indexed": true, "name": "dst", "nodeType": "VariableDeclaration", "scope": 26, "src": "966:19:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 21, "name": "address", "nodeType": "ElementaryTypeName", "src": "966:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 24, "indexed": false, "name": "wad", "nodeType": "VariableDeclaration", "scope": 26, "src": "987:8:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 23, "name": "uint", "nodeType": "ElementaryTypeName", "src": "987:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "944:52:0"}}, {"id": 32, "nodeType": "EventDefinition", "src": "1002:46:0", "nodes": [], "anonymous": false, "documentation": null, "name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": {"id": 31, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 28, "indexed": true, "name": "dst", "nodeType": "VariableDeclaration", "scope": 32, "src": "1017:19:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 27, "name": "address", "nodeType": "ElementaryTypeName", "src": "1017:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 30, "indexed": false, "name": "wad", "nodeType": "VariableDeclaration", "scope": 32, "src": "1038:8:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 29, "name": "uint", "nodeType": "ElementaryTypeName", "src": "1038:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1016:31:0"}}, {"id": 38, "nodeType": "EventDefinition", "src": "1053:49:0", "nodes": [], "anonymous": false, "documentation": null, "name": "<PERSON><PERSON><PERSON>", "parameters": {"id": 37, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 34, "indexed": true, "name": "src", "nodeType": "VariableDeclaration", "scope": 38, "src": "1071:19:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 33, "name": "address", "nodeType": "ElementaryTypeName", "src": "1071:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 36, "indexed": false, "name": "wad", "nodeType": "VariableDeclaration", "scope": 38, "src": "1092:8:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 35, "name": "uint", "nodeType": "ElementaryTypeName", "src": "1092:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1070:31:0"}}, {"id": 42, "nodeType": "VariableDeclaration", "src": "1108:65:0", "nodes": [], "constant": false, "name": "balanceOf", "scope": 245, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "typeName": {"id": 41, "keyType": {"id": 39, "name": "address", "nodeType": "ElementaryTypeName", "src": "1117:7:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "1108:25:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "valueType": {"id": 40, "name": "uint", "nodeType": "ElementaryTypeName", "src": "1128:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}, "value": null, "visibility": "public"}, {"id": 48, "nodeType": "VariableDeclaration", "src": "1179:65:0", "nodes": [], "constant": false, "name": "allowance", "scope": 245, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}, "typeName": {"id": 47, "keyType": {"id": 43, "name": "address", "nodeType": "ElementaryTypeName", "src": "1188:7:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "1179:46:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}, "valueType": {"id": 46, "keyType": {"id": 44, "name": "address", "nodeType": "ElementaryTypeName", "src": "1208:7:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "1199:25:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "valueType": {"id": 45, "name": "uint", "nodeType": "ElementaryTypeName", "src": "1219:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}}, "value": null, "visibility": "public"}, {"id": 55, "nodeType": "FunctionDefinition", "src": "1251:54:0", "nodes": [], "body": {"id": 54, "nodeType": "Block", "src": "1279:26:0", "nodes": [], "statements": [{"expression": {"argumentTypes": null, "arguments": [], "expression": {"argumentTypes": [], "id": 51, "name": "deposit", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 74, "src": "1289:7:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 52, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1289:9:0", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 53, "nodeType": "ExpressionStatement", "src": "1289:9:0"}]}, "documentation": null, "implemented": true, "kind": "fallback", "modifiers": [], "name": "", "parameters": {"id": 49, "nodeType": "ParameterList", "parameters": [], "src": "1259:2:0"}, "returnParameters": {"id": 50, "nodeType": "ParameterList", "parameters": [], "src": "1279:0:0"}, "scope": 245, "stateMutability": "payable", "superFunction": null, "visibility": "external"}, {"id": 74, "nodeType": "FunctionDefinition", "src": "1310:130:0", "nodes": [], "body": {"id": 73, "nodeType": "Block", "src": "1344:96:0", "nodes": [], "statements": [{"expression": {"argumentTypes": null, "id": 64, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 58, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42, "src": "1354:9:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 61, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 59, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 260, "src": "1364:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 60, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1364:10:0", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1354:21:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "+=", "rightHandSide": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 62, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 260, "src": "1379:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 63, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "value", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1379:9:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1354:34:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 65, "nodeType": "ExpressionStatement", "src": "1354:34:0"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "expression": {"argumentTypes": null, "id": 67, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 260, "src": "1411:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 68, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1411:10:0", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 69, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 260, "src": "1423:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 70, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "value", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1423:9:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 66, "name": "<PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 32, "src": "1403:7:0", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,uint256)"}}, "id": 71, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1403:30:0", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 72, "nodeType": "EmitStatement", "src": "1398:35:0"}]}, "documentation": null, "implemented": true, "kind": "function", "modifiers": [], "name": "deposit", "parameters": {"id": 56, "nodeType": "ParameterList", "parameters": [], "src": "1326:2:0"}, "returnParameters": {"id": 57, "nodeType": "ParameterList", "parameters": [], "src": "1344:0:0"}, "scope": 245, "stateMutability": "payable", "superFunction": null, "visibility": "public"}, {"id": 110, "nodeType": "FunctionDefinition", "src": "1445:203:0", "nodes": [], "body": {"id": 109, "nodeType": "Block", "src": "1480:168:0", "nodes": [], "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 85, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 80, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42, "src": "1498:9:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 83, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 81, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 260, "src": "1508:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 82, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1508:10:0", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1498:21:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"argumentTypes": null, "id": 84, "name": "wad", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 76, "src": "1523:3:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1498:28:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 79, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [263, 264], "referencedDeclaration": 263, "src": "1490:7:0", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 86, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1490:37:0", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 87, "nodeType": "ExpressionStatement", "src": "1490:37:0"}, {"expression": {"argumentTypes": null, "id": 93, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 88, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42, "src": "1537:9:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 91, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 89, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 260, "src": "1547:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 90, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1547:10:0", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1537:21:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "-=", "rightHandSide": {"argumentTypes": null, "id": 92, "name": "wad", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 76, "src": "1562:3:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1537:28:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 94, "nodeType": "ExpressionStatement", "src": "1537:28:0"}, {"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 100, "name": "wad", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 76, "src": "1595:3:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 95, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 260, "src": "1575:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 98, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1575:10:0", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "id": 99, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "transfer", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1575:19:0", "typeDescriptions": {"typeIdentifier": "t_function_transfer_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256)"}}, "id": 101, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1575:24:0", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 102, "nodeType": "ExpressionStatement", "src": "1575:24:0"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "expression": {"argumentTypes": null, "id": 104, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 260, "src": "1625:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 105, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1625:10:0", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, {"argumentTypes": null, "id": 106, "name": "wad", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 76, "src": "1637:3:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 103, "name": "<PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 38, "src": "1614:10:0", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,uint256)"}}, "id": 107, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1614:27:0", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 108, "nodeType": "EmitStatement", "src": "1609:32:0"}]}, "documentation": null, "implemented": true, "kind": "function", "modifiers": [], "name": "withdraw", "parameters": {"id": 77, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 76, "name": "wad", "nodeType": "VariableDeclaration", "scope": 110, "src": "1463:8:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 75, "name": "uint", "nodeType": "ElementaryTypeName", "src": "1463:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1462:10:0"}, "returnParameters": {"id": 78, "nodeType": "ParameterList", "parameters": [], "src": "1480:0:0"}, "scope": 245, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"id": 121, "nodeType": "FunctionDefinition", "src": "1654:95:0", "nodes": [], "body": {"id": 120, "nodeType": "Block", "src": "1704:45:0", "nodes": [], "statements": [{"expression": {"argumentTypes": null, "expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 116, "name": "this", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 274, "src": "1729:4:0", "typeDescriptions": {"typeIdentifier": "t_contract$_WETH9_$245", "typeString": "contract WETH9"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_WETH9_$245", "typeString": "contract WETH9"}], "id": 115, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1721:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": "address"}, "id": 117, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1721:13:0", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "id": 118, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "balance", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1721:21:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 114, "id": 119, "nodeType": "Return", "src": "1714:28:0"}]}, "documentation": null, "implemented": true, "kind": "function", "modifiers": [], "name": "totalSupply", "parameters": {"id": 111, "nodeType": "ParameterList", "parameters": [], "src": "1674:2:0"}, "returnParameters": {"id": 114, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 113, "name": "", "nodeType": "VariableDeclaration", "scope": 121, "src": "1698:4:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 112, "name": "uint", "nodeType": "ElementaryTypeName", "src": "1698:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1697:6:0"}, "scope": 245, "stateMutability": "view", "superFunction": null, "visibility": "public"}, {"id": 149, "nodeType": "FunctionDefinition", "src": "1755:177:0", "nodes": [], "body": {"id": 148, "nodeType": "Block", "src": "1817:115:0", "nodes": [], "statements": [{"expression": {"argumentTypes": null, "id": 137, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 130, "name": "allowance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 48, "src": "1827:9:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 134, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 131, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 260, "src": "1837:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 132, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1837:10:0", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1827:21:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 135, "indexExpression": {"argumentTypes": null, "id": 133, "name": "guy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 123, "src": "1849:3:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1827:26:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "id": 136, "name": "wad", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 125, "src": "1856:3:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1827:32:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 138, "nodeType": "ExpressionStatement", "src": "1827:32:0"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "expression": {"argumentTypes": null, "id": 140, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 260, "src": "1883:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 141, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1883:10:0", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, {"argumentTypes": null, "id": 142, "name": "guy", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 123, "src": "1895:3:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 143, "name": "wad", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 125, "src": "1900:3:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 139, "name": "Approval", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 18, "src": "1874:8:0", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 144, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1874:30:0", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 145, "nodeType": "EmitStatement", "src": "1869:35:0"}, {"expression": {"argumentTypes": null, "hexValue": "74727565", "id": 146, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "1921:4:0", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 129, "id": 147, "nodeType": "Return", "src": "1914:11:0"}]}, "documentation": null, "implemented": true, "kind": "function", "modifiers": [], "name": "approve", "parameters": {"id": 126, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 123, "name": "guy", "nodeType": "VariableDeclaration", "scope": 149, "src": "1772:11:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 122, "name": "address", "nodeType": "ElementaryTypeName", "src": "1772:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 125, "name": "wad", "nodeType": "VariableDeclaration", "scope": 149, "src": "1785:8:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 124, "name": "uint", "nodeType": "ElementaryTypeName", "src": "1785:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1771:23:0"}, "returnParameters": {"id": 129, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 128, "name": "", "nodeType": "VariableDeclaration", "scope": 149, "src": "1811:4:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 127, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1811:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": null, "visibility": "internal"}], "src": "1810:6:0"}, "scope": 245, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"id": 166, "nodeType": "FunctionDefinition", "src": "1938:121:0", "nodes": [], "body": {"id": 165, "nodeType": "Block", "src": "2001:58:0", "nodes": [], "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "expression": {"argumentTypes": null, "id": 159, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 260, "src": "2031:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 160, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "2031:10:0", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, {"argumentTypes": null, "id": 161, "name": "dst", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 151, "src": "2043:3:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 162, "name": "wad", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 153, "src": "2048:3:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 158, "name": "transferFrom", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 244, "src": "2018:12:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$_t_bool_$", "typeString": "function (address,address,uint256) returns (bool)"}}, "id": 163, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2018:34:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 157, "id": 164, "nodeType": "Return", "src": "2011:41:0"}]}, "documentation": null, "implemented": true, "kind": "function", "modifiers": [], "name": "transfer", "parameters": {"id": 154, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 151, "name": "dst", "nodeType": "VariableDeclaration", "scope": 166, "src": "1956:11:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 150, "name": "address", "nodeType": "ElementaryTypeName", "src": "1956:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 153, "name": "wad", "nodeType": "VariableDeclaration", "scope": 166, "src": "1969:8:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 152, "name": "uint", "nodeType": "ElementaryTypeName", "src": "1969:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1955:23:0"}, "returnParameters": {"id": 157, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 156, "name": "", "nodeType": "VariableDeclaration", "scope": 166, "src": "1995:4:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 155, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1995:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": null, "visibility": "internal"}], "src": "1994:6:0"}, "scope": 245, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"id": 244, "nodeType": "FunctionDefinition", "src": "2065:460:0", "nodes": [], "body": {"id": 243, "nodeType": "Block", "src": "2165:360:0", "nodes": [], "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 182, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 178, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42, "src": "2183:9:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 180, "indexExpression": {"argumentTypes": null, "id": 179, "name": "src", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 168, "src": "2193:3:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2183:14:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"argumentTypes": null, "id": 181, "name": "wad", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 172, "src": "2201:3:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2183:21:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 177, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [263, 264], "referencedDeclaration": 263, "src": "2175:7:0", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 183, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2175:30:0", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 184, "nodeType": "ExpressionStatement", "src": "2175:30:0"}, {"condition": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 200, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 188, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 185, "name": "src", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 168, "src": "2220:3:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 186, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 260, "src": "2227:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 187, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "2227:10:0", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "src": "2220:17:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 199, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 189, "name": "allowance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 48, "src": "2241:9:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 191, "indexExpression": {"argumentTypes": null, "id": 190, "name": "src", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 168, "src": "2251:3:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2241:14:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 194, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 192, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 260, "src": "2256:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 193, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "2256:10:0", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2241:26:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 197, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "-", "prefix": true, "src": "2276:2:0", "subExpression": {"argumentTypes": null, "hexValue": "31", "id": 196, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2277:1:0", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "typeDescriptions": {"typeIdentifier": "t_rational_minus_1_by_1", "typeString": "int_const -1"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_minus_1_by_1", "typeString": "int_const -1"}], "id": 195, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2271:4:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": "uint"}, "id": 198, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2271:8:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2241:38:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "2220:59:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": null, "id": 222, "nodeType": "IfStatement", "src": "2216:179:0", "trueBody": {"id": 221, "nodeType": "Block", "src": "2281:114:0", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 209, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 202, "name": "allowance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 48, "src": "2303:9:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 204, "indexExpression": {"argumentTypes": null, "id": 203, "name": "src", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 168, "src": "2313:3:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2303:14:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 207, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 205, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 260, "src": "2318:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 206, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "2318:10:0", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2303:26:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"argumentTypes": null, "id": 208, "name": "wad", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 172, "src": "2333:3:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2303:33:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 201, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [263, 264], "referencedDeclaration": 263, "src": "2295:7:0", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 210, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2295:42:0", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 211, "nodeType": "ExpressionStatement", "src": "2295:42:0"}, {"expression": {"argumentTypes": null, "id": 219, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 212, "name": "allowance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 48, "src": "2351:9:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 216, "indexExpression": {"argumentTypes": null, "id": 213, "name": "src", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 168, "src": "2361:3:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2351:14:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 217, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 214, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 260, "src": "2366:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 215, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "2366:10:0", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2351:26:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "-=", "rightHandSide": {"argumentTypes": null, "id": 218, "name": "wad", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 172, "src": "2381:3:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2351:33:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 220, "nodeType": "ExpressionStatement", "src": "2351:33:0"}]}}, {"expression": {"argumentTypes": null, "id": 227, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 223, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42, "src": "2405:9:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 225, "indexExpression": {"argumentTypes": null, "id": 224, "name": "src", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 168, "src": "2415:3:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2405:14:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "-=", "rightHandSide": {"argumentTypes": null, "id": 226, "name": "wad", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 172, "src": "2423:3:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2405:21:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 228, "nodeType": "ExpressionStatement", "src": "2405:21:0"}, {"expression": {"argumentTypes": null, "id": 233, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 229, "name": "balanceOf", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 42, "src": "2436:9:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 231, "indexExpression": {"argumentTypes": null, "id": 230, "name": "dst", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 170, "src": "2446:3:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2436:14:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "+=", "rightHandSide": {"argumentTypes": null, "id": 232, "name": "wad", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 172, "src": "2454:3:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2436:21:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 234, "nodeType": "ExpressionStatement", "src": "2436:21:0"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 236, "name": "src", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 168, "src": "2482:3:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 237, "name": "dst", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 170, "src": "2487:3:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 238, "name": "wad", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 172, "src": "2492:3:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 235, "name": "Transfer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 26, "src": "2473:8:0", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 239, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2473:23:0", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 240, "nodeType": "EmitStatement", "src": "2468:28:0"}, {"expression": {"argumentTypes": null, "hexValue": "74727565", "id": 241, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "2514:4:0", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 176, "id": 242, "nodeType": "Return", "src": "2507:11:0"}]}, "documentation": null, "implemented": true, "kind": "function", "modifiers": [], "name": "transferFrom", "parameters": {"id": 173, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 168, "name": "src", "nodeType": "VariableDeclaration", "scope": 244, "src": "2087:11:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 167, "name": "address", "nodeType": "ElementaryTypeName", "src": "2087:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 170, "name": "dst", "nodeType": "VariableDeclaration", "scope": 244, "src": "2100:11:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 169, "name": "address", "nodeType": "ElementaryTypeName", "src": "2100:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 172, "name": "wad", "nodeType": "VariableDeclaration", "scope": 244, "src": "2113:8:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 171, "name": "uint", "nodeType": "ElementaryTypeName", "src": "2113:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "2086:36:0"}, "returnParameters": {"id": 176, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 175, "name": "", "nodeType": "VariableDeclaration", "scope": 244, "src": "2155:4:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 174, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2155:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": null, "visibility": "internal"}], "src": "2154:6:0"}, "scope": 245, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}], "baseContracts": [], "contractDependencies": [], "contractKind": "contract", "documentation": null, "fullyImplemented": true, "linearizedBaseContracts": [245], "name": "WETH9", "scope": 246}]}, "id": 0}