"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ECOBridgeAdapter = void 0;
const core_utils_1 = require("@eth-optimism/core-utils");
const ethers_1 = require("ethers");
const coercion_1 = require("../utils/coercion");
const standard_bridge_1 = require("./standard-bridge");
class ECOBridgeAdapter extends standard_bridge_1.StandardBridgeAdapter {
    async supportsTokenPair(l1Token, l2Token) {
        const l1Bridge = new ethers_1.Contract(this.l1Bridge.address, [
            {
                inputs: [],
                name: 'l1Eco',
                outputs: [
                    {
                        internalType: 'address',
                        name: '',
                        type: 'address',
                    },
                ],
                stateMutability: 'view',
                type: 'function',
            },
        ], this.messenger.l1Provider);
        const l2Bridge = new ethers_1.Contract(this.l2Bridge.address, [
            {
                inputs: [],
                name: 'l2Eco',
                outputs: [
                    {
                        internalType: 'contract L2ECO',
                        name: '',
                        type: 'address',
                    },
                ],
                stateMutability: 'view',
                type: 'function',
            },
        ], this.messenger.l2Provider);
        const [remoteL1Token, remoteL2Token] = await Promise.all([
            l1Bridge.l1Eco(),
            l2Bridge.l2Eco(),
        ]);
        if (!(0, core_utils_1.hexStringEquals)(remoteL1Token, (0, coercion_1.toAddress)(l1Token))) {
            return false;
        }
        if (!(0, core_utils_1.hexStringEquals)(remoteL2Token, (0, coercion_1.toAddress)(l2Token))) {
            return false;
        }
        return true;
    }
}
exports.ECOBridgeAdapter = ECOBridgeAdapter;
//# sourceMappingURL=eco-bridge.js.map